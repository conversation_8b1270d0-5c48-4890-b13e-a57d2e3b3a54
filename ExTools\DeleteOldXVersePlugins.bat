setlocal enabledelayedexpansion
@echo off
cd ..
cd Engine\Plugins
set EnginePluginDir=%cd%
set EngineXVersePluginDir=%EnginePluginDir%\XVerse
echo EnginePluginDir=%EnginePluginDir%
echo EngineXVersePluginDir=%EngineXVersePluginDir%


if  exist "%EngineXVersePluginDir%" (
	echo DeleteOldXVersePlugins  Xverse %EngineXVersePluginDir%
    rd /S/Q %EngineXVersePluginDir%
)


for /D %%i in (%EnginePluginDir%\*) do ( 
rem echo %%i
set PDir=%%i
echo PDir = !PDir!
set FPath=!PDir!\Plugin.xverse
rem echo FPath=!FPath!
if  exist "!FPath!" (
	echo DeleteOldXVersePlugins  Xverse !PDir!
    rd /S/Q !PDir!
)
)

set XversePlugins=XBaseLib;XverseWater;XCOMM;Protobuf;XverseArtChecker;XUE;Prefabricator;MegascansPlugin;XDataRateChecker;XverseAvatar;KawaiiPhysics;XVerseEngine;DTWebBrowser;XverseTurboEditing
:loop
rem echo loop %XversePlugins%
for /f "tokens=1* delims=;" %%a  in ("!XversePlugins!") do ( 
    echo XversePlugins =%%a 
    set PluginPath=%EnginePluginDir%\%%a 
    echo PluginPath =!PluginPath! 
    rd /S/Q !PluginPath! 
    set XversePlugins=%%b
)

if defined XversePlugins (
    goto :loop
) else (
    goto :exit
)

:exit
echo exit...
pause