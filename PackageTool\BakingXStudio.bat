@chcp 65001>nul
@cls
@echo off
rem add hosts System32/drivers/etc/hosts  below
rem **********		CreatorSamba

rem cmdkey /add:CreatorSamba /USER:**********\xverse_creator /Pass:xverse_creator >nul
rem net use \\CreatorSamba /USER:**********\xverse_creator xverse_creator /PERSISTENT:YES >nul

pushd %~dp0
cd ..
set ProjectDir=%cd%
set ProjectName=XStudio
echo ProjectDir=%ProjectDir%
call :GetEngineDir
call :BuildProject
call :BakingXStudio
goto :Exit

:BuildProject
rem -nocompileeditor
echo start BuildProject...
cd %ProjectDir%
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat -ScriptsForProject=%ProjectDir%\%ProjectName%.uproject BuildCookRun -project=%ProjectDir%\%ProjectName%.uproject -targetplatform=Win64 -clientconfig=Development -ue4exe=UE4Editor-Cmd.exe -noP4 -iterate -prereqs -build -CrashReporter -ForceDebugInfo -utf8output -compressed -FastCook
goto :eof

:BakingXStudio
echo start Baking ....
set BakingCmd=%EngineDir%\Engine\Binaries\Win64\UE4Editor.exe %ProjectDir%\%ProjectName%.uproject -RenderOffScreen --- z.ConvertToUMap D:\XVerseCode\Baking\0815_disco.xvprj /Game/Maps 0815_disco true ---z.RequestExitWithStatus true 0
call %BakingCmd%
goto :eof


:GetEngineDir
set "EngineDir="
set "SearchEngineDir="
set "DefineEngineDir="
::set "DefineEngineDir=D:\UEEngineWork\UEBuildOutPut\XVerseEngine-20220812-100933"
echo Start search Engine...
set "EngineSearchDir=D:\UEEngineWork\UEBuildOutPut"
cd %EngineSearchDir%
for /f "tokens=4 delims= " %%a in ('dir /AD XVerseEngine-* ^| findstr XVerseEngine') do set "SearchEngineDir=%%a"
echo Search SearchEngineDir=%SearchEngineDir%
if "%DefineEngineDir%"=="" (
set "EngineDir=%EngineSearchDir%\%SearchEngineDir%"
) else (
set "EngineDir=%DefineEngineDir%"
)
echo Use EngineDir=%EngineDir%
goto :eof

:Exit
pause
goto :eof