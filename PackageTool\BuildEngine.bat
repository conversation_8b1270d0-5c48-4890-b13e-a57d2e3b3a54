setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
@echo off

rem add hosts System32/drivers/etc/hosts  below
rem **********		CreatorSamba

rem cmdkey /add:CreatorSamba /USER:**********\xverse_creator /Pass:xverse_creator >nul
rem net use \\CreatorSamba /USER:**********\xverse_creator xverse_creator /PERSISTENT:YES >nul
cd ..
cd PackageTool
set PackageToolDir=%cd%
set EngineDir=C:\work\xverse\UnrealEngine
set EngineOutPutDir=C:\work\xverse\EngineOutput
set RemoteEngineUploadPath=\\CreatorSamba\XverseCreator\XCreatorEngine
set ProjectBasehVersion=1.0
set "WithAndroid=false"
set "WithIOS=false"
set "WithWin64=false"
set "WithLinux=false"
set "EmbedSymStore=true"
set "EmbedUpload=true"
set "ResetLocalBranch=false"
set GameConfigurations=DebugGame;Development;Shipping
set ProjectName=XVerseEngine
set Remarks=nil
set AllParam=%*
set CurrentParam=%AllParam%
set "CurrentParamIndex=1"
set "ForceExit=false"
set BuildBranchName=xstudio
echo all: %AllParam%
call :ReadParams
call :GetCurrentTimeYMDHMS
call :CreateDateDir
call :SwitchBranch
if "%ForceExit%"=="true" (
	echo BuildEngine SwitchBranch Error
 goto :Exit
)
call :GetBranchVersion
call :PrintLog
call :BuildEngine
if not exist "%OutputWin64Dir%\Engine\Binaries" (
	echo BuildEngine Error
	goto :Exit
)
call :WriteCommitInfo
call :CopyConfigs
call :CopyRuntimeDll
call :UploadEngine
goto :Exit


:ReadParams
echo Start ReadParams... 
rem ./BuildEngine.bat -engineDir=D:/Engine -packageToolDir=d:/ -engineOutputDir=D:/EngineOuput -withWin64=false -withLinux=false -withAndroid=false -withIOS=false -gameConfigurations="DebugGame;Development;Shipping" -embedSymStore=true -embedUpload=true
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	echo param.!CurrentParam!
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam%!") do ( 
		echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam%!") do ( 
			echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-engineDir" (
			    set EngineDir=!Value!
			)
			if "!Key!"=="-packageToolDir" (
			    set PackageToolDir=!Value!
			)
			if "!Key!"=="-engineOutputDir" (
			    set EngineOutPutDir=!Value!
			)
			
			if "!Key!"=="-withAndroid" (
			    set WithAndroid=!Value!
			)
			if "!Key!"=="-withWin64" (
			    set WithWin64=!Value!
			)
			
			if "!Key!"=="-withLinux" (
			    set WithLinux=!Value!
			)
			
			if "!Key!"=="-withIOS" (
			    set WithIOS=!Value!
			)
			
			if "!Key!"=="-gameConfigurations" (
			    set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-embedSymStore" (
				set EmbedSymStore=!Value!
			)
			
			if "!Key!"=="-embedUpload" (
				set EmbedUpload=!Value!
			)
			
			if "!Key!"=="-branchName" (
				set BuildBranchName=!Value!
			)

			if "!Key!"=="-resetLocalBranch" (
				set ResetLocalBranch=!Value!
			)

			if "!Key!"=="-remoteEngineUploadPath" (
				set RemoteEngineUploadPath=!Value!
			)
			
			if "!Key!"=="-remarks" (
				set Remarks=!Value!
			)
		)

		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams

:BuildEngine
echo Start BuildEngine...
rem start buildEngine
set BuildEngineCmd=%EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildGraph -target="Make Installed Build Win64" -script="%PackageToolDir%\InstalledEngineBuild.xml" -set:WithWin64=%WithWin64% -set:WithWin32=false -set:WithAndroid=%WithAndroid% -set:WithFullDebugInfo=true -set:WithMac=false -set:WithIOS=%WithIOS% -set:WithTVOS=false -set:WithLinux=%WithLinux% -set:WithLinuxAArch64=false -set:WithDDC=false -set:WithLumin=false -set:WithHoloLens=false -set:VS2019=true -set:GameConfigurations=%GameConfigurations% -set:EmbedSymStore=%EmbedSymStore% -set:LocalInstalledDirOverride=%OutputWin64Dir%
echo BuildEngineCmd=%BuildEngineCmd%
call %BuildEngineCmd%
goto :eof

:WriteCommitInfo
echo Start WriteCommitInfo...
rem save last commit info
call :GetEngineLastestCommitMessage
mkdir %OutputWin64Dir%\Engine\Version
echo Engine:%EngineLastestVer% > %OutputWin64Dir%\Engine\Version\EngineVersion.ini
echo BuildTime:%CurrentTimeYMDHMS% >> %OutputWin64Dir%\Engine\Version\EngineVersion.ini
echo Version:%ProjectBranch%-%ProjectBasehVersion%.%ProjectBranchVersion% >> %OutputWin64Dir%\Engine\Version\EngineVersion.ini
echo Remarks:%Remarks% >> %OutputWin64Dir%\Engine\Version\EngineVersion.ini
echo Branch:%BuildBranchName% >> %OutputWin64Dir%\Engine\Version\EngineVersion.ini
echo CommitId:%EngineLastestVer% >> %OutputWin64Dir%\Engine\Version\EngineVersion.ini
echo CommitVersion:%ProjectBranchVersion% >> %OutputWin64Dir%\Engine\Version\EngineVersion.ini
echo CommitInfo:%Remarks% >> %OutputWin64Dir%\Engine\Version\EngineVersion.ini

echo Branch:%BuildBranchName% > %OutputWin64Dir%\Engine\Version\EngineBranch.ini
echo Branch:%BuildBranchName% > %OutputWin64Dir%\Engine\Version\EngineInstalled.ini



rem copy launch engine script
copy %PackageToolDir%\MountSambaCache.bat %OutputWin64Dir%\
echo call MountSambaCache.bat >%OutputWin64Dir%\UnrealEngine.bat
echo start %%~dp0Engine\Binaries\Win64\UE4Editor.exe >>%OutputWin64Dir%\UnrealEngine.bat
::rmdir /S /Q %OutputWin64Dir%\Engine\Plugins\Experimental\MeshModelingToolset

::copy MenuRegisterTool
mkdir %OutputWin64Dir%\Launcher
copy %PackageToolDir%\UnrealProjectMenuRegistration.cmd %OutputWin64Dir%\UnrealProjectMenuRegistration.cmd
copy %PackageToolDir%\UnrealVersionSelector.exe %OutputWin64Dir%\Launcher\UnrealVersionSelector.exe
echo copy MenuRegister tool to Engine
goto :eof

:CopyConfigs
echo BuildEngine CopyConfigs...
copy %PackageToolDir%\Config\BaseEngine.ini %OutputWin64Dir%\Engine\Config\BaseEngine.ini
goto :eof

:CopyRuntimeDll
echo BuildPluginEngine Start CopyRuntimeDll..
set RuntimeDllPath=%OutputWin64Dir%\Engine\Binaries\ThirdParty\AppLocalDependencies\Win64\Microsoft.VC.CRT
mkdir %OutputWin64Dir%\Engine\Binaries\Win64\
robocopy /E %RuntimeDllPath% %OutputWin64Dir%\Engine\Binaries\Win64\
goto :eof

:UploadEngine
rem upload zip
echo Start UploadEngine...%EmbedUpload%
set FixedBranch=%BuildBranchName:/=_%
if "!EmbedUpload!"=="true" (
echo UploadEngine Uploading...
cd %EngineOutPutDir%
bz c %OutputWin64FileName% %OutputWin64Dir%\Engine %OutputWin64Dir%\FeaturePacks %OutputWin64Dir%\Launcher %OutputWin64Dir%\Samples %OutputWin64Dir%\Launcher %OutputWin64Dir%\Templates %OutputWin64Dir%\UnrealEngine.bat %OutputWin64Dir%\MountSambaCache.bat %OutputWin64Dir%\UnrealProjectMenuRegistration.cmd
mkdir %RemoteEngineUploadPath%\%FixedBranch%
copy  %OutputWin64FileName% %RemoteEngineUploadPath%\%FixedBranch%\

echo "UploadEngine Success %RemoteEngineUploadPath%\%FixedBranch%\%OutputWin64FileName%"
)

goto :eof


:CreateDateDir
echo Start CreateDateDir...
set FixedBrnachName=%BuildBranchName:/=_%
set "OutputWin64Dir=%EngineOutPutDir%\%ProjectName%-%CurrentTimeYMDHMS%"
setx CreatorOutputEnv %OutputWin64Dir%
set OutputWin64FileName=%ProjectName%-%CurrentTimeYMDHMS%.zip
goto :eof

:PrintLog
echo Start PrintLog...
echo EngineDir=%EngineDir%
echo EngineOutPutDir=%EngineOutPutDir%
echo OutputWin64Dir=%OutputWin64Dir%
echo PackageToolDir=%PackageToolDir%
echo ProjectBranch=%ProjectBranch%
echo ProjectBasehVersion=%ProjectBasehVersion%
echo ProjectBranchVersion=%ProjectBranchVersion%
echo EngineLastestVer=%EngineLastestVer%
goto :eof

:GetEngineLastestCommitMessage
cd %EngineDir%
set Command=git log -1 --oneline

FOR /F "delims= " %%A IN ('%Command%') DO (
    SET EngineLastestVer=%%A
)
goto :eof

:GetCurrentTimeYMDHMS
echo BuildPEngine Start GetCurrentTimeYMDHMS..
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do set TmpTimestamp=%%a
for /f "tokens=* delims= " %%a in ("%TmpTimestamp%") do (
        set "CurrentTimeYMDHMS=%%a"
    )
goto :eof

:GetCurrentTimeYMD
echo BuildPEngine Start GetCurrentTime..
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd'"') do set TmpTimestamp=%%a
for /f "tokens=* delims= " %%a in ("%TmpTimestamp%") do (
        set "CurrentTimeYMD=%%a"
    )
goto :eof

:GetCurrentBranch
echo BuildPEngine GetCurrentBranch... 
cd %EngineDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:SwitchBranch
echo BuildPEngine SwitchBranch... 
call :GetCurrentBranch
echo BuildBranchName=%BuildBranchName% CurrentBranch=%ProjectBranch%, BuildBranchName=%BuildBranchName%
if "%BuildBranchName%"=="" (
  set "BuildBranchName=%ProjectBranch%"
)
echo BuildPEngine SwitchBranch Use BranchName=%BuildBranchName%
cd %ProjectDir%
if %BuildBranchName%==%ProjectBranch% (
echo BuildPEngine No Need SwitchBranch(Current Branch Is %BuildBranchName%)
) else (
git fetch
git checkout %BuildBranchName%
)
call :GetCurrentBranch
if %BuildBranchName%==%ProjectBranch% (
echo BuildPEngine SwitchBranch Success %BuildBranchName%
if "%ResetLocalBranch%"=="true" (
	echo BuildPEngine Start Git Reset Local Cache...
	git reset --hard origin/%BuildBranchName%
)
git pull
git log -2
) else (
echo SwitchBranch Fail OldBranch=%ProjectBranch%, %BuildBranchName%
set "ForceExit=true"
)
goto :eof

:GetBranchVersion
echo BuildPEngine GetBranchVersion...
cd %EngineDir%
set BranchVersionCommand=git rev-list --count --first-parent %BuildBranchName%
echo BuildPEngine BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
SET ProjectBranchVersion=%%A
)
goto :eof

:Exit
echo BuildPEngine Start Exit...
pause
goto :eof

:ExitFail
echo BuildPEngine Start ExitFail...
exit /b 1
