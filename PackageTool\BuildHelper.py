#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import os
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
def searchEngineDirCmd(engineSearchDir, branch = "xstudio", outPutPath = None):
    print("BuildHelper searchEngineDirCmd", engineSearchDir, branch, outPutPath)
    engineDir = searchEngineDir(engineSearchDir, branch)
    if outPutPath is None:
        return
    if os.path.exists(outPutPath):
        os.remove(outPutPath)
    if len(engineDir) > 0:
        outFile = open(outPutPath, "w")
        outFile.writelines(engineDir)
        outFile.close()
def searchEngineDir(engineSearchDir, branch, bLatest, engineDirPrefix = "XVerseEngine-"):
    print("BuildHelper searchEngineDir", engineSearchDir, branch)
    if not os.path.exists(engineSearchDir):
        return ""
    file_list = os.listdir(engineSearchDir)
    if bLatest:
        file_list.sort(reverse=True)

    engineDir = ""
    for f in file_list:
        fpath = os.path.join(engineSearchDir, f)
        if len(engineDirPrefix) > 0 and not f.startswith(engineDirPrefix):
            continue
        if os.path.isdir(fpath):
            print("BuildHelper searchEngineDir ",fpath)
            workStatePath = os.path.join(fpath, "Engine\Version\EngineNoWork.ini")
            if os.path.exists(workStatePath):
                print("BuildHelper engine  unavailable",workStatePath)
                continue
            engineInstalledPath = os.path.join(fpath, "Engine\Version\EngineInstalled.ini")
            if not os.path.exists(engineInstalledPath):
                print("BuildHelper engine  unavailable",engineInstalledPath)
                continue
            uatPath = os.path.join(fpath, "Engine\Build\BatchFiles\RunUAT.bat")
            if not os.path.exists(uatPath):
                print("BuildHelper engine  uat unavailable",uatPath)
                continue
            print("BuildHelper found installed engine", fpath)
            engineVersionPath = os.path.join(fpath, "Engine\Version\EngineVersion.ini")
            engineVersionFile = open(engineVersionPath, "r" , encoding="utf-8")
            for line in engineVersionFile:
                if line.startswith("Branch"):
                    print("BuildHelper engineVersionFileLine", engineVersionPath, line)
                    lines = line.split(":")
                    if len(lines) > 1:
                        fileBranch = lines[1]
                        fileBranch = fileBranch.strip()
                        print("BuildHelper engineVersionFileLine FileBranch", fileBranch, branch)
                        if fileBranch == branch:
                            engineDir = fpath
                            break
            if len(engineDir) > 0:
                break
    if len(engineDir) < 2:
        print("BuildHelper engine not found")
    return engineDir

def GetEngineCommitInfo(EngineDir, OutPutPath):
    print("BuildHelper GetEngineCommitInfo", EngineDir, OutPutPath)
    engineVersionPath = os.path.join(EngineDir, "Engine\Version\EngineVersion.ini")
    engineVersionFile = open(engineVersionPath, "r" , encoding="utf-8")
    for line in engineVersionFile:
        if line.startswith("Engine"):
            print("BuildHelper engineVersionFileLine", engineVersionPath, line)
            lines = line.split(":")
            if len(lines) > 1:
                engineCommitId = lines[1]
                outFile = open(OutPutPath, "w")
                outFile.writelines(engineCommitId)
                outFile.close()
if __name__ == "__main__":
    #print("Start SearchEngine",sys.argv)
    InAction = "searchEngine"
    if len(sys.argv) < 1:
        print("error Argv Len")
        exit(1)
    if len(sys.argv) > 1:
        InAction = sys.argv[1]
    if InAction == "searchEngine":
        engineSearchDir = "C:\\work\\xverse\\EngineOutput"
        branch = "dev_mrq_optim2"
        outputDir = None
        if len(sys.argv) > 2:
            engineSearchDir = sys.argv[2]
        if len(sys.argv) > 3:
            branch = sys.argv[3]
        if len(sys.argv) > 4:
            outputDir = sys.argv[4]
        searchEngineDirCmd(engineSearchDir, branch, outputDir)
    elif InAction == "getEngineCommitInfo":
        GetEngineCommitInfo(sys.argv[2], sys.argv[3])
    else:
        print("error InAction")