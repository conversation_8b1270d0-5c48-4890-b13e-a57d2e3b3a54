setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
@echo off
pushd %~dp0
cd ../
set PackageToolDir=%cd%\PackageTool
cd ../
set ProjectDir=C:\work\xverse\XVerseStudio
cd %PackageToolDir%
set ProjectOutput=C:\work\xverse\ProjectOutput
set ProjectBaseVersion=1.0
set OutPutNamePrefix=XStudioPIE
set ProjectName=XStudio
set PIEName=PIE
set TargetPlatform=Win64
set GameConfigurations=Development
set "EngineDir="
set "EngineName="
set "DefineEngineDir="
set EngineSearchDir=C:\work\xverse\EngineOutput
set "OutPutPluginEngineDir="
set TranlateType=Dev
set ProjectBranch=dev
set "BuildBranchName=dev"
set "ForceExit=false"
set "DeleteOldCache=true"
set "ResetLocalBranch=false"
set "EngineBranch=xstudio"
set AllParam=%*
set CurrentParam=%AllParam%
set "CurrentParamIndex=1"
echo BuildPIE Receive Params: %AllParam%
echo BuildPIE Start ReadParams...

call :ReadParams
call :GetCurrentTimeYMD
call :GetCurrentTimeYMDHMS
call :GetCurrentBranch
call :SwitchBranch
if "%ForceExit%"=="true" (
 goto :Exit
)
call :GetEngineVersionInfo
call :GetProjectBranchVersion
call :GetProjectCommitInfo
call :GetEngineDir
call :GetEngineName
call :SetEnvConfigInfo
call :PrintInfo
call :DeleteOldProjectCache
call :SetProjectFile
call :CopyEngine
if "%ForceExit%"=="true" (
 goto :Exit
)
call :BuildProject
echo %TargetPlatform%| findstr Linux >nul && (
	if not exist "%ProjectDir%\Binaries\Linux\%ProjectName%.sym" (
		echo BuildProject did exist project with Linux
		goto :Exit
	)
)

echo %TargetPlatform%| findstr Win64 >nul && (
	if not exist "%ProjectDir%\Binaries\Win64\%ProjectName%.lib" (
		echo BuildProject did exist project with Win64
		goto :Exit
	)
)
call :GenXVerseProjectConfig
call :CopyProjectToPIE
call :CopyConfigToPIE
call :CopyCommitInfoToPIE
call :CompressePIE
call :UploadPIE
goto :Exit


:ReadParams
rem echo Start ReadParams...
rem ./BuildPIE.bat -deleteOldCache=false -tranlateType=Dev -engineDir=d:/ -engineSearchDir=d:/engine -gameConfigurations=Development -outPutDir=d:/
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
rem echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	echo CurrentParamContent.!CurrentParam!
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam!") do ( 
		echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam!") do ( 
			echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-projectName" (
			    set ProjectName=!Value!
			)
			
			if "!Key!"=="-projectDir" (
			    set ProjectDir=!Value!
			)
			
			if "!Key!"=="-packageToolDir" (
			    set PackageToolDir=!Value!
			)
			
			if "!Key!"=="-targetPlatform" (
			    set TargetPlatform=!Value!
			)
			
			if "!Key!"=="-tranlateType" (
			    set TranlateType=!Value!
			)
			
			if "!Key!"=="-deleteOldCache" (
				set DeleteOldCache=!Value!
			)
			
			if "!Key!"=="-engineDir" (
				set DefineEngineDir=!Value!
			)
			
			if "!Key!"=="-engineSearchDir" (
				set EngineSearchDir=!Value!
			)
			if "!Key!"=="-gameConfigurations" (
				set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-outPutDir" (
				set ProjectOutput=!Value!
			)			
			
			if "!Key!"=="-autoUpload" (
				set AutoUpload=!Value!
			)		
			
			if "!Key!"=="-branchName" (
				set BuildBranchName=!Value!
			)

			if "!Key!"=="-engineBranch" (
				set EngineBranch=!Value!
			)
						
		    if "!Key!"=="-resetLocalBranch" (
				set ResetLocalBranch=!Value!
			)
		)
		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams

:PrintInfo
echo BuildPIE Start PrintInfo..
echo CurrentTimeYMD=%CurrentTimeYMD%
echo CurrentTimeYMDHMS=%CurrentTimeYMDHMS%
echo DefineEngineDir=%DefineEngineDir%
echo EngineDir=%EngineDir%
echo EngineName=%EngineName%
echo PackageToolDir=%PackageToolDir%
echo OutPutPIECompileTargetPath=%OutPutPIECompileTargetPath%
echo OutPutPIEProjectCompileTargetPath=%OutPutPIEProjectCompileTargetPath%
echo OutPutPIEEngineZipPath=%OutPutPIEEngineZipPath%
echo ProjectOutput=%ProjectOutput%
echo ProjectBranch=%ProjectBranch%
echo TranlateType=%TranlateType%
echo CommitId=%CommitId%
echo CommitVersion=%CommitInfo%
echo ProjectBaseVersion=%ProjectBaseVersion%
echo ProjectBranchVersion=%ProjectBranchVersion%
goto :eof


:SetEnvConfigInfo
echo BuildPIE Start SetEnvConfigInfo..
cd %ProjectOutput%
set ProjectTargetDirName=%OutPutNamePrefix%-%CurrentTimeYMDHMS%
set OutPutPIEEngineZipName=%ProjectTargetDirName%.zip
set OutPutPIECompileTargetPath=%ProjectOutput%\%ProjectTargetDirName%
set OutPutPIEEngineZipPath=%OutPutPIECompileTargetPath%\%OutPutPIEEngineZipName%
set OutPutPIEProjectCompileTargetPath=%OutPutPIECompileTargetPath%\%ProjectName%
RD /S/Q %OutPutPIEProjectCompileTargetPath%
RD /S/Q %OutPutPIECompileTargetPath%
mkdir %OutPutPIEProjectCompileTargetPath%
goto :eof

:CopyEngine
echo BuildPIE Start CopyEngine..
set EngineZipFilePath=%EngineDir%.zip

if exist %EngineDir% (
echo BuildPIE CopyEngine exist Engine Dir %EngineDir%
) else (
echo BuildPIE CopyEngine not exist Engine Dir
set "ForceExit=true"
goto :Exit
)
if exist %EngineZipFilePath% (
echo BuildPIE CopyEngine already exist zip engine dir %EngineZipFilePath%
) else (
echo BuildPIE CopyEngine Start Compresse EngineDir
bz c %EngineZipFilePath% %EngineDir%
)
mkdir %ProjectOutput%
echo BuildPIE delete projectOutPut EngineZipFile
del %OutPutPIEEngineZipPath%
echo BuildPIE CopyEngine Start Copy Engine zip to OutPutDir=%OutPutPIEEngineZipPath%
copy  %EngineZipFilePath% %OutPutPIEEngineZipPath%
goto :eof



:SetProjectFile
echo BuildPIE start SetProjectFile...
cd %ProjectDir%
::remove old files
del %ProjectDir%\Source\*.Target.cs
del %ProjectDir%\Source\XVerse\*.cs
del %ProjectDir%\Source\XVerse\*.cpp
RD /S/Q %ProjectDir%\Config\Windows
RD /S/Q %ProjectDir%\Config\Linux
RD /S/Q %ProjectDir%\Config\Android
::copy project files
echo BuildPIE start copy uproject=%ProjectDir%\Script\Projects\%ProjectName%\%ProjectName%.uproject to %ProjectDir%\
xcopy /E/H/Y %ProjectDir%\Script\Projects\%ProjectName%\* %ProjectDir%\
::copy %ProjectDir%\Script\Projects\%ProjectName%\%ProjectName%.uproject  %ProjectDir%\
::copy %ProjectDir%\Script\Projects\%ProjectName%\Source\*.Target.cs %ProjectDir%\Source\
::copy %ProjectDir%\Script\Projects\%ProjectName%\Source\XVerse\*.cs %ProjectDir%\Source\XVerse\
goto :eof

:DeleteOldProjectCache
echo BuildPIE Start DeleteOldProjectCache Flag=%DeleteOldCache%
if "%DeleteOldCache%"=="true" (
echo Start DeleteOldProjectCache...
cd %ProjectDir%
for /f "tokens=*" %%a in ('dir /s /b /ad Plugins') do (
        if "%%~nxa"=="Intermediate" (  
            @echo remove %%a
			rd /q /s "%%a"
        )
)

rd /q /s "%ProjectDir%\Intermediate\Build\BuildRules"

git clean -f -x -d
)

goto :eof

:BuildProject
rem build XStudio Project
echo BuildPIE Start Build XStudio Project.....
rem -nocompileeditor 
rem -archive -archivedirectory=%OutputWin64Dir%
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat -ScriptsForProject=%ProjectDir%\%ProjectName%.uproject BuildCookRun -project=%ProjectDir%\%ProjectName%.uproject -targetplatform=%TargetPlatform% -clientconfig=%GameConfigurations% -ue4exe=UE4Editor-Cmd.exe -noP4 -iterate -cook -pak -package -stage -prereqs -build -CrashReporter -ForceDebugInfo -utf8output -compressed -FastCook
goto :eof

:CopyProjectToPIE
echo BuildPIE Start CopyProjectToPIE....
cd %ProjectDir%

robocopy /E Binaries %OutPutPIEProjectCompileTargetPath%\Binaries\
robocopy /E Build %OutPutPIEProjectCompileTargetPath%\Build\
robocopy /E Config %OutPutPIEProjectCompileTargetPath%\Config\
robocopy /E Content %OutPutPIEProjectCompileTargetPath%\Content\
robocopy /E Plugins %OutPutPIEProjectCompileTargetPath%\Plugins\
goto :eof

:CopyConfigToPIE
echo BuildPIE Start CopyConfigToPIE....
cd %ProjectOutput%
copy %ProjectDir%\%ProjectName%.uproject %OutPutPIEProjectCompileTargetPath%\%ProjectName%.uproject
mkdir %OutPutPIECompileTargetPath%\Engine\Saved\Swarm
copy %PackageToolDir%\SwarmAgent.Options.xml %OutPutPIECompileTargetPath%\Engine\Saved\Swarm\SwarmAgent.Options.xml

mkdir %OutPutPIEProjectCompileTargetPath%\Saved\Config\WindowsNoEditor
if "%TranlateType%"=="Dev" (
	copy  %PackageToolDir%\BaseXConsole_Dev.ini %OutPutPIEProjectCompileTargetPath%\Saved\Config\WindowsNoEditor\XConsole.ini
)

if "%TranlateType%"=="Release" (
	copy  %PackageToolDir%\BaseXConsole.ini %OutPutPIEProjectCompileTargetPath%\Saved\Config\WindowsNoEditor\XConsole.ini
)
mkdir %OutPutPIEProjectCompileTargetPath%\Plugins\XUE\Config
echo [/Script/XverseAssetUploader.LocalSrcVersion]> %OutPutPIEProjectCompileTargetPath%\Plugins\XUE\Config\DefaultLocalSrcVersion.ini
echo SourceVersion=%CommitVersion%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XUE\Config\DefaultLocalSrcVersion.ini
echo SourceCommitId=%CommitId%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XUE\Config\DefaultLocalSrcVersion.ini
goto :eof

:CopyCommitInfoToPIE
echo BuildPIE Start CopyCommitInfoToPIE....
set FixedProjectBranch=%ProjectBranch:/=_%
echo Version=%FixedProjectBranch%_%TranlateType%_%ProjectBaseVersion%.%CommitVersion% > %OutPutPIEProjectCompileTargetPath%\Saved\XStudioVersion.ini
echo InnerVersion=%CommitVersion% >> %OutPutPIEProjectCompileTargetPath%\Saved\XStudioVersion.ini
goto :eof

:GenXVerseProjectConfig
echo BuildProject Start GenXVerseProjectConfig..

echo [/Script/ProjectItem.XVerseProjectConfigMng]> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo ProjectName=%PIEName%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo BuildTime=%CurrentTimeYMDHMS%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo ProjectVersion=%ProjectBaseVersion%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo ConsoleEnv=%TranlateType%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo PackageType=%GameConfigurations%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo Branch=%BuildBranchName%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CommitId=%CommitId%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CommitVersion=%CommitVersion%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CommitInfo="%CommitInfo%">> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CapturePath=%XCaptureIdePath%>> %OutPutPIEProjectCompileTargetPath%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
goto :eof

:RemoveSourceDir
rem Delete Source Files
rem rd /s /q %DestEngineDir%\Source
rem rd /s /q %TargetDir%\Source
for /f "tokens=*" %%a in ('dir /s /b /ad "%OutPutPIECompileTargetPath%\Engine\Plugins"') do (
  if "%%~nxa" equ "Source" (  
  @echo "remove %%~nxa in %%~dpa
  rd /s /q "%%a"
 )
)
for /f "tokens=*" %%a in ('dir /s /b /ad "%OutPutPIEProjectCompileTargetPath%\Plugins"') do (
  if "%%~nxa" equ "Source" (  
  @echo "remove %%~nxa in %%~dpa
  rd /s /q "%%a"
 )
)
goto :eof

:CompressePIE
echo BuildPIE Start CompressePIE...
cd %OutPutPIECompileTargetPath%
bz a %OutPutPIEEngineZipName% Engine
bz a %OutPutPIEEngineZipName% %ProjectName%
goto :eof

:UploadPIE
echo BuildPIE Start UploadPIE...
cd %OutPutPIECompileTargetPath%
set FixedProjectBranch=%ProjectBranch:/=_%
set RemotePIEDirPath=\\CreatorSamba\XverseCreator\%ProjectName%\%PIEName%\%ProjectName%-%CurrentTimeYMDHMS%-%FixedProjectBranch%
set RemotePIEFileName=%OutPutNamePrefix%-%TranlateType%.zip
set RemotePIEFilePath=%RemotePIEDirPath%\%RemotePIEFileName%
mkdir %RemotePIEDirPath%
copy  %OutPutPIEEngineZipName% %RemotePIEFilePath%
 
echo "UploadPIE Success %RemotePIEFilePath%
goto :eof

:GetEngineDir
echo BuildPIE GetEngineDir...
set "SearchEngineDir="
cd %EngineSearchDir%
set GetEngineCmd=python %PackageToolDir%\BuildHelper.py searchEngine %EngineSearchDir% %EngineBranch% %PackageToolDir%\Cache\SearchEngine.txt
echo BuildProject GetEngineDir GetEngineCmd = %GetEngineCmd%
call %GetEngineCmd% 
for /f "eol=* tokens=*" %%i in (%PackageToolDir%\Cache\SearchEngine.txt) do (
    set SearchEngineDir=%%i
)
if "%DefineEngineDir%"=="" (
set "EngineDir=%SearchEngineDir%"
) else (
set "EngineDir=%DefineEngineDir%"
)

goto :eof

:GetEngineName
echo BuildPIE GetEngineName...
cd %EngineDir%
for %%i in ("%cd%") do (
  echo current dir=%%~ni
  set EngineName=%%~ni
)
goto :eof

:GetBuildTime
echo BuildPIE Start GetBuildTime...
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do (
	echo CurrentTime=%%a
	set CurrentTime=%%a 
)
for /f %%a in ("%CurrentTime%") do set YSTD=%%a
goto :eof

:GetCurrentTimeYMDHMS
echo BuildPIE Start GetCurrentTimeYMDHMS..
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do set TmpTimestamp=%%a
for /f "tokens=* delims= " %%a in ("%TmpTimestamp%") do (
        set "CurrentTimeYMDHMS=%%a"
    )
goto :eof

:GetCurrentTimeYMD
echo BuildPIE Start GetCurrentTime..
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd'"') do set TmpTimestamp=%%a
for /f "tokens=* delims= " %%a in ("%TmpTimestamp%") do (
        set "CurrentTimeYMD=%%a"
    )
goto :eof

:CreateDateDir
echo BuildProject Start CreateDateDir...
if "%ProjectOutputName%"=="" (
	set ProjectOutputName=%ProjectName%-%CurrentTimeYMDHMS%

)
set "OutputWin64Dir=%ProjectOutput%\%ProjectOutputName%"
mkdir %OutputWin64Dir%
setx CreatorOutputEnv %OutputWin64Dir%
goto :eof



:GetCurrentBranch
rem echo BuildProject GetCurrentBranch... 
cd %ProjectDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:SwitchBranch
echo BuildProject SwitchBranch... 
echo BuildProject SwitchBranch CurrentBranch=%ProjectBranch%,BuildBranchName=%BuildBranchName%
if "%BuildBranchName%"=="" (
  set "BuildBranchName=%ProjectBranch%"
)
echo BuildProject SwitchBranch Use BranchName=%BuildBranchName%
cd %ProjectDir%
if %BuildBranchName%==%ProjectBranch% (
echo BuildProject No Need SwitchBranch(Current Branch Is %BuildBranchName%)
) else (
git fetch
git checkout %BuildBranchName%
)
call :GetCurrentBranch
echo BuildBranchName=%BuildBranchName% CurrentBranch=%ProjectBranch%
if %BuildBranchName%==%ProjectBranch% (
echo BuildProject SwitchBranch Success %BuildBranchName%
git reset --hard origin/%BuildBranchName%
git pull
git log -2
) else (
echo SwitchBranch Fail OldBranch=%ProjectBranch%, %BuildBranchName%
set "ForceExit=true"
)
goto :eof

:GetProjectBranchVersion
echo BuildProject GetBranchVersion...
cd %ProjectDir%
set BranchVersionCommand=git rev-list --count --first-parent %ProjectBranch%
echo BuildProject BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
SET CommitVersion=%%A
)
goto :eof

:GetProjectCommitInfo
echo BuildProject GetProjectCommitInfo=%ProjectDir%
cd %ProjectDir%
set Command=git log -1 --oneline

FOR /F "delims= " %%A IN ('%Command%') DO (
    SET CommitId=%%A
)
FOR /F "delims=" %%A IN ('%Command%') DO (
    set CommitInfo=%%A
)
goto :eof

:GetEngineVersionInfo
echo BuildPIE GetEngineVersionInfo=%EngineDir%
set EngineInstalledFile=%EngineDir%\Engine\Build\InstalledBuild.txt
if exist %EngineInstalledFile% (
echo Installed Engine no need get commit info
goto :eof
) 

cd %EngineDir%
set Command=git log -1 --oneline

FOR /F "delims=" %%A IN ('%Command%') DO (
SET EngineCommitVersion=%%A
GOTO :eof  
)

:Exit
echo BuildPIE Exit...
pause
goto :eof

:ExitFail
echo BuildPIE ExitFail...
exit /b 1