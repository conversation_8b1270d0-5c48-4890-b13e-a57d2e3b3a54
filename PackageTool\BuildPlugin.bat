
setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
@echo off
rem add hosts System32/drivers/etc/hosts  below
rem **********		CreatorSamba

rem cmdkey /add:CreatorSamba /USER:**********\xverse_creator /Pass:xverse_creator >nul
rem net use \\CreatorSamba /USER:**********\xverse_creator xverse_creator /PERSISTENT:YES >nul
pushd %~dp0
cd ../
set PackageToolDir=%cd%\PackageTool
cd ../
set ProjectDir=C:\work\xverse\XVerseStudio
cd %PackageToolDir%
set ProjectOutput=C:\work\xverse\ProjectOutput
set OutPutName=XStudioPluginEngine
set PluginEngineName=PluginEngine
set ProjectBaseVersion=1.0
set "EngineDir="
set "EngineName="
set "DefineEngineDir="
set EngineSearchDir=C:\work\xverse\EngineOutput
set TranlateType=Release
set ProjectBranch=dev
set "BuildBranchName=dev"
set "ForceExit=false"
set "DeleteOldCache=true"
set "ResetLocalBranch=false"
set "EnablePullCode=false"
set "EngineBranch=xstudio"
set CommitId=0
set CommitVersion=0
set CommitInfo=0
set CapturePath=null
set AllParam=%*
set CurrentParam=%AllParam%
set "CurrentParamIndex=1"
echo BuildPlugin Receive Params: %AllParam%
echo BuildPlugin Start ReadParams...
call :ReadParams
call :GetCurrentTimeYMD
call :GetCurrentTimeYMDHMS
call :GetCurrentBranch
call :SwitchBranch
if "%ForceExit%"=="true" (
 goto :ExitFail
)
call :GetBranchVersion
call :GetProjectCommitInfo
call :GetEngineDir
call :GetEngineName
call :SetEnvConfigInfo
call :PrintInfo
rem call :CopyEngine

:: remove old info
rem call :RemoveCache
call :RemovePluginCache

::compile
call :CompilePlugin

::copy
call :CopyPlugin


:: copy config files
rem call :CopyCommitInfo
rem call :CopyConfig
rem call :CopyTools
rem call :GenXVerseProjectConfig


::upload

goto :Exit


:ReadParams
rem echo Start ReadParams...
rem ./BuildPlugin.bat -deleteOldCache=false -tranlateType=Dev -engineDir=d:/ -engineSearchDir=d:/engine -gameConfigurations=Development -outPutDir=d:/ -outPutName=xstudio-123"
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	echo CurrentParamContent.!CurrentParam!
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam!") do ( 
		echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam!") do ( 
			echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-projectName" (
			    set ProjectName=!Value!
			)
			
			if "!Key!"=="-projectDir" (
			    set ProjectDir=!Value!
			)
			
			if "!Key!"=="-packageToolDir" (
			    set PackageToolDir=!Value!
			)
			
			if "!Key!"=="-targetPlatform" (
			    set TargetPlatform=!Value!
			)
			
			if "!Key!"=="-tranlateType" (
			    set TranlateType=!Value!
			)
			
			if "!Key!"=="-deleteOldCache" (
				set DeleteOldCache=!Value!
			)
			
			if "!Key!"=="-engineDir" (
				set DefineEngineDir=!Value!
			)
			
			if "!Key!"=="-engineSearchDir" (
				set EngineSearchDir=!Value!
			)
			if "!Key!"=="-gameConfigurations" (
				set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-outPutDir" (
				set ProjectOutput=!Value!
			)
			
			if "!Key!"=="-outPutName" (
				set OutPutName=!Value!
			)
			
			if "!Key!"=="-autoUpload" (
				set AutoUpload=!Value!
			)
			
			if "!Key!"=="-embedExit" (
				set EmbedExit=!Value!
			)
			
			if "!Key!"=="-exRawParam" (
				set ExRawParam=!CurrentParam:~12!
			)
			
			if "!Key!"=="-branchName" (
				set BuildBranchName=!Value!
			)

			if "!Key!"=="-engineBranch" (
				set EngineBranch=!Value!
			)
			
			if "!Key!"=="-pluginName" (
				set PluginName=!Value!
			)
			
			if "!Key!"=="-pluginPath" (
				set PluginPath=!Value!
			)

			if "!Key!"=="-enablePullCode" (
				set EnablePullCode=!Value!
			)
			
		    if "!Key!"=="-resetLocalBranch" (
				set ResetLocalBranch=!Value!
			)
		)
		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams

set remain=%str%
:ReadDeps
for /f "tokens=1* delims=;" %%a in ("%remain%") do (
	::输出第一个分段(令牌)
	echo %%a
	rem 将截取剩下的部分赋给变量remain，其实这里可以使用延迟变量开关
	set remain=%%b
)
::如果还有剩余,则继续分割
if defined remain goto :ReadDeps

:PrintInfo
echo BuildPlugin Start PrintInfo..
echo CurrentTimeYMD=%CurrentTimeYMD%
echo CurrentTimeYMDHMS=%CurrentTimeYMDHMS%
echo DefineEngineDir=%DefineEngineDir%
echo EngineDir=%EngineDir%
echo EngineName=%EngineName%
echo ToolsCacheDir=%ToolsCacheDir%
echo TranlateType=%TranlateType%
echo ProjectOutput=%ProjectOutput%
echo PlugincCompileTargetDir=%PlugincCompileTargetDir%
echo OutPutPluginEngineZipPath=%OutPutPluginEngineZipPath%
echo OutPutPluginEngineZipName=%OutPutPluginEngineZipName%
echo OutPutProjectDirName=%OutPutProjectDirName%
echo PackageToolDir=%PackageToolDir%
echo CommitInfo=%CommitId%, %CommitVersion%, %CommitInfo%
echo EnablePullCode=%EnablePullCode%
goto :eof

:SetEnvConfigInfo
echo BuildPlugin Start SetEnvConfigInfo..
cd %ProjectOutput%
set ToolsCacheDir=%ProjectOutput%\XversePluginTools
set OutPutProjectDirName=%OutPutName%-%CurrentTimeYMDHMS%
set OutPutPluginEngineZipName=%OutPutProjectDirName%.zip
set OutPutPluginEngineZipPath=%ProjectOutput%\%OutPutProjectDirName%\%OutPutPluginEngineZipName%
set PlugincCompileTargetDir=%ProjectOutput%\%OutPutProjectDirName%
rmdir /S /Q %ToolsCacheDir%
mkdir %ProjectOutput%
del %OutPutPluginEngineZipPath%
rd /s/q %PlugincCompileTargetDir%
mkdir %PlugincCompileTargetDir%
goto :eof

:DeleteOldProjectCache
echo BuildPlugin Start DeleteOldProjectCache Flag=%DeleteOldCache%
if "%DeleteOldCache%"=="true" (
echo Start DeleteOldProjectCache...
cd %ProjectDir%
for /f "tokens=*" %%a in ('dir /s /b /ad Plugins') do (
        if "%%~nxa"=="Intermediate" (  
            @echo remove %%a
			rd /q /s "%%a"
        )
)

rd /q /s "%cd%Intermediate\Build\BuildRules"
echo "BuildPlugin git clean start"
git clean -f -x -d
)

goto :eof

:CopyEngine
echo BuildPlugin Start CopyEngine..%EngineDir%
set ZipEngineDir=%EngineDir%.zip

if exist %EngineDir% (
echo BuildPlugin CopyEngine exist Engine Dir %EngineDir%
) else (
echo BuildPlugin CopyEngine not exist Engine Dir
goto :Exit
)
if exist %ZipEngineDir% (
echo BuildPlugin CopyEngine already exist zip engine dir %ZipEngineDir%
) else (
bz c %ZipEngineDir% %EngineDir%
)

echo BuildPlugin CopyEngine Start Copy Engine zip to OutPutDir=%OutPutPluginEngineZipPath%
copy  %ZipEngineDir% %OutPutPluginEngineZipPath%
goto :eof

:CompilePlugin
echo BuildPlugin Start CompilePlugin...
set CompilePluginCmd=%EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%PluginPath%\%PluginName%.uplugin -CppStd=Cpp17 -Package=%ToolsCacheDir%\%PluginName% -VS2019 -NoDeleteHostProject -TargetPlatforms=Win64

goto :eof

:RemoveCache
echo BuildPlugin RemoveCache...
cd %ProjectDir%
git clean -dxf
goto :eof

:RemovePlugin
echo BuildPlugin RemovePlugin...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\%PluginName%
rd /s/q %PluginPath%\Binaries
rd /s/q %PluginPath%\Intermediate
goto :eof

:CopyPlugin
echo BuildPlugin CopyPlugin...
robocopy /E %ToolsCacheDir%\XBaseLib\HostProject\Plugins\XBaseLib %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\
copy  %PackageToolDir%\Plugins\XBaseLib.uplugin %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\XBaseLib.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Source
goto :eof

:CopyConfig
echo BuildPlugin Start CopyConfig..%TranlateType%
mkdir %PlugincCompileTargetDir%\Engine\Config
rd /s/q %PlugincCompileTargetDir%\Engine\Config\BaseXConsole.ini
if "%TranlateType%"=="Dev" (
	copy  %PackageToolDir%\BaseXConsole_Dev.ini %PlugincCompileTargetDir%\Engine\Config\BaseXConsole.ini
)

if "%TranlateType%"=="Release" (
	copy  %PackageToolDir%\BaseXConsole.ini %PlugincCompileTargetDir%\Engine\Config\BaseXConsole.ini
)
rd /s/q %PlugincCompileTargetDir%\Engine\Config\BaseXSkinSambaCache.ini
copy  %PackageToolDir%\BaseXSkinSambaCache.ini %PlugincCompileTargetDir%\Engine\Config\BaseXSkinSambaCache.ini
goto :eof

::git commit info
:CopyCommitInfo
echo BuildPlugin Start CopyCommitInfo..
echo "XversePluginTools:"%CommitId% >%PlugincCompileTargetDir%\XversePluginToolsVersion.ini
echo "Branch:"%BuildBranchName% >> %PlugincCompileTargetDir%\XversePluginToolsVersion.ini
echo "BranchVersion:"%CommitVersion% >> %PlugincCompileTargetDir%\XversePluginToolsVersion.ini
echo "BuildTime:"%CurrentTimeYMDHMS% >> %PlugincCompileTargetDir%\XversePluginToolsVersion.ini

goto :eof

::copy lowpoly tools
:CopyTools
echo BuildPlugin Start CopyTools..
rd /s/q %PlugincCompileTargetDir%\Engine\lowpolyApplication
set DestLowpolyToolPath=\\CreatorSamba\XverseCreator\lowpolyApplication
robocopy /E %DestLowpolyToolPath% %PlugincCompileTargetDir%\Engine\lowpolyApplication\
goto :eof

:CompresseProject
echo BuildPlugin CompresseProject..

cd %PlugincCompileTargetDir%
bz d %OutPutPluginEngineZipName% Engine\Plugins\Experimental\ModelingToolsEditorMode\ModelingToolsEditorMode.uplugin
bz a %OutPutPluginEngineZipName% Engine
bz a %OutPutPluginEngineZipName% XversePluginToolsVersion.ini
goto :eof

:UploadEngine
echo BuildPlugin Start UploadEngine..
cd %PlugincCompileTargetDir%
set FixedProjectBranch=%ProjectBranch:/=_%
set RemotePluginEngineDirName=%OutPutName%-%CurrentTimeYMDHMS%
set RemotePluginEngineFileName=%OutPutName%-%TranlateType%.zip
set UploadDirPath=\\CreatorSamba\XverseCreator\XStudio\%PluginEngineName%\%RemotePluginEngineDirName%-%FixedProjectBranch%
set UploadFilePath=%UploadDirPath%\%RemotePluginEngineFileName%
mkdir %UploadDirPath%
copy  %OutPutPluginEngineZipName% %UploadFilePath%
echo "UploadEngine Success %UploadFilePath%"
goto :eof


:GetCurrentBranch
rem echo BuildPlugin GetCurrentBranch
cd %ProjectDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:SwitchBranch
echo BuildPlugin SwitchBranch...
echo BuildPlugin SwitchBranch CurrentBranch=%ProjectBranch%,BuildBranchName=%BuildBranchName%
if "%BuildBranchName%"=="" (
set "BuildBranchName=%ProjectBranch%"
)
echo BuildPlugin SwitchBranch Use BranchName=%BuildBranchName%
cd %ProjectDir%
if %BuildBranchName%==%ProjectBranch% (
echo BuildProject No Need SwitchBranch
) else (
echo Start CheckOut %BuildBranchName%
git fetch
git checkout %BuildBranchName%
)
call :GetCurrentBranch
echo BuildPlugin BuildBranchName=%BuildBranchName% CurrentBranch=%ProjectBranch% EnablePullCode=%EnablePullCode%
if %BuildBranchName%==%ProjectBranch% (
echo BuildPlugin SwitchBranch Success %BuildBranchName%
if "%EnablePullCode%"=="true" (
git reset --hard origin/%BuildBranchName%
git pull
git log -2
)
) else (
echo SwitchBranch Fail OldBranch=%ProjectBranch%, %BuildBranchName%
set "ForceExit=true"
)

goto :eof

:GetBranchVersion
cd %ProjectDir%
set BranchVersionCommand=git rev-list --count --first-parent %ProjectBranch%
echo BuildProject BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
SET CommitVersion=%%A
)
goto :eof

:GetProjectCommitInfo
echo BuildProject GetProjectCommitInfo=%ProjectDir%
cd %ProjectDir%
set Command=git log -1 --oneline

FOR /F "delims= " %%A IN ('%Command%') DO (
    SET CommitId=%%A
)
FOR /F "delims=" %%A IN ('%Command%') DO (
    set PreCommitInfo=%%A
)
set CommitInfo=%PreCommitInfo:~9%
goto :eof


:GetCurrentTimeYMDHMS
echo BuildPlugin Start GetCurrentTimeYMDHMS..
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do set TmpTimestamp=%%a
for /f "tokens=* delims= " %%a in ("%TmpTimestamp%") do (
        set "CurrentTimeYMDHMS=%%a"
    )
goto :eof

:GetCurrentTimeYMD
echo BuildPlugin Start GetCurrentTime..
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd'"') do set TmpTimestamp=%%a
for /f "tokens=* delims= " %%a in ("%TmpTimestamp%") do (
        set "CurrentTimeYMD=%%a"
    )
goto :eof

:GetEngineDir
echo BuildPlugin GetEngineDir...
set "SearchEngineDir="
cd %EngineSearchDir%
set GetEngineCmd=python %PackageToolDir%\BuildHelper.py searchEngine %EngineSearchDir% %EngineBranch% %PackageToolDir%\Cache\SearchEngine.txt
echo BuildProject GetEngineDir GetEngineCmd = %GetEngineCmd%
call %GetEngineCmd% 
for /f "eol=* tokens=*" %%i in (%PackageToolDir%\Cache\SearchEngine.txt) do (
    set SearchEngineDir=%%i
)
if "%DefineEngineDir%"=="" (
set "EngineDir=%SearchEngineDir%"
) else (
set "EngineDir=%DefineEngineDir%"
)
goto :eof

:GetEngineName
echo BuildPlugin GetEngineName...
cd %EngineDir%
for %%i in ("%cd%") do (
  echo current dir=%%~ni
  set EngineName=%%~ni
)
goto :eof

:Exit
echo BuildPlugin Exit...
pause
goto :eof

:ExitFail
echo BuildPlugin Start ExitFail...
exit /b 1
