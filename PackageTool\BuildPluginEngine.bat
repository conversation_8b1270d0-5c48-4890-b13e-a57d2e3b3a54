﻿
setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
@echo off
rem add hosts System32/drivers/etc/hosts  below
rem **********		CreatorSamba

rem cmdkey /add:CreatorSamba /USER:**********\xverse_creator /Pass:xverse_creator >nul
rem net use \\CreatorSamba /USER:**********\xverse_creator xverse_creator /PERSISTENT:YES >nul
pushd %~dp0
cd ../
set PackageToolDir=%cd%\PackageTool
cd ../
set ProjectDir=C:\work\xverse\XVerseStudio
cd %PackageToolDir%
set ProjectOutput=C:\work\xverse\ProjectOutput
set OutPutName=XStudioPluginEngine
set PluginEngineName=PluginEngine
set ProjectBaseVersion=1.0
set "EngineDir="
set "EngineName="
set "DefineEngineDir="
set EngineSearchDir=C:\work\xverse\EngineOutput
set TranlateType=Release
set ProjectBranch=dev
set "BuildBranchName=dev"
set "ForceExit=false"
set "DeleteOldCache=true"
set "ResetLocalBranch=false"
set "EnablePullCode=false"
set "EngineBranch=xstudio"
set CommitId=0
set CommitVersion=0
set CommitInfo=0
set CapturePath=null
set AllParam=%*
set CurrentParam=%AllParam%
set "CurrentParamIndex=1"
echo BuildPluginEngine Receive Params: %AllParam%
echo BuildPluginEngine Start ReadParams...
call :ReadParams
call :GetCurrentTimeYMD
call :GetCurrentTimeYMDHMS
call :GetCurrentBranch
call :SwitchBranch
if "%ForceExit%"=="true" (
 goto :ExitFail
)
call :GetBranchVersion
call :GetProjectCommitInfo
call :GetEngineDir
call :GetEngineName
call :SetEnvConfigInfo
call :PrintInfo
call :CopyEngine
call :GetEngineCommitInfo

:: remove old info
call :RemoveCache
call :RemoveXverseWater
call :RemoveXBaseLib
call :RemoveXCOMM
call :RemoveArtChecker
call :RemoveXUE
call :RemoveProtoBuf
call :RemovePrefabricator
call :RemoveMegascansPlugin
call :RemoveXDataRateChecker
call :RemoveXverseAvatar
call :RemoveKawaiiPhysics
call :RemoveXVerseEngine

::compile
call :CompileXBaseLib
call :CompileXverseWater
call :CompileXCOMM
call :CompileProtoBuf
call :CompileArtChecker
call :CompileXUE
call :CompilePrefabricator
call :CompileMegascansPlugin
call :CompileXDataRateChecker
call :CompileXverseAvatar
call :CompileKawaiiPhysics
call :CompileXVerseEngine

::copy
call :CopyXBaseLib
call :CopyXverseWater
call :CopyXCOMM
call :CopyProtoBuf
call :CopyArtChecker
call :CopyXUE
call :CopyPrefabricator
call :CopyMegascansPlugin
call :CopyXDataRateChecker
call :CopyXverseAvatar
call :CopyKawaiiPhysics
call :CopyXVerseEngine

:: copy config files
call :CopyCommitInfo
call :CopyConfig
call :CopyTools
rem call :CopyRuntimeDll
call :GenXVerseProjectConfig

:: enbale system plugin
call :EnableModelingToolsEditorMode

::upload
call :CompresseProject
call :UploadEngine
goto :Exit


:ReadParams
rem echo Start ReadParams...
rem ./BuildPluginEngine.bat -deleteOldCache=false -tranlateType=Dev -engineDir=d:/ -engineSearchDir=d:/engine -gameConfigurations=Development -outPutDir=d:/ -outPutName=xstudio-123"
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	echo CurrentParamContent.!CurrentParam!
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam!") do ( 
		echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam!") do ( 
			echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-projectName" (
			    set ProjectName=!Value!
			)
			
			if "!Key!"=="-projectDir" (
			    set ProjectDir=!Value!
			)
			
			if "!Key!"=="-packageToolDir" (
			    set PackageToolDir=!Value!
			)
			
			if "!Key!"=="-targetPlatform" (
			    set TargetPlatform=!Value!
			)
			
			if "!Key!"=="-tranlateType" (
			    set TranlateType=!Value!
			)
			
			if "!Key!"=="-deleteOldCache" (
				set DeleteOldCache=!Value!
			)
			
			if "!Key!"=="-engineDir" (
				set DefineEngineDir=!Value!
			)
			
			if "!Key!"=="-engineSearchDir" (
				set EngineSearchDir=!Value!
			)
			if "!Key!"=="-gameConfigurations" (
				set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-outPutDir" (
				set ProjectOutput=!Value!
			)
			
			if "!Key!"=="-outPutName" (
				set OutPutName=!Value!
			)
			
			if "!Key!"=="-autoUpload" (
				set AutoUpload=!Value!
			)
			
			if "!Key!"=="-embedExit" (
				set EmbedExit=!Value!
			)
			
			if "!Key!"=="-exRawParam" (
				set ExRawParam=!CurrentParam:~12!
			)
			
			if "!Key!"=="-branchName" (
				set BuildBranchName=!Value!
			)

			if "!Key!"=="-engineBranch" (
				set EngineBranch=!Value!
			)

			if "!Key!"=="-enablePullCode" (
				set EnablePullCode=!Value!
			)
			
		    if "!Key!"=="-resetLocalBranch" (
				set ResetLocalBranch=!Value!
			)
		)
		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams

:PrintInfo
echo BuildPluginEngine Start PrintInfo..
echo CurrentTimeYMD=%CurrentTimeYMD%
echo CurrentTimeYMDHMS=%CurrentTimeYMDHMS%
echo DefineEngineDir=%DefineEngineDir%
echo EngineDir=%EngineDir%
echo EngineName=%EngineName%
echo ToolsCacheDir=%ToolsCacheDir%
echo TranlateType=%TranlateType%
echo ProjectOutput=%ProjectOutput%
echo PlugincCompileTargetDir=%PlugincCompileTargetDir%
echo OutPutPluginEngineZipPath=%OutPutPluginEngineZipPath%
echo OutPutPluginEngineZipName=%OutPutPluginEngineZipName%
echo OutPutProjectDirName=%OutPutProjectDirName%
echo PackageToolDir=%PackageToolDir%
echo CommitInfo=%CommitId%, %CommitVersion%, %CommitInfo%
echo EngineCommitId=%EngineCommitId%
echo EnablePullCode=%EnablePullCode%
goto :eof

:SetEnvConfigInfo
echo BuildPluginEngine Start SetEnvConfigInfo..
cd %ProjectOutput%
set ToolsCacheDir=%ProjectOutput%\XversePluginTools
set OutPutProjectDirName=%OutPutName%-%CurrentTimeYMDHMS%
set OutPutPluginEngineZipName=%OutPutProjectDirName%.zip
set OutPutPluginEngineZipPath=%ProjectOutput%\%OutPutProjectDirName%\%OutPutPluginEngineZipName%
set PlugincCompileTargetDir=%ProjectOutput%\%OutPutProjectDirName%
rmdir /S /Q %ToolsCacheDir%
goto :eof

:DeleteOldProjectCache
echo BuildPluginEngine Start DeleteOldProjectCache Flag=%DeleteOldCache%
if "%DeleteOldCache%"=="true" (
echo Start DeleteOldProjectCache...
cd %ProjectDir%
for /f "tokens=*" %%a in ('dir /s /b /ad Plugins') do (
        if "%%~nxa"=="Intermediate" (  
            @echo remove %%a
			rd /q /s "%%a"
        )
)

rd /q /s "%cd%Intermediate\Build\BuildRules"
echo "BuildPluginEngine git clean start"
git clean -f -x -d
)

goto :eof

:CopyEngine
echo BuildPluginEngine Start CopyEngine..%EngineDir%
set ZipEngineDir=%EngineDir%.zip

if exist %EngineDir% (
echo BuildPluginEngine CopyEngine exist Engine Dir %EngineDir%
) else (
echo BuildPluginEngine CopyEngine not exist Engine Dir
goto :Exit
)
if exist %ZipEngineDir% (
echo BuildPluginEngine CopyEngine already exist zip engine dir %ZipEngineDir%
) else (
bz c %ZipEngineDir% %EngineDir%
)
mkdir %ProjectOutput%
del %OutPutPluginEngineZipPath%
rd /s/q %PlugincCompileTargetDir%
mkdir %PlugincCompileTargetDir%
echo BuildPluginEngine CopyEngine Start Copy Engine zip to OutPutDir=%OutPutPluginEngineZipPath%
copy  %ZipEngineDir% %OutPutPluginEngineZipPath%
goto :eof

:CompileXBaseLib
echo BuildPluginEngine Start CompileXBaseLib...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XBaseLib\XBaseLib.uplugin -CppStd=Cpp17 -Package=%ToolsCacheDir%\XBaseLib -VS2019 -NoDeleteHostProject -TargetPlatforms=Win64
goto :eof

:CompileXverseWater
echo BuildPluginEngine Start CompileXverseWater...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XDeps\XverseWater\XverseWater.uplugin -Dependency=%ProjectDir%\Plugins\XBaseLib\XBaseLib.uplugin -CppStd=Cpp17 -Package=%ToolsCacheDir%\XverseWater -VS2019 -NoDeleteHostProject -TargetPlatforms=Win64
goto :eof

:CompileXCOMM
echo BuildPluginEngine Start CompileXCOMM...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XCOMM\XCOMM.uplugin -Dependency=%ProjectDir%\Plugins\XBaseLib\XBaseLib.uplugin -Dependency=%ProjectDir%\Plugins\Marketplace\Protobuf\Protobuf.uplugin -CppStd=Cpp17 -TargetPlatforms="Win64" -Package=%ToolsCacheDir%\XCOMM -VS2019 -NoDeleteHostProject
goto :eof


:CompileArtChecker
echo BuildPluginEngine Start Compile XverseArtChecker...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XDeps\XverseTurboEditing\XverseArtChecker\XverseArtChecker.uplugin -CppStd=Cpp17 -TargetPlatforms="Win64" -Package=%ToolsCacheDir%\XverseTurboEditing\XverseArtChecker -VS2019 -NoDeleteHostProject
goto :eof

:CompileXUE
echo BuildPluginEngine Start Compile XUE...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XUE\XUE.uplugin -Dependency=%ProjectDir%\Plugins\XBaseLib\XBaseLib.uplugin -Dependency=%ProjectDir%\Plugins\XCOMM\XCOMM.uplugin -Dependency=%ProjectDir%\Plugins\Marketplace\Protobuf\Protobuf.uplugin -Dependency=%ProjectDir%\Plugins\XDeps\XverseTurboEditing\XverseArtChecker\XverseArtChecker.uplugin -CppStd=Cpp17 -NoDeleteHostProject -TargetPlatforms="Win64" -Package=%ToolsCacheDir%\XUE -VS2019
goto :eof

:CompileProtoBuf
echo BuildPluginEngine Start Compile ProtoBuf...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\Marketplace\Protobuf\Protobuf.uplugin -CppStd=Cpp17 -NoDeleteHostProject -TargetPlatforms=Win64 -Package=%ToolsCacheDir%\ProtoBuf -VS2019
goto :eof

:CompilePrefabricator
echo BuildXversePluginTools Start CompilePrefabricator...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\Marketplace\Prefabricator\Prefabricator.uplugin -CppStd=Cpp17 -Package=%ToolsCacheDir%\Prefabricator -VS2019 -NoDeleteHostProject -TargetPlatforms=Win64
goto :eof

:CompileXMeshModelingToolset
echo BuildXversePluginTools Start CompileXMeshModelingToolset...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\Marketplace\XMeshModelingToolset\XMeshModelingToolset.uplugin -CppStd=Cpp17 -Package=%ToolsCacheDir%\XMeshModelingToolset -VS2019 -NoDeleteHostProject -TargetPlatforms=Win64
goto :eof

:CompileMegascansPlugin
echo BuildXversePluginTools Start CompileMegascansPlugin...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\Marketplace\MegascansPlugin\MegascansPlugin.uplugin -CppStd=Cpp17 -Package=%ToolsCacheDir%\MegascansPlugin -VS2019 -NoDeleteHostProject -TargetPlatforms=Win64
goto :eof

:CompileXDataRateChecker
echo BuildXversePluginTools Start CompileXDataRateChecker...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XDataRateChecker\XDataRateChecker.uplugin -CppStd=Cpp17 -Package=%ToolsCacheDir%\XDataRateChecker -VS2019 -NoDeleteHostProject -TargetPlatforms=Win64
goto :eof

:CompileXverseAvatar
echo BuildPluginEngine Start CompileXverseAvatar...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XDeps\XverseAvatar\XverseAvatar.uplugin -Dependency=%ProjectDir%\Plugins\XBaseLib\XBaseLib.uplugin -CppStd=Cpp17 -NoDeleteHostProject -TargetPlatforms="Win64" -Package=%ToolsCacheDir%\XverseAvatar -VS2019
goto :eof

:CompileKawaiiPhysics
echo BuildPluginEngine Start CompileKawaiiPhysics...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XDeps\KawaiiPhysics\KawaiiPhysics.uplugin -CppStd=Cpp17 -NoDeleteHostProject -TargetPlatforms="Win64" -Package=%ToolsCacheDir%\KawaiiPhysics -VS2019
goto :eof

:CompileXVerseEngine
echo BuildPluginEngine Start CompileXVerseEngine...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XDeps\XVerseEngine\XVerseEngine.uplugin -Dependency=%ProjectDir%\Plugins\XBaseLib\XBaseLib.uplugin -Dependency=%ProjectDir%\Plugins\XDeps\XverseAvatar\XverseAvatar.uplugin -Dependency=%ProjectDir%\Plugins\Marketplace\Protobuf\Protobuf.uplugin -CppStd=Cpp17 -NoDeleteHostProject -TargetPlatforms="Win64" -Package=%ToolsCacheDir%\XVerseEngine -VS2019
goto :eof

:RemoveCache
echo BuildPluginEngine RemoveCache...
cd %ProjectDir%
git clean -dxf
goto :eof

:RemoveXBaseLib
echo BuildPluginEngine RemoveXBaseLib...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib
rd /s/q %ProjectDir%\Plugins\XBaseLib\Binaries
rd /s/q %ProjectDir%\Plugins\XBaseLib\Intermediate
goto :eof

:RemoveXverseWater
echo BuildPluginEngine RemoveXverseWater...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XverseWater
rd /s/q %ProjectDir%\Plugins\XDeps\XverseWater\Binaries
rd /s/q %ProjectDir%\Plugins\XDeps\XverseWater\Intermediate
goto :eof

:RemoveXCOMM
echo BuildPluginEngine RemoveXCOMM...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XCOMM
rd /s/q %ProjectDir%\Plugins\XCOMM\Binaries
rd /s/q %ProjectDir%\Plugins\XCOMM\Intermediate
goto :eof

:RemoveArtChecker
echo BuildPluginEngine RemoveArtChecker...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XverseTurboEditing
rd /s/q %ProjectDir%\Plugins\XDeps\XverseTurboEditing\XverseArtChecker\Binaries
rd /s/q %ProjectDir%\Plugins\XDeps\XverseTurboEditing\XverseArtChecker\Intermediate
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\Protobuf
goto :eof

:RemoveXUE
echo BuildPluginEngine RemoveXUE...
echo remove XUE.%PlugincCompileTargetDir%\Engine\Plugins\XUE
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XUE
rd /s/q %ProjectDir%\Plugins\XUE\Binaries
rd /s/q %ProjectDir%\Plugins\XUE\Intermediate
goto :eof

:RemoveProtoBuf
echo BuildPluginEngine RemoveProtoBuf...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\Protobuf
rd /s/q %ProjectDir%\Plugins\Marketplace\Protobuf\Binaries
rd /s/q %ProjectDir%\Plugins\Marketplace\Protobuf\Intermediate
goto :eof


:RemovePrefabricator
echo BuildPluginEngine RemovePrefabricator...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\Prefabricator
rd /s/q %ProjectDir%\Plugins\Marketplace\Prefabricator\Binaries
rd /s/q %ProjectDir%\Plugins\Marketplace\Prefabricator\Intermediate
goto :eof

:RemoveXMeshModelingToolset
echo BuildPluginEngine RemoveXMeshModelingToolset...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XMeshModelingToolset
rd /s/q %ProjectDir%\Plugins\Marketplace\XMeshModelingToolset\Binaries
rd /s/q %ProjectDir%\Plugins\Marketplace\XMeshModelingToolset\Intermediate
goto :eof

:RemoveMegascansPlugin
echo BuildPluginEngine RemoveMegascansPlugin...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\MegascansPlugin
rd /s/q %ProjectDir%\Plugins\Marketplace\MegascansPlugin\Binaries
rd /s/q %ProjectDir%\Plugins\Marketplace\MegascansPlugin\Intermediate
goto :eof

:RemoveXDataRateChecker
echo BuildPluginEngine RemoveXDataRateChecker...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XDataRateChecker
rd /s/q %ProjectDir%\Plugins\XDataRateChecker\Binaries
rd /s/q %ProjectDir%\Plugins\XDataRateChecker\Intermediate
goto :eof

:RemoveXverseAvatar
echo BuildPluginEngine RemoveXverseAvatar...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XverseAvatar
rd /s/q %ProjectDir%\Plugins\XDeps\XverseAvatar\Binaries
rd /s/q %ProjectDir%\Plugins\XDeps\XverseAvatar\Intermediate
goto :eof

:RemoveKawaiiPhysics
echo BuildPluginEngine RemoveKawaiiPhysics...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\KawaiiPhysics
rd /s/q %ProjectDir%\Plugins\XDeps\KawaiiPhysics\Binaries
rd /s/q %ProjectDir%\Plugins\XDeps\KawaiiPhysics\Intermediate
goto :eof


:RemoveXVerseEngine
echo BuildPluginEngine RemoveXVerseEngine...
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XVerseEngine
rd /s/q %ProjectDir%\Plugins\XDeps\XVerseEngine\Binaries
rd /s/q %ProjectDir%\Plugins\XDeps\XVerseEngine\Intermediate
goto :eof

::copy xbaseLib
:CopyXBaseLib
echo BuildPluginEngine CopyXBaseLib...
robocopy /E %ToolsCacheDir%\XBaseLib\HostProject\Plugins\XBaseLib %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\
copy  %PackageToolDir%\Plugins\XBaseLib.uplugin %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\XBaseLib.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Source
goto :eof

:CopyXverseWater
echo BuildPluginEngine CopyXverseWater...
robocopy /E %ToolsCacheDir%\XverseWater\HostProject\Plugins\XverseWater %PlugincCompileTargetDir%\Engine\Plugins\XverseWater\
echo BuildPluginEngine CopyXverseWater uplugin...
copy  %PackageToolDir%\Plugins\XverseWater.uplugin %PlugincCompileTargetDir%\Engine\Plugins\XverseWater\XverseWater.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XverseWater\Source
goto :eof

::copy xcomm
:CopyXCOMM
echo BuildPluginEngine Start CopyXCOMM..
robocopy /E %ToolsCacheDir%\XCOMM\HostProject\Plugins\XCOMM %PlugincCompileTargetDir%\Engine\Plugins\XCOMM\
copy  %PackageToolDir%\Plugins\XCOMM.uplugin %PlugincCompileTargetDir%\Engine\Plugins\XCOMM\XCOMM.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XCOMM\Source
goto :eof

::copy XverseArtChecker
:CopyArtChecker
echo BuildPluginEngine Start CopyArtChecker..
robocopy /E %ToolsCacheDir%\XverseTurboEditing\XverseArtChecker\HostProject\Plugins\XverseArtChecker %PlugincCompileTargetDir%\Engine\Plugins\XverseTurboEditing\
copy  %PackageToolDir%\Plugins\XverseArtChecker.uplugin %PlugincCompileTargetDir%\Engine\Plugins\XverseTurboEditing\XverseArtChecker\XverseArtChecker.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XverseTurboEditing\XverseArtChecker\Source
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XverseTurboEditing\XverseArtChecker\HostProject
goto :eof

::copy xue
:CopyXUE
echo BuildPluginEngine Start CopyXUE..
robocopy /E %ToolsCacheDir%\XUE\HostProject\Plugins\XUE %PlugincCompileTargetDir%\Engine\Plugins\XUE\
copy  %PackageToolDir%\Plugins\XUE.uplugin %PlugincCompileTargetDir%\Engine\Plugins\XUE\XUE.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XUE\Source

echo [/Script/XverseAssetUploader.LocalSrcVersion]> %PlugincCompileTargetDir%\Engine\Plugins\XUE\Config\DefaultLocalSrcVersion.ini
echo SourceVersion=%CommitVersion%>> %PlugincCompileTargetDir%\Engine\Plugins\XUE\Config\DefaultLocalSrcVersion.ini
echo SourceCommitId=%CommitId%>> %PlugincCompileTargetDir%\Engine\Plugins\XUE\Config\DefaultLocalSrcVersion.ini
goto :eof

:GenXVerseProjectConfig
echo BuildPluginEngine Start GenXVerseProjectConfig..
mkdir %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config
del %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo [/Script/ProjectItem.XVerseProjectConfigMng]> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo ProjectName=%PluginEngineName%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo BuildTime=%CurrentTimeYMDHMS%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo ProjectVersion=%ProjectBaseVersion%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo ConsoleEnv=%TranlateType%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo PackageType=%GameConfigurations%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo Branch=%BuildBranchName%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CommitId=%CommitId%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CommitVersion=%CommitVersion%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CommitInfo="%CommitInfo%">> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CapturePath=%CapturePath%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo EngineVersion=%EngineName%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo EngineCommitId=%EngineCommitId%>> %PlugincCompileTargetDir%\Engine\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
goto :eof

::copy protobuf
:CopyProtoBuf
echo BuildPluginEngine Start CopyProtoBuf...
robocopy /E %ToolsCacheDir%\Protobuf\HostProject\Plugins\Protobuf %PlugincCompileTargetDir%\Engine\Plugins\Protobuf\
copy  %PackageToolDir%\Plugins\ProtoBuf.uplugin %PlugincCompileTargetDir%\Engine\Plugins\Protobuf\ProtoBuf.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\Protobuf\Source
goto :eof


:CopyPrefabricator
echo BuildXversePluginTools Start CopyPrefabricator...
robocopy /E %ToolsCacheDir%\Prefabricator\HostProject\Plugins\Prefabricator %PlugincCompileTargetDir%\Engine\Plugins\Prefabricator\
copy  %PackageToolDir%\Plugins\Prefabricator.uplugin %PlugincCompileTargetDir%\Engine\Plugins\Prefabricator\Prefabricator.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\Prefabricator\Source
goto :eof

:CopyXMeshModelingToolset
echo BuildXversePluginTools Start CopyXMeshModelingToolset...
robocopy /E %ToolsCacheDir%\XMeshModelingToolset\HostProject\Plugins\XMeshModelingToolset %PlugincCompileTargetDir%\Engine\Plugins\XMeshModelingToolset\
copy  %PackageToolDir%\Plugins\XMeshModelingToolset.uplugin %PlugincCompileTargetDir%\Engine\Plugins\XMeshModelingToolset\XMeshModelingToolset.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XMeshModelingToolset\Source
goto :eof

:CopyMegascansPlugin
echo BuildXversePluginTools Start CopyMegascansPlugin...
robocopy /E %ToolsCacheDir%\MegascansPlugin\HostProject\Plugins\MegascansPlugin %PlugincCompileTargetDir%\Engine\Plugins\MegascansPlugin\
copy  %PackageToolDir%\Plugins\MegascansPlugin.uplugin %PlugincCompileTargetDir%\Engine\Plugins\MegascansPlugin\MegascansPlugin.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\MegascansPlugin\Source
goto :eof

:CopyXDataRateChecker
echo BuildXversePluginTools Start CopyXDataRateChecker...
robocopy /E %ToolsCacheDir%\XDataRateChecker\HostProject\Plugins\XDataRateChecker %PlugincCompileTargetDir%\Engine\Plugins\XDataRateChecker\
copy  %PackageToolDir%\Plugins\XDataRateChecker.uplugin %PlugincCompileTargetDir%\Engine\Plugins\XDataRateChecker\XDataRateChecker.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XDataRateChecker\Source
goto :eof

:CopyXverseAvatar
echo BuildXversePluginTools Start CopyXverseAvatar...
robocopy /E %ToolsCacheDir%\XverseAvatar\HostProject\Plugins\XverseAvatar %PlugincCompileTargetDir%\Engine\Plugins\XverseAvatar\
copy  %PackageToolDir%\Plugins\XverseAvatar.uplugin %PlugincCompileTargetDir%\Engine\Plugins\XverseAvatar\XverseAvatar.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XverseAvatar\Source
goto :eof

:CopyKawaiiPhysics
echo BuildXversePluginTools Start CopyKawaiiPhysics...
robocopy /E %ToolsCacheDir%\KawaiiPhysics\HostProject\Plugins\KawaiiPhysics %PlugincCompileTargetDir%\Engine\Plugins\KawaiiPhysics\
copy  %PackageToolDir%\Plugins\KawaiiPhysics.uplugin %PlugincCompileTargetDir%\Engine\Plugins\KawaiiPhysics\KawaiiPhysics.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\KawaiiPhysics\Source
goto :eof

:CopyXVerseEngine
echo BuildXversePluginTools Start CopyXVerseEngine...
robocopy /E %ToolsCacheDir%\XVerseEngine\HostProject\Plugins\XVerseEngine %PlugincCompileTargetDir%\Engine\Plugins\XVerseEngine\
copy  %PackageToolDir%\Plugins\XVerseEngine.uplugin %PlugincCompileTargetDir%\Engine\Plugins\XVerseEngine\XVerseEngine.uplugin
rd /s/q %PlugincCompileTargetDir%\Engine\Plugins\XVerseEngine\Source
goto :eof

:EnableModelingToolsEditorMode
echo BuildXversePluginTools Start EnableModelingToolsEditorMode...
mkdir %PlugincCompileTargetDir%\Engine\Plugins\Experimental\ModelingToolsEditorMode
copy  %PackageToolDir%\Plugins\ModelingToolsEditorMode.uplugin %PlugincCompileTargetDir%\Engine\Plugins\Experimental\ModelingToolsEditorMode\ModelingToolsEditorMode.uplugin
goto :eof

::XverseCommunication
:CopyConfig
echo BuildPluginEngine Start CopyConfig..%TranlateType%
mkdir %PlugincCompileTargetDir%\Engine\Config
rd /s/q %PlugincCompileTargetDir%\Engine\Config\BaseXConsole.ini
if "%TranlateType%"=="Dev" (
	copy  %PackageToolDir%\BaseXConsole_Dev.ini %PlugincCompileTargetDir%\Engine\Config\BaseXConsole.ini
)

if "%TranlateType%"=="Release" (
	copy  %PackageToolDir%\BaseXConsole.ini %PlugincCompileTargetDir%\Engine\Config\BaseXConsole.ini
)
rd /s/q %PlugincCompileTargetDir%\Engine\Config\BaseXSkinSambaCache.ini
copy  %PackageToolDir%\BaseXSkinSambaCache.ini %PlugincCompileTargetDir%\Engine\Config\BaseXSkinSambaCache.ini
goto :eof

::git commit info
:CopyCommitInfo
echo BuildPluginEngine Start CopyCommitInfo..
echo "XversePluginTools:"%CommitId% >%PlugincCompileTargetDir%\XversePluginToolsVersion.ini
echo "Branch:"%BuildBranchName% >> %PlugincCompileTargetDir%\XversePluginToolsVersion.ini
echo "BranchVersion:"%CommitVersion% >> %PlugincCompileTargetDir%\XversePluginToolsVersion.ini
echo "BuildTime:"%CurrentTimeYMDHMS% >> %PlugincCompileTargetDir%\XversePluginToolsVersion.ini

goto :eof

::copy lowpoly tools
:CopyTools
echo BuildPluginEngine Start CopyTools..
rd /s/q %PlugincCompileTargetDir%\Engine\lowpolyApplication
set LowpolyToolPath=\\CreatorSamba\XverseCreator\lowpolyApplication
robocopy /E %LowpolyToolPath% %PlugincCompileTargetDir%\Engine\lowpolyApplication\
goto :eof

:CopyRuntimeDll
echo BuildPluginEngine Start CopyRuntimeDll..
set RuntimeDllPath=%EngineDir%\Engine\Binaries\ThirdParty\AppLocalDependencies\Win64\Microsoft.VC.CRT
mkdir %PlugincCompileTargetDir%\Engine\Binaries\Win64\
copy /N %RuntimeDllPath%\vcruntime140.dll %PlugincCompileTargetDir%\Engine\Binaries\Win64\vcruntime140.dll
copy /N %RuntimeDllPath%\vcruntime140_1.dll %PlugincCompileTargetDir%\Engine\Binaries\Win64\vcruntime140_1.dll
goto :eof

:CompresseProject
echo BuildPluginEngine CompresseProject..

cd %PlugincCompileTargetDir%
bz d %OutPutPluginEngineZipName% Engine\Plugins\Experimental\ModelingToolsEditorMode\ModelingToolsEditorMode.uplugin
bz a %OutPutPluginEngineZipName% Engine
bz a %OutPutPluginEngineZipName% XversePluginToolsVersion.ini
goto :eof

:UploadEngine
echo BuildPluginEngine Start UploadEngine..
cd %PlugincCompileTargetDir%
set FixedProjectBranch=%ProjectBranch:/=_%
set RemotePluginEngineDirName=%OutPutName%-%CurrentTimeYMDHMS%
set RemotePluginEngineFileName=%OutPutName%-%TranlateType%.zip
set UploadDirPath=\\CreatorSamba\XverseCreator\XStudio\%PluginEngineName%\%RemotePluginEngineDirName%-%FixedProjectBranch%
set UploadFilePath=%UploadDirPath%\%RemotePluginEngineFileName%
mkdir %UploadDirPath%
copy  %OutPutPluginEngineZipName% %UploadFilePath%
echo "UploadEngine Success %UploadFilePath%"
goto :eof


:GetCurrentBranch
rem echo BuildPluginEngine GetCurrentBranch
cd %ProjectDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:SwitchBranch
echo BuildPluginEngine SwitchBranch...
echo BuildPluginEngine SwitchBranch CurrentBranch=%ProjectBranch%,BuildBranchName=%BuildBranchName%
if "%BuildBranchName%"=="" (
set "BuildBranchName=%ProjectBranch%"
)
echo BuildPluginEngine SwitchBranch Use BranchName=%BuildBranchName%
cd %ProjectDir%
if %BuildBranchName%==%ProjectBranch% (
echo BuildProject No Need SwitchBranch
) else (
echo Start CheckOut %BuildBranchName%
git fetch
git checkout %BuildBranchName%
)
call :GetCurrentBranch
echo BuildPluginEngine BuildBranchName=%BuildBranchName% CurrentBranch=%ProjectBranch% EnablePullCode=%EnablePullCode%
if %BuildBranchName%==%ProjectBranch% (
echo BuildPluginEngine SwitchBranch Success %BuildBranchName%
if "%EnablePullCode%"=="true" (
git reset --hard origin/%BuildBranchName%
git pull
git log -2
)
) else (
echo SwitchBranch Fail OldBranch=%ProjectBranch%, %BuildBranchName%
set "ForceExit=true"
)

goto :eof

:GetBranchVersion
cd %ProjectDir%
set BranchVersionCommand=git rev-list --count --first-parent %ProjectBranch%
echo BuildProject BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
SET CommitVersion=%%A
)
goto :eof

:GetProjectCommitInfo
echo BuildProject GetProjectCommitInfo=%ProjectDir%
cd %ProjectDir%
set Command=git log -1 --oneline

FOR /F "delims= " %%A IN ('%Command%') DO (
    SET CommitId=%%A
)
FOR /F "delims=" %%A IN ('%Command%') DO (
    set PreCommitInfo=%%A
)
set CommitInfo=%PreCommitInfo:~9%
goto :eof

:GetEngineCommitInfo
echo BuildPluginEngine GetEngineCommitInfo=%EngineDir%
set GetEngineCommitCmd=%PythonBin% %PackageToolDir%\BuildHelper.py getEngineCommitInfo %EngineDir% %PackageToolDir%\Cache\EngineCommitInfo.txt
echo BuildPluginEngine GetEngineDir GetEngineCmd = %GetEngineCmd%
call %GetEngineCommitCmd% 
for /f "eol=* tokens=*" %%i in (%PackageToolDir%\Cache\EngineCommitInfo.txt) do (
    set InstalledEngineCommitInfo=%%i
)
echo BuildPluginEngine InstalledEngineCommitInfo=%InstalledEngineCommitInfo%

if "%InstalledEngineCommitInfo%"=="" (

echo BuildProject GetEngineLastestCommitMessage=%EngineDir%
cd %EngineDir%
set Command=git log -1 --oneline
FOR /F "delims=" %%A IN ('%Command%') DO (
SET EngineCommitId=%%A
)
) else  (
set "EngineCommitId=%InstalledEngineCommitInfo%"
)

goto :eof


:GetCurrentTimeYMDHMS
echo BuildPluginEngine Start GetCurrentTimeYMDHMS..
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do set TmpTimestamp=%%a
for /f "tokens=* delims= " %%a in ("%TmpTimestamp%") do (
        set "CurrentTimeYMDHMS=%%a"
    )
goto :eof

:GetCurrentTimeYMD
echo BuildPluginEngine Start GetCurrentTime..
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd'"') do set TmpTimestamp=%%a
for /f "tokens=* delims= " %%a in ("%TmpTimestamp%") do (
        set "CurrentTimeYMD=%%a"
    )
goto :eof

:GetEngineDir
echo BuildPluginEngine GetEngineDir...
set "SearchEngineDir="
cd %EngineSearchDir%
set GetEngineCmd=python %PackageToolDir%\BuildHelper.py searchEngine %EngineSearchDir% %EngineBranch% %PackageToolDir%\Cache\SearchEngine.txt
echo BuildProject GetEngineDir GetEngineCmd = %GetEngineCmd%
call %GetEngineCmd% 
for /f "eol=* tokens=*" %%i in (%PackageToolDir%\Cache\SearchEngine.txt) do (
    set SearchEngineDir=%%i
)
if "%DefineEngineDir%"=="" (
set "EngineDir=%SearchEngineDir%"
) else (
set "EngineDir=%DefineEngineDir%"
)
goto :eof

:GetEngineName
echo BuildPluginEngine GetEngineName...
cd %EngineDir%
for %%i in ("%cd%") do (
  echo current dir=%%~ni
  set EngineName=%%~ni
)
goto :eof

:Exit
echo BuildPluginEngine Exit...
pause
goto :eof

:ExitFail
echo BuildPluginEngine Start ExitFail...
exit /b 1
