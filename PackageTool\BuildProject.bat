setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
@echo off
cd ../
set PackageToolDir=%cd%\PackageTool
set ProjectDir=C:\work\xverse\XVerseStudio
set PythonBin=C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe
cd %PackageToolDir%
set ProjectName=XStudio
set EngineBranch=xstudio
set ProjectBranch=dev
set BuildBranchName=dev
set ProjectBaseVersion=1.0
set TargetPlatform=Win64
set TranlateType=Release
set GameConfigurations=Development
set "DeleteOldCache=false"
set "EmbedGitClean=false"
set "ResetLocalBranch=false"
set "AutoUpload=true"
set "EmbedExit=false"
set "ForceExit=false"
set "IncludeCrashReporter=true"
set "IncludeDebugFiles=true"
set "IncludePrerequisites=true"
set "IncludeMountSamba=false"
set "EnableSwitchBranch=true"
set "WithGameContentOverride=false"
set "Remarks="
set "ExRawParam="
set ProjectOutput=C:\work\xverse\ProjectOutput
set "ProjectOutputName="
set XCaptureIdePath=
set "EngineDir="
set EngineSearchDir=C:\work\xverse\EngineOutput
set "DefineEngineDir="
::set "DefineEngineDir=D:\UEEngineWork\UEBuildOutPut\XVerseEngine-20220812-100933"
set "ProjectBuildUrl=unknown"
set "UploadDestPath=undefine"
set "EngineLocked=false"
set "NotifyMsgTitle="
set "EnableNotify=false"
set ProjectLogOutput=nil

set AllParam=%*
set CurrentParam=%AllParam%
set "CurrentParamIndex=1"
echo BuildProject Receive Params: %AllParam%
echo BuildProject Start ReadParams...

call :ReadParams
call :InitConfig
rem call :CheckBuildingState
if "%EngineLocked%"=="true" (
 goto :Exit
)
call :ReadXCaptureConfig
call :GetBuildTime
call :GetEngineDir
if "%EngineDir%"=="" (
	echo BuildProject GetEngineDir Error No Exist
	set "NotifyMsgTitle=%ProjectName%(%TargetPlatform%)出包失败，当前引擎分支(%BuildBranchName%)不存在，请先手动编译引擎"
	call :FinishBuildState
	call :SendNotifyMsg
	goto :ExitFail
)
call :GetEngineName
call :SetEnvConfig
call :SwitchBranch
if "%ForceExit%"=="true" (
	set "NotifyMsgTitle=%ProjectName%(%TargetPlatform%)出包失败，Switch To %BuildBranchName% Error"
	call :FinishBuildState
	call :SendNotifyMsg
 	goto :Exit
)
call :GetBranchVersion
call :GetProjectCommitInfo
call :GetEngineCommitInfo
call :CreateDateDir
call :PrintParams
if not exist "%EngineDir%\Engine\Binaries" (
	set "NotifyMsgTitle=%ProjectName%(%TargetPlatform%)出包失败，Engine is unavailable, please check"
	call :FinishBuildState
	call :SendNotifyMsg
	goto :Exit
)
call :DeleteOldProjectCache
call :BackupRunEnvLibs
call :CopyProjectFile
call :GenXVerseProjectConfig
call :BuildCookRunProject
call :CopyEngineBuildLog
call :CheckBuildResult
if "%ForceExit%"=="true" (
    echo BuildProject Error...
	goto :Exit
)
call :CopyFiles
call :CopyRunEnvLibs
call :WriteCommitInfo
call :UploadSymbol
call :CompresseProject
call :UploadProject
call :FinishBuildState
call :SendNotifyMsg
goto :Exit

:ReadParams
rem echo Start ReadParams...
rem ./BuildProject.bat -projectDir=D:\XStudio -packageToolDir=D:/ -projectName=XStudio -targetPlatform=Win64 -deleteOldCache=false -tranlateType=Dev -engineDir=d:/ -gameConfigurations=Development -outPutDir=d:/ -outPutName=xstudio-123 -autoUpload=false -embedExit=false -branchName=dev -exRawParam="-d=1"
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
rem echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	rem echo CurrentParamContent="!CurrentParam!"
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam!") do ( 
		rem echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam!") do ( 
			rem echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-projectName" (
			    set ProjectName=!Value!
			)
			
			if "!Key!"=="-projectDir" (
			    set ProjectDir=!Value!
			)
			
			if "!Key!"=="-packageToolDir" (
			    set PackageToolDir=!Value!
			)
			
			if "!Key!"=="-targetPlatform" (
			    set TargetPlatform=!Value!
			)
			
			if "!Key!"=="-tranlateType" (
			    set TranlateType=!Value!
			)
			
			if "!Key!"=="-deleteOldCache" (
				set DeleteOldCache=!Value!
				set EmbedGitClean=!Value!
			)

			if "!Key!"=="-embedGitClean" (
				set EmbedGitClean=!Value!
			)
			
			if "!Key!"=="-engineDir" (
				set DefineEngineDir=!Value!
			)
			
			if "!Key!"=="-engineSearchDir" (
				set EngineSearchDir=!Value!
			)
			if "!Key!"=="-gameConfigurations" (
				set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-outPutDir" (
				set ProjectOutput=!Value!
			)
			
			if "!Key!"=="-outPutName" (
				set ProjectOutputName=!Value!
			)
			
			if "!Key!"=="-autoUpload" (
				set AutoUpload=!Value!
			)
			
			if "!Key!"=="-embedExit" (
				set EmbedExit=!Value!
			)
			
			if "!Key!"=="-branchName" (
				set BuildBranchName=!Value!
			)
			
			if "!Key!"=="-enableNotify" (
				set EnableNotify=!Value!
			)
			
			if "!Key!"=="-resetLocalBranch" (
				set ResetLocalBranch=!Value!
			)
			
			if "!Key!"=="-remarks" (
				set Remarks=!Value!
			)

			if "!Key!"=="-engineBranch" (
				set EngineBranch=!Value!
			)

			if "!Key!"=="-enableSwitchBranch" (
				set EnableSwitchBranch=!Value!
			)
			
			if "!Key!"=="-includeMountSamba" (
				set IncludeMountSamba=!Value!
			)
			
			if "!Key!"=="-includeCrashReporter" (
				set IncludeCrashReporter=!Value!
			)

			if "!Key!"=="-includeDebugFiles" (
				set IncludeDebugFiles=!Value!
			)
			
			if "!Key!"=="-includePrerequisites" (
				set IncludePrerequisites=!Value!
			)
			
			if "!Key!"=="-withGameContentOverride" (
				set WithGameContentOverride=!Value!
			)
			if "!Key!"=="-projectBuildUrl" (
				set ProjectBuildUrl=!Value!
			)
						
			if "!Key!"=="-exRawParam" (
				set ExRawParam=!CurrentParam:~12!
			)
			
		)
		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams


:ReadXCaptureConfig
set FixedProjectBranch=%BuildBranchName:/=_%
set FixedEngineBranch=%EngineBranch:/=_%
set XCaptureConfigFileName=XCaptureConfig_%FixedEngineBranch%_%FixedProjectBranch%.ini
echo BuildProject Start ReadXCaptureConfig...%PackageToolDir%\Cache\%XCaptureConfigFileName%
for /f "eol=* tokens=*" %%i in (%PackageToolDir%\Cache\%XCaptureConfigFileName%) do (
	set LineContent=%%i
	echo ReadXCaptureConfig Line="!LineContent!"
	for /f "tokens=1 delims==" %%k  in ("!LineContent%!") do ( 
		::echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%v  in ("!LineContent%!") do ( 
			::echo param_value=%%k	
			set "Value=%%v"
			if "!Key!"=="LastIdePath" (
			    set XCaptureIdePath=!Value!
			)
		)
	)
)
goto :eof

:PrintParams
echo BuildProject Start PrintParams...
echo CurrentTimeYMDHMS=%CurrentTimeYMDHMS%
echo ProjectName=%ProjectName%
echo ProjectDir=%ProjectDir%
echo ProjectOutput=%ProjectOutput%
echo ProjectOutputName=%ProjectOutputName%
echo OutputWin64Dir=%OutputWin64Dir%
echo PackageToolDir=%PackageToolDir%
echo XCaptureIdePath=%XCaptureIdePath%
echo EngineSearchDir=%EngineSearchDir%
echo SearchEngineDir=%SearchEngineDir%
echo DefineEngineDir=%DefineEngineDir%
echo EngineDir=%EngineDir%
echo BuildBranchName=%BuildBranchName%
echo ProjectBaseVersion=%ProjectBaseVersion%
echo CommitId=%CommitId%
echo CommitVersion=%CommitVersion%
echo PreCommitInfo=%PreCommitInfo%
echo CommitInfo=%CommitInfo%
echo EngineCommitId=%EngineCommitId%
echo EngineBuildLogPath=%EngineBuildLogPath%
echo ProjectLogOutput=%ProjectLogOutput%
echo TaskBuildingStateFile=%TaskBuildingStateFile%
echo EnableSwitchBranch=%EnableSwitchBranch%
echo ProjectBuildUrl=%projectBuildUrl%
goto :eof

:SetEnvConfig
echo BuildProject start SetEnvConfig...
set EngineBuildLogPath=C:\Users\<USER>\AppData\Roaming\Unreal Engine\AutomationTool\Logs\C+work+xverse+EngineOutput+%EngineName%
set ProjectLogOutput=%ProjectOutput%\Logs\%ProjectName%
set ProjectRunEnvLibsBackupPath=%ProjectOutput%\RunEnvLibs
mkdir %ProjectLogOutput%
del "%EngineBuildLogPath%\"*.txt
del "%ProjectLogOutput%\"*.txt

RD /S/Q %ProjectRunEnvLibsBackupPath%
mkdir %ProjectRunEnvLibsBackupPath%
goto :eof

:InitConfig
echo BuildProject start InitConfig...
set TaskBuildingStateDir=%PackageToolDir%\Cache
set TaskBuildingStateFile=%TaskBuildingStateDir%\Building.state
echo TaskBuildingStateFile=%TaskBuildingStateFile%
mkdir %TaskBuildingStateDir%
del %PackageToolDir%\Cache\%ProjectName%.package
goto :eof

:CheckBuildingState
echo BuildProject start CheckBuildingState...%TaskBuildingStateFile%
if exist %TaskBuildingStateFile% (
	set "EngineLocked=true"
	echo BuildProject CheckBuildingState exist %TaskBuildingStateFile%
	echo Other Task is Building
) else (
	echo %ProjectName% > %TaskBuildingStateFile%
)
goto :eof

:FinishBuildState
echo BuildProject start FinishBuildState...
rem del %TaskBuildingStateFile%
goto :eof

:CopyEngineBuildLog
echo BuildProject start CopyEngineBuildLog...%EngineBuildLogPath% to %ProjectLogOutput%
xcopy /E/H/Y "%EngineBuildLogPath%\" %ProjectLogOutput%\
goto :eof

:CopyProjectFile
echo BuildProject start SetProjectFile...
::remove old files
del %ProjectDir%\Source\*.Target.cs
del %ProjectDir%\Source\XVerse\*.cs
del %ProjectDir%\Source\XVerse\*.cpp
RD /S/Q %ProjectDir%\Config\Windows
RD /S/Q %ProjectDir%\Config\Linux
RD /S/Q %ProjectDir%\Config\Android
::copy project files
echo BuildProject start copy uproject=%ProjectDir%\Script\Projects\%ProjectName%\%ProjectName%.uproject to %ProjectDir%\
xcopy /E/H/Y %ProjectDir%\Script\Projects\%ProjectName%\* %ProjectDir%\
rem set CopyUprojectCmd=copy %ProjectDir%\Script\Projects\%ProjectName%\%ProjectName%.uproject  %ProjectDir%\%ProjectName%.uproject
rem echo CopyUprojectCmd=%CopyUprojectCmd%
rem call %CopyUprojectCmd%
rem copy -rf %ProjectDir%\Script\Projects\%ProjectName%\Source %ProjectDir%\Source\
rem copy %ProjectDir%\Script\Projects\%ProjectName%\Source\XVerse\*.cs %ProjectDir%\Source\XVerse\
goto :eof

:DeleteOldProjectCache
echo BuildProject Start DeleteOldProjectCache DeleteOldCache=%DeleteOldCache%,EmbedGitClean=%EmbedGitClean%
cd %ProjectDir%
if "%DeleteOldCache%"=="true" (
echo BuildProject DeleteOldProjectCache...
for /f "tokens=*" %%a in ('dir /s /b /ad Plugins') do (
        if "%%~nxa"=="Intermediate" (  
            @echo remove %%a
			rd /q /s "%%a"
        )
)

rd /q /s "%cd%Intermediate\Build\BuildRules"

)

if "%EmbedGitClean%"=="true" (
echo "BuildProject git clean start"
git clean -f -x -d
)

goto :eof

:CompileProject
echo BuildProject Start CompileProject...
set CompileCmd=%EngineDir%\Engine\Build\BatchFiles\RunUAT.bat -ScriptsForProject=%ProjectDir%\%ProjectName%.uproject BuildCookRun -project=%ProjectDir%\%ProjectName%.uproject -build
if "%WithGameContentOverride%"=="true" (
	set CompileCmd=%CompileCmd% -UBTArgs="-ProjectDefine:WITH_GAME_CONTENT_OVERRIDE=1" 
)
echo CompileCmd CompileCmd=%CompileCmd%
call %CompileCmd%
goto :eof

:BuildCookRunProject
echo BuildProject Start BuildCookRunProject...
rem -nocompileeditor
cd %ProjectDir% 
set BuildProjectCmd=%EngineDir%\Engine\Build\BatchFiles\RunUAT.bat -ScriptsForProject=%ProjectDir%\%ProjectName%.uproject BuildCookRun -installed -noP4 -project=%ProjectDir%\%ProjectName%.uproject
set BuildProjectCmd=%BuildProjectCmd% -cook -stage -archive -archivedirectory=%OutputWin64Dir% -package -ue4exe=%EngineDir%\Engine\Binaries\Win64\UE4Editor-Cmd.exe -ddc=InstalledDerivedDataBackendGraph
set BuildProjectCmd=%BuildProjectCmd% -pak -build -targetplatform=%TargetPlatform% -clientconfig=%GameConfigurations% -utf8output
if "%IncludePrerequisites%"=="true" (
	set BuildProjectCmd=%BuildProjectCmd% -prereqs 
)

if "%IncludeDebugFiles%"=="false" (
	set BuildProjectCmd=%BuildProjectCmd% -nodebuginfo 
)

if "%IncludeCrashReporter%"=="true" (
	set BuildProjectCmd=%BuildProjectCmd% -CrashReporter 
)

if "%WithGameContentOverride%"=="true" (
	set BuildProjectCmd=%BuildProjectCmd% -UBTArgs="-ProjectDefine:WITH_GAME_CONTENT_OVERRIDE=1" 
)

if "%ExRawParam%"=="" (
	echo "BuildProject BuildCookRunProject ExRawParam Is Empty"
) else (
	set BuildProjectCmd=%BuildProjectCmd% %ExRawParam% 
)
rem set BuildProjectCmd=%BuildProjectCmd% -cookflavor=ETC2

echo BuildProject BuildCookRunProject=%BuildProjectCmd%
call %BuildProjectCmd%
echo BuildProject BuildCookRunProject End=%BuildProjectCmd%
goto :eof

:CheckBuildResult
echo %TargetPlatform%| findstr Linux >nul && (
	if not exist "%OutputWin64Dir%\LinuxNoEditor\%ProjectName%.sh" (
		echo BuildProject did exist project %OutputWin64Dir%\LinuxNoEditor\%ProjectName%.sh
		set "NotifyMsgTitle=%ProjectName%(%TargetPlatform%)出包失败"
		call :FinishBuildState
		call :SendNotifyMsg
		set "ForceExit=true"
	)
)

echo %TargetPlatform%| findstr Win64 >nul && (
	if not exist "%OutputWin64Dir%\WindowsNoEditor\%ProjectName%.exe" (
		echo BuildProject did exist project %OutputWin64Dir%\WindowsNoEditor\%ProjectName%.exe
		set "NotifyMsgTitle=%ProjectName%(%TargetPlatform%)出包失败"
		call :FinishBuildState
		call :SendNotifyMsg
		set "ForceExit=true"
	)
)
echo %TargetPlatform%| findstr Android >nul && (
	set "ForceExit=true"
	if exist "%OutputWin64Dir%\Android_ETC2" (
		set "ForceExit=false"
	)
	if exist "%OutputWin64Dir%\Android_ASTC" (

		set "ForceExit=false"
	)
	if "%ForceExit%"=="true" (
		echo BuildProject Error...
		echo BuildProject did exist project Android %OutputWin64Dir%\Android
		set "NotifyMsgTitle=%ProjectName%(%TargetPlatform%) Error"
		call :FinishBuildState
		call :SendNotifyMsg
	)
)


goto :eof

:CopyFiles
echo BuildProject Start CopyFiles...
cd %ProjectDir%

echo %TargetPlatform%| findstr Win64 >nul && (

mkdir %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor

if "%IncludeMountSamba%"=="true" (
  copy %PackageToolDir%\MountSambaCache.bat %OutputWin64Dir%\WindowsNoEditor\
)

if "%IncludeMountSamba%"=="false" (
  del %OutputWin64Dir%\WindowsNoEditor\*.txt
)

copy %~dp0Start%ProjectName%.bat %OutputWin64Dir%\WindowsNoEditor\
if exist %EngineDir%\Engine\Version\EngineVersion.ini (
	copy %EngineDir%\Engine\Version\EngineVersion.ini %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\EngineVersion.ini
	echo "Engine: "%EngineCommitId% >%OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
)
if "%TranlateType%"=="Dev" (
	copy  %PackageToolDir%\BaseXConsole_Dev.ini %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XConsole.ini
)

if "%TranlateType%"=="Release" (
	copy  %PackageToolDir%\BaseXConsole.ini %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XConsole.ini
)

if "%ProjectName%"=="XStudio" (
	copy  %ProjectDir%\Config\DefaultXBatchEditWhiteList.ini %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\RumtimeXBatchEditWhiteList.ini

	rem copy onlinepathType
	mkdir %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\PathFiles
	copy  %ProjectDir%\Plugins\XCOMM\PathFiles\OnlinePathType.csv %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\PathFiles\OnlinePathType.csv

)

)
echo %TargetPlatform%| findstr Linux >nul && (
mkdir %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor
echo "Engine: "%EngineCommitId% >%OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
if exist %EngineDir%\Engine\Version\EngineVersion.ini (
copy %EngineDir%\Engine\Version\EngineVersion.ini %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\EngineVersion.ini
)

if "%TranlateType%"=="Dev" (
	copy  %PackageToolDir%\BaseXConsole_Dev.ini %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XConsole.ini
)

if "%TranlateType%"=="Release" (
	copy  %PackageToolDir%\BaseXConsole.ini %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XConsole.ini
)
)

echo %ProjectName% > %PackageToolDir%\Cache\%ProjectName%.package
goto :eof


:BackupRunEnvLibs
echo BuildProject Start BackupRunEnvLibs...From=%PackageToolDir%, TargetPlatform=%TargetPlatform%
echo %TargetPlatform%| findstr Win64 >nul && (
	xcopy /E/H/Y %PackageToolDir%\PackageLibs\* %ProjectRunEnvLibsBackupPath%\
)
goto :eof

:CopyRunEnvLibs
echo BuildProject Start CopyRunEnvLibs...From=%ProjectRunEnvLibsBackupPath%,TargetPlatform=%TargetPlatform%
echo %TargetPlatform%| findstr Win64 >nul && (
	xcopy /E/H/Y %ProjectRunEnvLibsBackupPath%\* %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Binaries\
)
goto :eof

:WriteCommitInfo
echo BuildProject Start WriteCommitInfo...
cd %ProjectDir%

echo [/Script/XverseAssetUploader.LocalSrcVersion]> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\DefaultLocalSrcVersion.ini
echo SourceVersion=%CommitVersion%>> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\DefaultLocalSrcVersion.ini
echo SourceCommitId=%CommitId%>> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\DefaultLocalSrcVersion.ini

set FixedProjectBranch=%BuildBranchName:/=_%
echo %TargetPlatform%| findstr Win64 >nul && (
  
echo "Game: "%CommitId% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
echo "ProjectCommitId: "%CommitId% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
echo [/Script/ProjectItem.XVProjectManager] > %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo Version=%FixedProjectBranch%-%ProjectBaseVersion%.%CommitVersion% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo InnerVersion=%TranlateType% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo ProjectCommitId=%CommitId% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo IdePath=%XCaptureIdePath% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
)

echo %TargetPlatform%| findstr Linux >nul && (

echo "Game: "%CommitId% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
echo "ProjectCommitId: "%CommitId% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
echo [/Script/ProjectItem.XVProjectManager] > %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo Version=%FixedProjectBranch%-%ProjectBaseVersion%.%CommitVersion%>> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo InnerVersion=%TranlateType% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo ProjectCommitId=%CommitId% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo IdePath=%XCaptureIdePath% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
)

goto :eof

:GenXVerseProjectConfig
echo BuildProject Start GenXVerseProjectConfig..
mkdir %ProjectDir%\Plugins\XBaseLib\Config
del %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
rem set CommitInfo=merge fix from dev to tanchuangyu
echo [/Script/ProjectItem.XVerseProjectConfigMng]> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo ProjectName=%ProjectName%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo BuildTime=%CurrentTimeYMDHMS%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo ProjectVersion=%ProjectBaseVersion%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo ConsoleEnv=%TranlateType%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo PackageType=%GameConfigurations%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo Branch=%BuildBranchName%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CommitId=%CommitId%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CommitVersion=%CommitVersion%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CommitInfo="%CommitInfo%">> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo CapturePath=%XCaptureIdePath%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo EngineCommitId=%EngineCommitId%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini
echo UProjectName=%ProjectName%>> %ProjectDir%\Plugins\XBaseLib\Config\XVerseProjectConfig.ini

rem new plugin config
mkdir %ProjectDir%\Plugins\XBaseDev\Config
del %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo [/Script/ProjectItem.XVerseProjectConfigMng]> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo ProjectName=%ProjectName%>> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo BuildTime=%CurrentTimeYMDHMS%>> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo ProjectVersion=%ProjectBaseVersion%>> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo ConsoleEnv=%TranlateType%>> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo PackageType=%GameConfigurations%>> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo Branch=%BuildBranchName%>> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo CommitId=%CommitId%>> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo CommitVersion=%CommitVersion%>> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo CommitInfo="%CommitInfo%">> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo CapturePath=%XCaptureIdePath%>> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
echo EngineCommitId=%EngineCommitId%>> %ProjectDir%\Plugins\XBaseDev\Config\XVerseProjectConfig.ini
goto :eof

:CompresseProject
echo BuildProject start CompresseProject...%AutoUpload%
set ArchZipName=%ProjectName%_%TranlateType%.zip
if "%AutoUpload%"=="true" (
echo BuildProject CompresseProject Inging...
echo BuildProject go dir %ProjectOutput%
cd %ProjectOutput%
echo BuildProject source Dir=%OutputWin64Dir%,dest file=%ArchZipName%
bz c %ArchZipName% %OutputWin64Dir%\WindowsNoEditor %OutputWin64Dir%\LinuxNoEditor %OutputWin64Dir%\Android_ETC2 %OutputWin64Dir%\Android_ASTC
)
echo BuildProject End CompresseProject...
goto :eof

:UploadProject
echo BuildProject start UploadProject...%AutoUpload%
set FixedProjectBranch=%BuildBranchName:/=_%
set UploadDestPath=\\CreatorSamba\XverseCreator\%ProjectName%\%TargetPlatform%\%ProjectOutputName%_%FixedProjectBranch%_%ProjectBaseVersion%.%CommitVersion%
if "%AutoUpload%"=="true" (
cd %ProjectOutput%
echo BuildProject UploadProject Inging...
mkdir %UploadDestPath%
copy %ArchZipName% %UploadDestPath%
echo BuildProject "uploadProject success %UploadDestPath%\%ArchZipName%"
)
echo BuildProject End Upload...
set "NotifyMsgTitle=%ProjectName%(%TargetPlatform%)出包成功"
goto :eof


:SendNotifyMsg
echo BuildProject Start SendNotifyMsg...EnableNotify=%EnableNotify% Content=%NotifyMsgTitle%
set NotifyVersion=%BuildBranchName%_%ProjectBaseVersion%.%CommitVersion%
if "%EnableNotify%"=="true" (
	call %PackageToolDir%\SendMsgProxy.bat "%NotifyMsgTitle%" %CurrentTime% %UploadDestPath% %GameConfigurations% %NotifyVersion% %ProjectLogOutput% %BuildBranchName% %ProjectBuildUrl%console
)

goto :eof

:UploadSymbol
echo BuildProject Start UploadSymbol...%AutoUpload%
set UploadSymbolCmd=%EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildGraph -target="Make Installed Build Win64" -script=%PackageToolDir%\PackageProjectBuild.xml -set:EmbedSymStore=true -set:XProjectDir=%OutputWin64Dir% -set:XProjectName=%ProjectName% -set:XPlatform=%TargetPlatform% -set:EmbedRemoveSymbol=false
echo UploadSymbolCmd=%UploadSymbolCmd%
call %UploadSymbolCmd%
goto :eof

:UploadSymbolWithSystem
echo BuildProject Start UploadSymbolWithSystem...%AutoUpload%
echo BuildProject UploadSymbolWithSystem Ing...
set PDBPathWin64=%ProjectDir%\Binaries\Win64\%ProjectName%.pdb
set PDBPathLinux=%ProjectDir%\Binaries\Linux\%ProjectName%.pdb
set PDBPathAndroid=%ProjectDir%\Binaries\Android\%ProjectName%.pdb
set PDBPathIOS=%ProjectDir%\Binaries\IOS\%ProjectName%.pdb

set PDBStorePathWin64=\\CreatorSamba\XverseCreator\%ProjectName%\Symbol\Win64\
set PDBStorePathLinux=\\CreatorSamba\XverseCreator\%ProjectName%\Symbol\Linux\
set PDBStorePathAndroid=\\CreatorSamba\XverseCreator\%ProjectName%\Symbol\Android\
set PDBStorePathIOS=\\CreatorSamba\XverseCreator\%ProjectName%\Symbol\IOS\
echo BuildProject UploadSymbolWithSystem Mkdir

mkdir %PDBStorePathWin64%
mkdir %PDBStorePathLinux%
mkdir %PDBStorePathAndroid%
mkdir %PDBStorePathIOS%
echo BuildProject UploadSymbolWithSystem Copy...

copy %PDBPathWin64% %PDBStorePathWin64%
copy %PDBPathLinux% %PDBStorePathLinux%
copy %PDBPathAndroid% %PDBStorePathAndroid%
copy %PDBPathIOS% %PDBStorePathIOS%
echo BuildProject End UploadSymbolWithSystem
goto :eof

:GetEngineDir
echo BuildProject GetEngineDir...%EngineBranch%
set "SearchEngineDir="
cd %EngineSearchDir%
set GetEngineCmd=%PythonBin% %PackageToolDir%\BuildHelper.py searchEngine %EngineSearchDir% %EngineBranch% %PackageToolDir%\Cache\SearchEngine.txt
echo BuildProject GetEngineDir GetEngineCmd = %GetEngineCmd%
call %GetEngineCmd% 
for /f "eol=* tokens=*" %%i in (%PackageToolDir%\Cache\SearchEngine.txt) do (
    set SearchEngineDir=%%i
)
if "%DefineEngineDir%"=="" (
set "EngineDir=%SearchEngineDir%"
) else (
set "EngineDir=%DefineEngineDir%"
)
echo BuildProject GetEngineDir Result=%EngineDir%
goto :eof

:GetEngineName
echo BuildProject GetEngineName...
cd %EngineDir%
for %%i in ("%cd%") do (
  echo current dir=%%~ni
  set EngineName=%%~ni
)
goto :eof

:GetBuildTime
echo BuildProject Start GetBuildTime...
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do (
	echo CurrentTime=%%a
	set CurrentTime=%%a 
)
for /f %%a in ("%CurrentTime%") do set CurrentTimeYMDHMS=%%a
goto :eof

:CreateDateDir
echo BuildProject Start CreateDateDir...
if "%ProjectOutputName%"=="" (
	set ProjectOutputName=%ProjectName%-%CurrentTimeYMDHMS%

)
set "OutputWin64Dir=%ProjectOutput%\%ProjectOutputName%"
mkdir %OutputWin64Dir%
setx CreatorOutputEnv %OutputWin64Dir%
goto :eof


:GetCurrentBranch
rem echo BuildProject GetCurrentBranch... 
cd %ProjectDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:SwitchBranch
call :GetCurrentBranch
echo BuildProject SwitchBranch EnableSwitchBranch=%EnableSwitchBranch%,CurrentBranch=%ProjectBranch%,BuildBranchName=%BuildBranchName%
if "%BuildBranchName%"=="" (
	set "BuildBranchName=%ProjectBranch%"
)
if "%EnableSwitchBranch%"=="true" (
	call :SwitchBranchInner
)
goto :eof

:SwitchBranchInner
echo BuildProject SwitchBranch Use BranchName=%BuildBranchName%
cd %ProjectDir%
call :GetCurrentBranch

if "%ResetLocalBranch%"=="true" (
	echo BuildProject Start Git Reset Local Cache...
	git rebase --abort
	git merge --abort
	git reset --hard origin/%ProjectBranch%
)
if %BuildBranchName%==%ProjectBranch% (
	echo BuildProject No Need SwitchBranch(Current Branch Is %BuildBranchName%)
) else (
	git fetch
	git checkout %BuildBranchName%
)
call :GetCurrentBranch
echo BuildBranchName=%BuildBranchName% CurrentBranch=%ProjectBranch%
if %BuildBranchName%==%ProjectBranch% (
	echo BuildProject SwitchBranch Success %BuildBranchName% ResetLocalBranch=%ResetLocalBranch%

	git stash
	git pull
	git log -3
) else (
	echo SwitchBranch Fail OldBranch=%ProjectBranch%, %BuildBranchName%
	set "ForceExit=true"
)
goto :eof

:GetBranchVersion
echo BuildProject GetBranchVersion...
cd %ProjectDir%
set BranchVersionCommand=git rev-list --count --first-parent %BuildBranchName%
echo BuildProject BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
set CommitVersion=%%A
)
goto :eof

:GetProjectCommitInfo
echo BuildProject GetProjectCommitInfo=%ProjectDir%
cd %ProjectDir%
set Command=git log -1 --oneline

FOR /F "delims= " %%A IN ('%Command%') DO (
    SET CommitId=%%A
)
FOR /F "delims=" %%A IN ('%Command%') DO (
    set PreCommitInfo=%%A
)
set CommitInfo=%PreCommitInfo:~9%
goto :eof

:GetEngineCommitInfo
echo BuildProject GetEngineCommitInfo=%EngineDir%
set GetEngineCommitCmd=%PythonBin% %PackageToolDir%\BuildHelper.py getEngineCommitInfo %EngineDir% %PackageToolDir%\Cache\EngineCommitInfo.txt
echo BuildProject GetEngineDir GetEngineCmd = %GetEngineCmd%
call %GetEngineCommitCmd% 
for /f "eol=* tokens=*" %%i in (%PackageToolDir%\Cache\EngineCommitInfo.txt) do (
    set InstalledEngineCommitInfo=%%i
)
echo BuildProject InstalledEngineCommitInfo=%InstalledEngineCommitInfo%

if "%InstalledEngineCommitInfo%"=="" (

echo BuildProject GetEngineLastestCommitMessage=%EngineDir%
cd %EngineDir%
set Command=git log -1 --oneline
FOR /F "delims=" %%A IN ('%Command%') DO (
SET EngineCommitId=%%A
)
) else  (
set "EngineCommitId=%InstalledEngineCommitInfo%"
)

goto :eof


:Exit
echo BuildProject Exit...%EmbedExit%
if "%EmbedExit%"=="true" (
  echo BuildProject  BuildProject Pause...
  pause
)
goto :eof

:ExitFail
echo BuildProject ExitFail...
exit /b 1
