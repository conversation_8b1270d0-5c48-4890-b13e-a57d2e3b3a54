setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
rem @echo off
cd ../
set PackageToolDir=%cd%\PackageTool
cd ../
set ProjectDir=C:\work\xverse\XVerseStudio
set ProjectName=XCapture
set ProjectBranch=dev
set BuildBranchName=dev
set EngineBranch=xstudio
set ProjectBaseVersion=1.0
set ProjectBranchVersion=1
set TargetPlatform=Linux
set TranlateType=Release
set "DeleteOldCache=true"
set "ResetLocalBranch=false"
set "UploadCos=false"
set "EnableNotify=false"
set "EnableSwitchBranch=true"
set "CallSelf=true"
set "Remarks="
set "ProjectBuildUrl="
set GameConfigurations=Development
set ProjectOutput=C:\work\xverse\ProjectOutput
set "ProjectOutPutName="
set EngineSearchDir=C:\work\xverse\EngineOutput
set "DefineEngineDir="
set CosCliHome=C:\work\xverse\PackageTools\CosTools
set CosSecretId=AKIDIagW9wbotiOuDGjREo96Al9FAkodlRSH
set CosSecretKey=kNlKfGdp67vaMvyPsPhmbppd4npeUjiS
set CosCreatorBucketName=cos://xverse-creator-1258211750
set CosCreatorBucketRegion=xverse-creator-1258211750
set CosCreatorBucketNickName=xverse-creator

set AllParam=%*
set CurrentParam=%AllParam%
::echo all: %AllParam%
set "CurrentParamIndex=1"
call :GetBuildTime
call :ReadParams
call :CreateDateDir
call :PrintParams
rem call :GetCurrentBranch
rem call :GetBranchVersion
rem call :GetLastestCommitMessage
rem call :UploadToCOSNew
rem goto :Exit
call :BuildProject
echo %TargetPlatform%| findstr Linux >nul && (
	if not exist "%OutputWin64Dir%\LinuxNoEditor\%ProjectName%.sh" (
		echo BuildXCapture did exist project %OutputWin64Dir%\LinuxNoEditor\%ProjectName%.sh
		goto :ExitFail
	)
)

echo %TargetPlatform%| findstr Win64 >nul && (
	if not exist "%OutputWin64Dir%\WindowsNoEditor\%ProjectName%.exe" (
		echo BuildXCapture did exist project %OutputWin64Dir%\WindowsNoEditor\%ProjectName%.exe
		goto :ExitFail
	)
)

call :GetCurrentBranch
call :GetBranchVersion
call :GetLastestCommitMessage
call :PrintParams
call :WriteCommitInfo
call :CompresseProject
call :UploadToLocalServer
call :UploadToCOS
rem call :UploadToCOSNew
goto :Exit

:ReadParams
echo BuildXCapture Start ReadParams...
rem ./BuildProject.bat -projectDir=D:\XStudio -tranlateType=Dev -engineDir=d:/ -gameConfigurations=Development
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
::echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	rem echo param.!CurrentParam!
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam%!") do ( 
		::echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam%!") do ( 
			::echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-projectName" (
			    set ProjectName=!Value!
			)
			
			if "!Key!"=="-projectDir" (
			    set ProjectDir=!Value!
			)
			if "!Key!"=="-outPutDir" (
				set ProjectOutput=!Value!
			)
			if "!Key!"=="-targetPlatform" (
			    set TargetPlatform=!Value!
			)
						
			if "!Key!"=="-engineDir" (
				set DefineEngineDir=!Value!
			)
			
			if "!Key!"=="-engineSearchDir" (
				set EngineSearchDir=!Value!
			)
			if "!Key!"=="-uploadCos" (
				set UploadCos=!Value!
			)
			
			if "!Key!"=="-deleteOldCache" (
				set DeleteOldCache=!Value!
			)
			
			if "!Key!"=="-gameConfigurations" (
				set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-tranlateType" (
			    set TranlateType=!Value!
			)
			if "!Key!"=="-cosCliHome" (
				set CosCliHome=!Value!
			)
						
			if "!Key!"=="-branchName" (
				set BuildBranchName=!Value!
			)
			
			if "!Key!"=="-packageToolDir" (
			    set PackageToolDir=!Value!
			)

		    if "!Key!"=="-enableNotify" (
				set EnableNotify=!Value!
			)
			
			if "!Key!"=="-resetLocalBranch" (
				set ResetLocalBranch=!Value!
			)
			if "!Key!"=="-projectBuildUrl" (
				set ProjectBuildUrl=!Value!
			)

			if "!Key!"=="-remarks" (
				set Remarks=!Value!
			)

			if "!Key!"=="-engineBranch" (
				set EngineBranch=!Value!
			)
			
			if "!Key!"=="-enableSwitchBranch" (
				set EnableSwitchBranch=!Value!
			)

		)

		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams

:PrintParams
echo BuildXCapture Start PrintParams... 
echo BuildXCapture ProjectName=%ProjectName%
echo BuildXCapture ProjectDir=%ProjectDir%
echo BuildXCapture OutputWin64Dir=%OutputWin64Dir%
ECHO BuildXCapture ProjectLastestCommit=%LastestVer%
ECHO BuildXCapture ProjectBranch=%ProjectBranch%
ECHO BuildXCapture ProjectBranchVersion=%ProjectBranchVersion%
echo BuildXCapture PackageToolDir=%PackageToolDir%
echo BuildXCapture UploadCos=%UploadCos%
goto :eof

:BuildProject
echo BuildXCapture Start BuildProject...
rem -nocompileeditor
cd %PackageToolDir% 
set BuildProjectCmd=%PackageToolDir%\BuildProject.bat -projectBuildUrl=%ProjectBuildUrl% -projectDir=%ProjectDir% -projectName=%ProjectName% -engineDir=%DefineEngineDir% -engineSearchDir=%EngineSearchDir% -packageToolDir=%PackageToolDir% -targetPlatform=%TargetPlatform% -tranlateType=%TranlateType% -gameConfigurations=%GameConfigurations% -autoUpload=false -outPutDir=%ProjectOutput% -outPutName=%ProjectOutPutName% -embedExit=false -branchName=%BuildBranchName% -engineBranch=%EngineBranch% -enableSwitchBranch=%EnableSwitchBranch% -deleteOldCache=%DeleteOldCache% -resetLocalBranch=%ResetLocalBranch% -enableNotify=%EnableNotify% -remarks=%Remarks%
echo BuildXCapture BuildProjectCmd=%BuildProjectCmd%
call %BuildProjectCmd%
goto :eof


:WriteCommitInfo
echo BuildXCapture Start WriteCommitInfo... 
cd %ProjectDir%
set FixedProjectBranch=%BuildBranchName:/=_%
set FixedEngineBranch=%EngineBranch:/=_%
set XCaptureConfigFileName=XCaptureConfig_%FixedEngineBranch%_%FixedProjectBranch%.ini
call :GetLastestCommitMessage
echo %TargetPlatform%| findstr Win64 >nul && (
  
echo "Game: "%LastestVer% >>%OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
echo [/Script/ProjectItem.XVProjectManager] > %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo Version=%FixedProjectBranch%_%ProjectBaseVersion%.%ProjectBranchVersion% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo InnerVersion=%LastestVer%>> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo IdePath=WindowsNoEditor/%FixedProjectBranch%_%ProjectBranchVersion%/%ProjectBaseVersion%.%ProjectBranchVersion%/%ProjectName%.zip>> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo LastIdePath=WindowsNoEditor/%FixedProjectBranch%_%ProjectBranchVersion%/%ProjectBaseVersion%.%ProjectBranchVersion%/%ProjectName%.zip> %PackageToolDir%\Cache\%XCaptureConfigFileName%
echo LastCaptureVersion=%FixedProjectBranch%_%ProjectBranchVersion%>> %PackageToolDir%\Cache\%XCaptureConfigFileName%
set CurrentCreatorCosPath=WindowsNoEditor/%FixedProjectBranch%_%ProjectBranchVersion%/%ProjectBaseVersion%.%ProjectBranchVersion%/%ProjectName%.zip
)

echo %TargetPlatform%| findstr Linux >nul && (

echo "Game: "%LastestVer% >>%OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
echo [/Script/ProjectItem.XVProjectManager] > %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo Version=%FixedProjectBranch%_%ProjectBaseVersion%.%ProjectBranchVersion% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo InnerVersion=%LastestVer% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo IdePath=LinuxNoEditor/%FixedProjectBranch%_%ProjectBranchVersion%/%ProjectBaseVersion%.%ProjectBranchVersion%/%ProjectName%.zip>> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
copy %PackageToolDir%\XVerseCreator.sh %OutputWin64Dir%\LinuxNoEditor\XVerseCreator.sh
echo LastIdePath=LinuxNoEditor/%FixedProjectBranch%_%ProjectBranchVersion%/%ProjectBaseVersion%.%ProjectBranchVersion%/%ProjectName%.zip> %PackageToolDir%\Cache\%XCaptureConfigFileName%
echo LastCaptureVersion=%FixedProjectBranch%_%ProjectBranchVersion%_%TranlateType%>> %PackageToolDir%\Cache\%XCaptureConfigFileName%
set CurrentCreatorCosPath=LinuxNoEditor/%FixedProjectBranch%_%ProjectBranchVersion%/%ProjectBaseVersion%.%ProjectBranchVersion%/%ProjectName%.zip
)

goto :eof

:CompresseProject
echo BuildXCapture start CompresseProject...
cd %OutputWin64Dir%
set ArchZipName=%ProjectName%.zip
echo %TargetPlatform%| findstr Win64 >nul && (
bz c %ArchZipName% %OutputWin64Dir%\WindowsNoEditor\
)

echo %TargetPlatform%| findstr Linux >nul && (
bz c %ArchZipName% %OutputWin64Dir%\LinuxNoEditor\
)
set ArchZipPath=%OutputWin64Dir%\%ArchZipName%
goto :eof

:UploadToLocalServer
echo BuildXCapture start UploadToLocalServer... 
cd %ProjectOutput%
set FixedProjectBranch=%BuildBranchName:/=_%
set UploadDestPath=\\CreatorSamba\XverseCreator\%ProjectName%\%TargetPlatform%\%ProjectOutPutName%_%FixedProjectBranch%_%ProjectBaseVersion%.%ProjectBranchVersion%\
mkdir %UploadDestPath%
copy %ArchZipPath% %UploadDestPath%
echo BuildXCapture UploadToLocalServer Success %UploadDestPath%
goto :eof

:UploadToCOS
echo BuildXCapture start UploadToCOS...%UploadCos%

if "%UploadCos%"=="false" (
	goto :eof
)
echo BuildXCapture start UploadToCOS Ing...
cd %CosCliHome%
set SourceFile=%ArchZipPath%
set DestFile=cos://xverse-creator-1258211750/%CurrentCreatorCosPath%
echo SourceFile=%SourceFile% to DestFile=%DestFile%
set UploadXCaptureCmd=coscli.exe cp %SourceFile% %DestFile% -e cos.ap-guangzhou.myqcloud.com -i %CosSecretId% -k %CosSecretKey%
echo UploadXCaptureCmd=%UploadXCaptureCmd%
%UploadXCaptureCmd% > upload.txt
for /f "eol=* tokens=*" %%i in (upload.txt) do (
	set ResultBody=%%i
	echo ResultBody="!ResultBody!"
	echo "!ResultBody!"| findstr 100% >nul && (
	  echo upload cos success 
	)
)
echo BuildXCapture UploadToCOS end...
goto :eof

:UploadToCOSNew
echo BuildXCapture start UploadToCOSNew...%UploadCos%

if "%UploadCos%"=="false" (
	goto :eof
)
echo BuildXCapture start UploadToCOSNew Ing...
cd %CosCliHome%
set SourceFile=%ArchZipPath%
set DestFile=cos://xverse-creator-sh-1258211750/%CurrentCreatorCosPath%
echo SourceFile=%SourceFile% to DestFile=%DestFile%
set UploadXCaptureCmd=coscli.exe cp %SourceFile% %DestFile% -e cos.ap-shanghai.myqcloud.com -i %CosSecretId% -k %CosSecretKey%
echo UploadXCaptureCmd=%UploadXCaptureCmd%
%UploadXCaptureCmd% > upload.txt
for /f "eol=* tokens=*" %%i in (upload.txt) do (
	set ResultBody=%%i
	echo ResultBody="!ResultBody!"
	echo "!ResultBody!"| findstr 100% >nul && (
	  echo upload cos new success 
	)
)
echo BuildXCapture UploadToCOSNew end...
goto :eof


:GetBuildTime
echo BuildXCapture Start GetBuildTime...
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do (
	echo BuildXCapture CurrentTime=%%a
	set CurrentTime=%%a 
)
for /f %%a in ("%CurrentTime%") do set YSTD=%%a
goto :eof

:CreateDateDir
echo BuildXCapture Start CreateDateDir...
set ProjectOutputName=%ProjectName%-%YSTD%
set "OutputWin64Dir=%ProjectOutput%\%ProjectOutputName%"
setx CreatorOutputEnv %OutputWin64Dir%
goto :eof


:GetCurrentBranch
echo BuildXCaptrue GetCurrentBranch...
cd %ProjectDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:GetLastestCommitMessage
echo BuildXCaptrue GetLastestCommitMessage=%ProjectDir%
cd %ProjectDir%
set Command=git log -1 --oneline

FOR /F "delims= " %%A IN ('%Command%') DO (
    SET LastestVer=%%A
)
goto :eof


:GetBranchVersion
echo BuildXCaptrue GetBranchVersion...
cd %ProjectDir%
set BranchVersionCommand=git rev-list --count --first-parent %ProjectBranch%
echo BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
SET ProjectBranchVersion=%%A
)
goto :eof

:Exit
echo BuildXCapture Exit...
goto :eof

:ExitFail
echo BuildXCapture ExitFail...
exit /b 1

