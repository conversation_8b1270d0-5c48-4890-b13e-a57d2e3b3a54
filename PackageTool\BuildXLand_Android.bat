setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
@echo off
cd ../
set PackageToolDir=%cd%\PackageTool
cd ../
set ProjectDir=C:\work\xverse\XVerseStudio
set ProjectName=XLand
set ProjectBranch=dev
set "BuildBranchName="
set ProjectBaseVersion=1.0
set ProjectBranchVersion=1
set TargetPlatform=Android
set TranlateType=Release
set "DeleteOldCache=true"
set ExRawParam=-cookflavor=ETC2
set GameConfigurations=Development
set ProjectOutput=C:\work\xverse\ProjectOutput
set "ProjectOutPutName="
set EngineDir=
set AllParam=%*
set CurrentParam=%AllParam%
::echo all: %AllParam%
set "CurrentParamIndex=1"


call :GetBuildTime
call :ReadParams
call :GetCurrentBranch
call :GetBranchVersion
call :CreateDateDir
call :PrintParams
call :BuildProject
goto :Exit

:ReadParams
echo BuildXLand_Android Start ReadParams...
rem ./BuildProject.bat -projectDir=D:\XStudio -tranlateType=Dev -engineDir=d:/ -gameConfigurations=Development
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
::echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	echo param.!CurrentParam!
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam%!") do ( 
		::echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam%!") do ( 
			::echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-projectName" (
			    set ProjectName=!Value!
			)
			
			if "!Key!"=="-projectDir" (
			    set ProjectDir=!Value!
			)
			
			if "!Key!"=="-targetPlatform" (
			    set TargetPlatform=!Value!
			)
			
			if "!Key!"=="-uploadCos" (
				set UploadCos=!Value!
			)
			
			if "!Key!"=="-deleteOldCache" (
				set DeleteOldCache=!Value!
			)
			
			if "!Key!"=="-gameConfigurations" (
				set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-tranlateType" (
			    set TranlateType=!Value!
			)
			
			if "!Key!"=="-branchName" (
				set BuildBranchName=!Value!
			)
		)

		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams

:PrintParams
echo BuildXLand_Android Start PrintParams... 
echo BuildXLand_Android ProjectName=%ProjectName%
echo BuildXLand_Android ProjectDir=%ProjectDir%
echo BuildXLand_Android OutputWin64Dir=%OutputWin64Dir%
ECHO BuildXLand_Android ProjectLastestCommit=%LastestVer%
ECHO BuildXLand_Android ProjectBranch=%ProjectBranch%
ECHO BuildXLand_Android ProjectBranchVersion=%ProjectBranchVersion%
echo BuildXLand_Android PackageToolDir=%PackageToolDir%
echo BuildXLand_Android Branch=%ProjectBranch%
echo BuildXLand_Android Branch=%UploadCos%
echo BuildXLand_Android EngineDir=%EngineDir%
goto :eof


:BuildProject
echo BuildXLand_Android Start BuildProject...
rem -nocompileeditor
cd %PackageToolDir% 
set BuildProjectCmd=%PackageToolDir%\BuildProject.bat -projectDir=%ProjectDir% -projectName=%ProjectName% -packageToolDir=%PackageToolDir% -targetPlatform=%TargetPlatform% -tranlateType=%TranlateType% -gameConfigurations=%GameConfigurations%  -deleteOldCache=%DeleteOldCache% -branchName=%BuildBranchName% -exRawParam=%ExRawParam%
call %BuildProjectCmd%
goto :eof

:GetEngineDir
echo BuildXLand_Android Start GetEngineDir...
set "EngineDir="
set "SearchEngineDir="
set "DefineEngineDir="
::set "DefineEngineDir=D:\UEEngineWork\UEBuildOutPut\XVerseEngine-20220812-100933"
set "EngineSearchDir=D:\UEEngineWork\UEBuildOutPut"
cd %EngineSearchDir%
for /f "tokens=4 delims= " %%a in ('dir /AD XVerseEngine-* ^| findstr XVerseEngine') do set "SearchEngineDir=%%a"
::echo Search SearchEngineDir=%SearchEngineDir%
if "%DefineEngineDir%"=="" (
set "EngineDir=%EngineSearchDir%\%SearchEngineDir%"
) else (
set "EngineDir=%DefineEngineDir%"
)

goto :eof

:GetBuildTime
echo BuildXLand_Android Start GetBuildTime...
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do (
	echo BuildXLand_Android CurrentTime=%%a
	set CurrentTime=%%a 
)
for /f %%a in ("%CurrentTime%") do set YSTD=%%a
goto :eof

:CreateDateDir
echo BuildXLand_Android Start CreateDateDir...
set ProjectOutputName=%ProjectName%-%YSTD%
set "OutputWin64Dir=%ProjectOutput%\%ProjectOutputName%"
setx CreatorOutputEnv %OutputWin64Dir%
goto :eof


:GetCurrentBranch
cd %ProjectDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:GetBranchVersion
cd %ProjectDir%
set BranchVersionCommand=git rev-list --count --first-parent %ProjectBranch%
echo BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
SET ProjectBranchVersion=%%A
)
goto :eof

:Exit
echo BuildXLand_Android Exit...
pause
goto :eof

