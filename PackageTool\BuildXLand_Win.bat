setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
@echo off
cd ../
set PackageToolDir=%cd%\PackageTool
cd ../
set ProjectDir=C:\work\xverse\XVerseStudio
set ProjectName=XLand
set ProjectBranch=init_ver
set ProjectBaseVersion=1.0
set ProjectBranchVersion=1
set TargetPlatform=Win64
set TranlateType=Release
set "DeleteOldCache=true"
set "AutoUpload=true"
set GameConfigurations=Development
set ProjectOutput=D:\XVerseCode\ProjectOutput
set "ProjectOutPutName="
set EngineDir=
set AllParam=%*
set CurrentParam=%AllParam%
::echo all: %AllParam%
set "CurrentParamIndex=1"
call :GetBuildTime
call :ReadParams
call :GetCurrentBranch
call :GetBranchVersion
call :CreateDateDir
call :PrintParams
call :BuildProject
if not exist "%OutputWin64Dir%\WindowsNoEditor\%ProjectName%.exe" (
	echo BuildXLand_Win did exist project %OutputWin64Dir%\WindowsNoEditor\%ProjectName%.exe
	goto :Exit
)

call :CopyFiles
call :WriteCommitInfo
call :CompresseProject
call :UploadToLocalServer
call :UploadToCOS
goto :Exit

:ReadParams
echo BuildXLand_Win Start ReadParams...
rem ./BuildProject.bat -projectDir=D:\XStudio -tranlateType=Dev -engineDir=d:/ -gameConfigurations=Development
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
::echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	echo param.!CurrentParam!
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam%!") do ( 
		::echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam%!") do ( 
			::echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-projectName" (
			    set ProjectName=!Value!
			)
			
			if "!Key!"=="-projectDir" (
			    set ProjectDir=!Value!
			)
			
			if "!Key!"=="-targetPlatform" (
			    set TargetPlatform=!Value!
			)
			
			if "!Key!"=="-uploadCos" (
				set UploadCos=!Value!
			)
			
			if "!Key!"=="-deleteOldCache" (
				set DeleteOldCache=!Value!
			)
			
			if "!Key!"=="-gameConfigurations" (
				set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-tranlateType" (
			    set TranlateType=!Value!
			)
						
			if "!Key!"=="-autoUpload" (
				set AutoUpload=!Value!
			)
		)

		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams

:PrintParams
echo BuildXLand_Win Start PrintParams... 
echo BuildXLand_Win ProjectName=%ProjectName%
echo BuildXLand_Win ProjectDir=%ProjectDir%
echo BuildXLand_Win OutputWin64Dir=%OutputWin64Dir%
ECHO BuildXLand_Win ProjectLastestCommit=%LastestVer%
ECHO BuildXLand_Win ProjectBranch=%ProjectBranch%
ECHO BuildXLand_Win ProjectBranchVersion=%ProjectBranchVersion%
echo BuildXLand_Win PackageToolDir=%PackageToolDir%
echo BuildXLand_Win Branch=%ProjectBranch%
echo BuildXLand_Win Branch=%UploadCos%
echo BuildXLand_Win EngineDir=%EngineDir%
goto :eof


:BuildProject
echo BuildXLand_Win Start BuildProject...
rem -nocompileeditor
cd %PackageToolDir% 
set BuildProjectCmd=%PackageToolDir%\BuildProject.bat -projectDir=%ProjectDir% -projectName=%ProjectName% -packageToolDir=%PackageToolDir% -targetPlatform=%TargetPlatform% -tranlateType=%TranlateType% -gameConfigurations=%GameConfigurations% -outPutDir=%ProjectOutput% -outPutName=%ProjectOutPutName%
call %BuildProjectCmd%
goto :eof

:GetEngineDir
echo BuildXLand_Win Start GetEngineDir...
set "EngineDir="
set "SearchEngineDir="
set "DefineEngineDir="
::set "DefineEngineDir=D:\UEEngineWork\UEBuildOutPut\XVerseEngine-20220812-100933"
set "EngineSearchDir=D:\UEEngineWork\UEBuildOutPut"
cd %EngineSearchDir%
for /f "tokens=4 delims= " %%a in ('dir /AD XVerseEngine-* ^| findstr XVerseEngine') do set "SearchEngineDir=%%a"
::echo Search SearchEngineDir=%SearchEngineDir%
if "%DefineEngineDir%"=="" (
set "EngineDir=%EngineSearchDir%\%SearchEngineDir%"
) else (
set "EngineDir=%DefineEngineDir%"
)

goto :eof

:GetBuildTime
echo BuildXLand_Win Start GetBuildTime...
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do (
	echo BuildXLand_Win CurrentTime=%%a
	set CurrentTime=%%a 
)
for /f %%a in ("%CurrentTime%") do set YSTD=%%a
goto :eof

:CreateDateDir
echo BuildXLand_Win Start CreateDateDir...
set ProjectOutputName=%ProjectName%-%YSTD%
set "OutputWin64Dir=%ProjectOutput%\%ProjectOutputName%"
setx CreatorOutputEnv %OutputWin64Dir%
goto :eof


:GetCurrentBranch
cd %ProjectDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:GetBranchVersion
cd %ProjectDir%
set BranchVersionCommand=git rev-list --count --first-parent %ProjectBranch%
echo BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
SET ProjectBranchVersion=%%A
)
goto :eof

:Exit
echo BuildXLand_Win Exit...
pause
goto :eof

