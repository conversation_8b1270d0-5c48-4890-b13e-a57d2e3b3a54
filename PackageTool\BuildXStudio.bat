setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
@echo off
cd ../
set PackageToolDir=%cd%\PackageTool
set ProjectDir=C:\work\xverse\XVerseStudio
cd %PackageToolDir%
set ProjectName=XStudio
set EngineBranch=xstudio
set ProjectBranch=dev
set BuildBranchName=dev
set ProjectBaseVersion=1.0
set ProjectBranchVersion=1
set TargetPlatform=Win64
set TranlateType=Release
set GameConfigurations=Development
set "DeleteOldCache=true"
set "ResetLocalBranch=false"
set "AutoUpload=true"
set "EmbedExit=false"
set "ForceExit=false"
set "IncludeXCapture=true"
set "EnableNotify=false"
set "Remarks="
set "ExRawParam="
set CosCliHome=C:\work\xverse\PackageTools\CosTools
set ProjectOutput=C:\work\xverse\ProjectOutput
set "ProjectOutputName="
set XCaptureIdePath=
set EngineSearchDir=C:\work\xverse\EngineOutput
set "DefineEngineDir="
::set "DefineEngineDir=D:\UEEngineWork\UEBuildOutPut\XVerseEngine-20220812-100933"
set "ProjectBuildUrl="
set AllParam=%*
set CurrentParam=%AllParam%
set "CurrentParamIndex=1"
echo BuildXStudio Receive Params: %AllParam%
echo BuildXStudio Start ReadParams...

call :ReadParams
call :GetBuildTime
call :DeleteOldProjectCache
call :GetCurrentBranch
call :SwitchBranch
if "%ForceExit%"=="true" (
 goto :ExitFail
)
call :GetBranchVersion
call :CreateDateDir
call :ReadXCaptureConfig
call :PrintParams
if "%IncludeXCapture%"=="true" (
 call :BuildXCapture
)


rem call :CopyProjectFile
call :ReadXCaptureConfig
call :CheckXCaptureBuildResult
if "%ForceExit%"=="true" (
 echo BuildXCapture Result Error...
 goto :ExitFail
)
call :CompileXStudio
call :CheckXStudioBuildResult
if "%ForceExit%"=="true" (
 echo BuildXStudio Result Error...
 goto :ExitFail
)
goto :Exit

:ReadParams
rem echo Start ReadParams...
rem ./BuildXStudio.bat -projectDir=D:\XStudio -packageToolDir=D:/ -projectName=XStudio -targetPlatform=Win64 -deleteOldCache=false -tranlateType=Dev -engineDir=d:/ -gameConfigurations=Development -outPutDir=d:/ -outPutName=xstudio-123 -autoUpload=false -embedExit=false -branchName=dev -exRawParam="-d=1"
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	echo CurrentParamContent.!CurrentParam!
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam!") do ( 
		echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam!") do ( 
			echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-projectName" (
			    set ProjectName=!Value!
			)
			
			if "!Key!"=="-projectDir" (
			    set ProjectDir=!Value!
			)
			
			if "!Key!"=="-packageToolDir" (
			    set PackageToolDir=!Value!
			)
			
			if "!Key!"=="-targetPlatform" (
			    set TargetPlatform=!Value!
			)
			
			if "!Key!"=="-tranlateType" (
			    set TranlateType=!Value!
			)
			
			if "!Key!"=="-deleteOldCache" (
				set DeleteOldCache=!Value!
			)
			
			if "!Key!"=="-engineDir" (
				set DefineEngineDir=!Value!
			)
			
			if "!Key!"=="-engineSearchDir" (
				set EngineSearchDir=!Value!
			)
			if "!Key!"=="-gameConfigurations" (
				set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-outPutDir" (
				set ProjectOutput=!Value!
			)
			
			if "!Key!"=="-outPutName" (
				set ProjectOutputName=!Value!
			)
			
			if "!Key!"=="-autoUpload" (
				set AutoUpload=!Value!
			)
			
			if "!Key!"=="-embedExit" (
				set EmbedExit=!Value!
			)
			
			if "!Key!"=="-branchName" (
				set BuildBranchName=!Value!
			)
			
			if "!Key!"=="-cosCliHome" (
				set CosCliHome=!Value!
			)

			if "!Key!"=="-enableNotify" (
				set EnableNotify=!Value!
			)			
			if "!Key!"=="-includeXCapture" (
				set IncludeXCapture=!Value!
			)
						
			if "!Key!"=="-resetLocalBranch" (
				set ResetLocalBranch=!Value!
			)

			if "!Key!"=="-remarks" (
				set Remarks=!Value!
			)

			if "!Key!"=="-engineBranch" (
				set EngineBranch=!Value!
			)

			if "!Key!"=="-projectBuildUrl" (
				set ProjectBuildUrl=!Value!
			)
						
			if "!Key!"=="-exRawParam" (
				set ExRawParam=!CurrentParam:~12!
			)
		)
		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams


:ReadXCaptureConfig
set FixedProjectBranch=%BuildBranchName:/=_%
set FixedEngineBranch=%EngineBranch:/=_%
set XCaptureConfigFileName=XCaptureConfig_%FixedEngineBranch%_%FixedProjectBranch%.ini
echo BuildXStudio Start ReadXCaptureConfig...%PackageToolDir%\Cache\%XCaptureConfigFileName%,IncludeXCapture=%IncludeXCapture%
for /f "eol=* tokens=*" %%i in (%PackageToolDir%\Cache\%XCaptureConfigFileName%) do (
	set LineContent=%%i
	echo ReadXCaptureConfig Line="!LineContent!"
	for /f "tokens=1 delims==" %%k  in ("!LineContent%!") do ( 
		::echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%v  in ("!LineContent%!") do ( 
			::echo param_value=%%k	
			set "Value=%%v"
			if "!Key!"=="LastIdePath" (
			    set XCaptureIdePath=!Value!
			)
			
			if "!Key!"=="LastCaptureVersion" (
			    set LastCaptureVersion=!Value!
			)
		)
	)
)
goto :eof



:PrintParams
echo BuildXStudio Start PrintLog...
echo YSTD=%YSTD%
echo ProjectName=%ProjectName%
echo ProjectDir=%ProjectDir%
echo ProjectOutput=%ProjectOutput%
echo ProjectOutputName=%ProjectOutputName%
echo OutputWin64Dir=%OutputWin64Dir%
echo PackageToolDir=%PackageToolDir%
echo XCaptureIdePath=%XCaptureIdePath%
echo EngineSearchDir=%EngineSearchDir%
echo DefineEngineDir=%DefineEngineDir%
echo ProjectBranch=%ProjectBranch%
echo BuildBranchName=%BuildBranchName%
echo ProjectBaseVersion=%ProjectBaseVersion%
echo BranchCommitVersion=%ProjectBranchVersion%

goto :eof

:CopyProjectFile
echo BuildXStudio start SetProjectFile...
::remove old files
del %ProjectDir%\Source\*.Target.cs
del %ProjectDir%\Source\XVerse\*.cs
del %ProjectDir%\Source\XVerse\*.cpp
RD /S/Q %ProjectDir%\Config\Windows
RD /S/Q %ProjectDir%\Config\Linux
RD /S/Q %ProjectDir%\Config\Android
::copy project files
echo BuildXStudio start copy uproject=%ProjectDir%\Script\Projects\%ProjectName%\%ProjectName%.uproject to %ProjectDir%\
xcopy /E/H/Y %ProjectDir%\Script\Projects\%ProjectName%\* %ProjectDir%\
::copy %ProjectDir%\Script\Projects\%ProjectName%\%ProjectName%.uproject  %ProjectDir%\
::copy %ProjectDir%\Script\Projects\%ProjectName%\Source\*.Target.cs %ProjectDir%\Source\
::copy %ProjectDir%\Script\Projects\%ProjectName%\Source\XVerse\*.cs %ProjectDir%\Source\XVerse\
goto :eof

:DeleteOldProjectCache
echo BuildXStudio Start DeleteOldProjectCache Flag=%DeleteOldCache%
if "%DeleteOldCache%"=="true" (
echo Start DeleteOldProjectCache...
cd %ProjectDir%
for /f "tokens=*" %%a in ('dir /s /b /ad Plugins') do (
        if "%%~nxa"=="Intermediate" (  
            @echo remove %%a
			rd /q /s "%%a"
        )
)

rd /q /s "%cd%Intermediate\Build\BuildRules"

git clean -f -x -d
)

goto :eof

:BuildXCapture
echo BuildXStudio Start BuildXCapture...
set XCaptureName=XCapture
set XCaptureTargetPlatform=Linux
cd %PackageToolDir%
set FixedProjectBranch=%BuildBranchName:/=_%
set BuildXCaptureCmd=%PackageToolDir%\BuildXCapture.bat -projectBuildUrl=%ProjectBuildUrl% -projectDir=%ProjectDir% -projectName=%XCaptureName% -engineDir=%DefineEngineDir% -engineSearchDir=%EngineSearchDir% -packageToolDir=%PackageToolDir% -targetPlatform=%XCaptureTargetPlatform% -tranlateType=%TranlateType% -gameConfigurations=%GameConfigurations% -outPutDir=%ProjectOutput% -branchName=%BuildBranchName% -engineBranch=%EngineBranch% -deleteOldCache=%DeleteOldCache% -cosCliHome=%CosCliHome% -uploadCos=true -resetLocalBranch=%ResetLocalBranch% -enableSwitchBranch=false -enableNotify=%EnableNotify% -remarks=%Remarks%
set CurrentXCaptureVersion=%FixedProjectBranch%_%ProjectBranchVersion%_%TranlateType%
echo BuildXStudio BuildXCapture Check XCaptureVersion Current=%CurrentXCaptureVersion%, Last=%LastCaptureVersion%
if "%LastCaptureVersion%"=="%CurrentXCaptureVersion%" (
echo BuildXStudio BuildXCapture  No Need Rebuild Xcapture
) else (
echo BuildXStudio BuildXCapture  Start Build XCaptrue..

echo BuildXStudio BuildXCaptureCmd %BuildXCaptureCmd%
call %BuildXCaptureCmd%
)
goto :eof

:CheckXCaptureBuildResult
echo BuildXStudio CheckXCaptureBuildResult...%IncludeXCapture%
set FixedProjectBranch=%BuildBranchName:/=_%
if "%IncludeXCapture%"=="false" (
echo BuildXStudio CheckXCaptureBuildResult No CheckResult
) else (
set CurrentXCaptureVersion=%FixedProjectBranch%_%ProjectBranchVersion%_%TranlateType%
echo BuildXStudio CheckXCaptureBuildResult Check XCaptureVersion Current=%CurrentXCaptureVersion%, Last=%LastCaptureVersion%
if "%LastCaptureVersion%"=="%CurrentXCaptureVersion%" (
echo BuildXStudio CheckXCaptureBuildResult Success
) else (
echo BuildXStudio CheckXCaptureBuildResult Fail
set "ForceExit=true"
)
)
goto :eof

:CheckXStudioBuildResult
echo BuildXStudio CheckXStudioBuildResult...
if exist %PackageToolDir%\Cache\%ProjectName%.package (
	echo BuildXStudio Success
	set "ForceExit=false"
) else (
	echo BuildXStudio Fail
	set "ForceExit=true"
)

goto :eof

:CompileXStudio
echo BuildXStudio Start BuildXStudio...
rem -nocompileeditor
del %PackageToolDir%\Cache\%ProjectName%.package
cd %PackageToolDir% 
set BuildXStudioCmd=%PackageToolDir%\BuildProject.bat -projectBuildUrl=%ProjectBuildUrl% -projectName=%ProjectName% -projectDir=%ProjectDir% -outPutDir=%ProjectOutput% -targetPlatform=%TargetPlatform% -packageToolDir=%PackageToolDir% -gameConfigurations=%GameConfigurations% -enableSwitchBranch=false -deleteOldCache=%DeleteOldCache% -engineDir=%DefineEngineDir% -engineSearchDir=%EngineSearchDir% -branchName=%BuildBranchName% -engineBranch=%EngineBranch% -tranlateType=%TranlateType% -resetLocalBranch=%ResetLocalBranch% -enableNotify=%EnableNotify% -remarks=%Remarks% -includeMountSamba=true
echo BuildXStudio BuildXStudioCmd=%BuildXStudioCmd%
call %BuildXStudioCmd%
goto :eof

:GetBuildTime
echo BuildXStudio Start GetBuildTime...
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do (
	echo CurrentTime=%%a
	set CurrentTime=%%a 
)
for /f %%a in ("%CurrentTime%") do set YSTD=%%a
goto :eof

:CreateDateDir
echo BuildXStudio Start CreateDateDir...
if "%ProjectOutputName%"=="" (
	set ProjectOutputName=%ProjectName%-%YSTD%

)
set "OutputWin64Dir=%ProjectOutput%\%ProjectOutputName%"
mkdir %OutputWin64Dir%
setx CreatorOutputEnv %OutputWin64Dir%
goto :eof

:GetCurrentBranch
rem echo BuildXStudio GetCurrentBranch... 
cd %ProjectDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:SwitchBranch
echo BuildXStudio SwitchBranch... 
echo BuildXStudio SwitchBranch CurrentBranch=%ProjectBranch%,BuildBranchName=%BuildBranchName%
if "%BuildBranchName%"=="" (
  set "BuildBranchName=%ProjectBranch%"
)
echo BuildXStudio SwitchBranch Use BranchName=%BuildBranchName%
cd %ProjectDir%
call :GetCurrentBranch
if "%ResetLocalBranch%"=="true" (
	echo BuildXStudio Git Reset Local Cache...
	git reset --hard origin/%ProjectBranch%
	git checkout *
	git rebase --abort
	git merge --abort

)
if %BuildBranchName%==%ProjectBranch% (
echo BuildXStudio No Need SwitchBranch(Current Branch Is %BuildBranchName%)
) else (
git fetch
git checkout %BuildBranchName%
)
call :GetCurrentBranch
echo BuildBranchName=%BuildBranchName% CurrentBranch=%ProjectBranch%
if %BuildBranchName%==%ProjectBranch% (
echo BuildXStudio SwitchBranch Success %BuildBranchName%

git pull
git log -2
) else (
echo SwitchBranch Fail OldBranch=%ProjectBranch%, %BuildBranchName%
set "ForceExit=true"
)
goto :eof

:GetBranchVersion
echo BuildXStudio GetBranchVersion...
cd %ProjectDir%
set BranchVersionCommand=git rev-list --count --first-parent %ProjectBranch%
echo BuildXStudio BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
SET ProjectBranchVersion=%%A
)
goto :eof


:Exit
echo BuildXStudio Exit...
exit 0
goto :eof

:ExitFail
echo BuildXStudio ExitFail...
exit /b 1
