setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
rem @echo off
cd ../
set PackageToolDir=%cd%\PackageTool
set ProjectDir=C:\work\xverse\xmit
set ProjectName=xmit
cd %PackageToolDir%
set ProjectBranch=dev
set BuildBranchName=
set ProjectBaseVersion=1.0
set ProjectBranchVersion=1
set TargetPlatform=Win64
set TranlateType=Release
set GameConfigurations=Development
set "DeleteOldCache=true"
set "AutoUpload=true"
set "ForceExit=false"
set "EnableNotify=false"
set ProjectOutput=C:\work\xverse\ProjectOutput
set "ProjectOutputName="

set AllParam=%*
set CurrentParam=%AllParam%
set "CurrentParamIndex=1"
echo BuildXmmit Receive Params: %AllParam%
echo BuildXmmit Start ReadParams...

call :ReadParams
call :GetBuildTime
call :GetCurrentBranch
rem call :SwitchBranch
if "%ForceExit%"=="true" (
 goto :Exit
)
call :GetBranchVersion
call :GetLastestCommitMessage
call :CreateDateDir
call :PrintParams
rem call :DeleteOldProjectCache
call :CompileXmit
goto :Exit
echo %TargetPlatform%| findstr Linux >nul && (
	if not exist "%OutputWin64Dir%\LinuxNoEditor\%ProjectName%.sh" (
		echo BuildXmit did exist project %OutputWin64Dir%\LinuxNoEditor\%ProjectName%.sh
		goto :Exit
	)
)

echo %TargetPlatform%| findstr Win64 >nul && (
	if not exist "%OutputWin64Dir%\WindowsNoEditor\%ProjectName%.exe" (
		echo BuildXmit did exist project %OutputWin64Dir%\WindowsNoEditor\%ProjectName%.exe
		goto :Exit
	)
)
echo %TargetPlatform%| findstr Android >nul && (
	if not exist "%OutputWin64Dir%\Android_ETC2" (
		echo BuildXmit did exist project Android %OutputWin64Dir%\Android_ETC2
		goto :Exit
	)
)
call :CopyFiles
call :WriteCommitInfo
call :UploadSymbol
call :CompresseProject
call :UploadProject
goto :Exit

:ReadParams
rem echo Start ReadParams...
rem ./BuildXmit.bat -projectDir=D:\XStudio -packageToolDir=D:/ -projectName=XStudio -targetPlatform=Win64 -deleteOldCache=false -tranlateType=Dev -engineDir=d:/ -gameConfigurations=Development -outPutDir=d:/ -outPutName=xstudio-123 -autoUpload=false -embedExit=false -branchName=dev -exRawParam="-d=1"
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
rem echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	rem echo CurrentParamContent.!CurrentParam!
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam!") do ( 
		rem echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam!") do ( 
			rem echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-projectName" (
			    set ProjectName=!Value!
			)
			
			if "!Key!"=="-projectDir" (
			    set ProjectDir=!Value!
			)
			
			if "!Key!"=="-packageToolDir" (
			    set PackageToolDir=!Value!
			)
			
			if "!Key!"=="-targetPlatform" (
			    set TargetPlatform=!Value!
			)
			
			if "!Key!"=="-tranlateType" (
			    set TranlateType=!Value!
			)
			
			if "!Key!"=="-deleteOldCache" (
				set DeleteOldCache=!Value!
			)
			
			if "!Key!"=="-engineDir" (
				set DefineEngineDir=!Value!
			)
			
			if "!Key!"=="-engineSearchDir" (
				set EngineSearchDir=!Value!
			)
			if "!Key!"=="-gameConfigurations" (
				set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-outPutDir" (
				set ProjectOutput=!Value!
			)
			
			if "!Key!"=="-outPutName" (
				set ProjectOutputName=!Value!
			)
			
			if "!Key!"=="-autoUpload" (
				set AutoUpload=!Value!
			)
			
			if "!Key!"=="-branchName" (
				set BuildBranchName=!Value!
			)
			
			if "!Key!"=="-enableNotify" (
				set EnableNotify=!Value!
			)
			
		)
		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams

:PrintParams
echo BuildXmit Start PrintLog...
echo ProjectName=%ProjectName%
echo ProjectDir=%ProjectDir%
echo ProjectOutput=%ProjectOutput%
echo ProjectOutputName=%ProjectOutputName%
echo OutputWin64Dir=%OutputWin64Dir%
echo PackageToolDir=%PackageToolDir%
echo ProjectBranch=%ProjectBranch%
echo ProjectBaseVersion=%ProjectBaseVersion%
echo BranchCommitVersion=%ProjectBranchVersion%
echo LastestVer=%LastestVer%
echo ProjectCommitInfo=%ProjectCommitInfo%

goto :eof

:CopyProjectFile
echo BuildXmit start SetProjectFile...
::remove old files
del %ProjectDir%\Source\*.Target.cs
del %ProjectDir%\Source\XVerse\*.cs
del %ProjectDir%\Source\XVerse\*.cpp
RD /S/Q %ProjectDir%\Config\Windows
RD /S/Q %ProjectDir%\Config\Linux
RD /S/Q %ProjectDir%\Config\Android
::copy project files
echo BuildXmit start copy uproject=%ProjectDir%\Script\Projects\%ProjectName%\%ProjectName%.uproject to %ProjectDir%\
xcopy /E/H/Y %ProjectDir%\Script\Projects\%ProjectName%\* %ProjectDir%\
rem set CopyUprojectCmd=copy %ProjectDir%\Script\Projects\%ProjectName%\%ProjectName%.uproject  %ProjectDir%\%ProjectName%.uproject
rem echo CopyUprojectCmd=%CopyUprojectCmd%
rem call %CopyUprojectCmd%
rem copy -rf %ProjectDir%\Script\Projects\%ProjectName%\Source %ProjectDir%\Source\
rem copy %ProjectDir%\Script\Projects\%ProjectName%\Source\XVerse\*.cs %ProjectDir%\Source\XVerse\
goto :eof

:DeleteOldProjectCache
echo BuildXmit Start DeleteOldProjectCache Flag=%DeleteOldCache%
if "%DeleteOldCache%"=="true" (
echo Start DeleteOldProjectCache...

git clean -f -x -d
)

goto :eof

:CompileXmit
echo BuildXmit Start CompileXmit...
cd %ProjectDir%\script\compilation\win
call gen_vs_proj.bat
echo BuildXmit CompileXmit Success...
goto :eof

:WriteCommitInfo
echo BuildXmit Start WriteCommitInfo...
cd %ProjectDir%
echo %TargetPlatform%| findstr Win64 >nul && (
  
echo "Game: "%LastestVer% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
echo "ProjectCommitId: "%LastestVer% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
rem echo "ProjectCommitInfo: "%ProjectCommitInfo% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
echo [/Script/ProjectItem.XVProjectManager] > %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo Version=%ProjectBranch%-%ProjectBaseVersion%.%ProjectBranchVersion% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo InnerVersion=%TranlateType% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
rem echo ProjectCommitInfo=%ProjectCommitInfo% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo ProjectCommitId=%LastestVer% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
echo IdePath=%XCaptureIdePath% >> %OutputWin64Dir%\WindowsNoEditor\%ProjectName%\Saved\Config\WindowsNoEditor\XVerseProject.ini
)

echo %TargetPlatform%| findstr Linux >nul && (

echo "Game: "%LastestVer% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
echo "ProjectCommitId: "%LastestVer% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
rem echo "ProjectCommitInfo: "%ProjectCommitInfo% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\XCreatorVersion.ini
echo [/Script/ProjectItem.XVProjectManager] > %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo Version=%ProjectBranch%-%ProjectBaseVersion%.%ProjectBranchVersion%>> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo InnerVersion=%TranlateType% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
rem echo ProjectCommitInfo=%ProjectCommitInfo% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo ProjectCommitId=%LastestVer% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
echo IdePath=%XCaptureIdePath% >> %OutputWin64Dir%\LinuxNoEditor\%ProjectName%\Saved\Config\LinuxNoEditor\XVerseProject.ini
)

goto :eof

:CompresseProject
echo BuildXmit start CompresseProject...%AutoUpload%
set ArchZipName=%ProjectName%_%TranlateType%.zip
if "%AutoUpload%"=="true" (
echo BuildXmit CompresseProject Inging...
echo BuildXmit go dir %ProjectOutput%
cd %ProjectOutput%
echo BuildXmit source Dir=%OutputWin64Dir%,dest file=%ArchZipName%
bz c %ArchZipName% %OutputWin64Dir%\WindowsNoEditor %OutputWin64Dir%\LinuxNoEditor %OutputWin64Dir%\Android_ETC2
)
echo BuildXmit End CompresseProject...
goto :eof

:UploadProject
echo BuildXmit start UploadProject...%AutoUpload%
set NewProjectBranch=%ProjectBranch:/=_%
set UploadDestPath=\\CreatorSamba\XverseCreator\%ProjectName%\%TargetPlatform%\%ProjectOutputName%_%NewProjectBranch%_%ProjectBaseVersion%.%ProjectBranchVersion%
if "%AutoUpload%"=="true" (
cd %ProjectOutput%
echo BuildXmit UploadProject Inging...
mkdir %UploadDestPath%
copy %ArchZipName% %UploadDestPath%
echo BuildXmit "uploadProject success %UploadDestPath%\%ArchZipName%"
)
echo BuildXmit End Upload...
if "%EnableNotify%"=="true" (
set SendMsgProxyContent=%ProjectBranchVersion%
rem sen message notify
call %PackageToolDir%\SendMsgProxy.bat %ProjectName% %UploadDestPath% %SendMsgProxyContent% %TranlateType% %BuildBranchName%
)

goto :eof


:UploadSymbol
echo BuildXmit Start UploadSymbol...%AutoUpload%
set UploadSymbolCmd=%EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildGraph -target="Make Installed Build Win64" -script=%PackageToolDir%\PackageProjectBuild.xml -set:EmbedSymStore=true -set:XProjectDir=%OutputWin64Dir% -set:XProjectName=%ProjectName% -set:XPlatform=%TargetPlatform% -set:EmbedRemoveSymbol=false
echo UploadSymbolCmd=%UploadSymbolCmd%
call %UploadSymbolCmd%
goto :eof

:UploadSymbolWithSystem
echo BuildXmit Start UploadSymbolWithSystem...%AutoUpload%
echo BuildXmit UploadSymbolWithSystem Ing...
set PDBPathWin64=%ProjectDir%\Binaries\Win64\%ProjectName%.pdb
set PDBPathLinux=%ProjectDir%\Binaries\Linux\%ProjectName%.pdb
set PDBPathAndroid=%ProjectDir%\Binaries\Android\%ProjectName%.pdb
set PDBPathIOS=%ProjectDir%\Binaries\IOS\%ProjectName%.pdb

set PDBStorePathWin64=\\CreatorSamba\XverseCreator\%ProjectName%\Symbol\Win64\
set PDBStorePathLinux=\\CreatorSamba\XverseCreator\%ProjectName%\Symbol\Linux\
set PDBStorePathAndroid=\\CreatorSamba\XverseCreator\%ProjectName%\Symbol\Android\
set PDBStorePathIOS=\\CreatorSamba\XverseCreator\%ProjectName%\Symbol\IOS\
echo BuildXmit UploadSymbolWithSystem Mkdir

mkdir %PDBStorePathWin64%
mkdir %PDBStorePathLinux%
mkdir %PDBStorePathAndroid%
mkdir %PDBStorePathIOS%
echo BuildXmit UploadSymbolWithSystem Copy...

copy %PDBPathWin64% %PDBStorePathWin64%
copy %PDBPathLinux% %PDBStorePathLinux%
copy %PDBPathAndroid% %PDBStorePathAndroid%
copy %PDBPathIOS% %PDBStorePathIOS%
echo BuildXmit End UploadSymbolWithSystem
goto :eof

:GetBuildTime
echo BuildXmit Start GetBuildTime...
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do (
	echo CurrentTime=%%a
	set CurrentTime=%%a 
)
for /f %%a in ("%CurrentTime%") do set YSTD=%%a
goto :eof

:CreateDateDir
echo BuildXmit Start CreateDateDir...
if "%ProjectOutputName%"=="" (
	set ProjectOutputName=%ProjectName%-%YSTD%

)
set "OutputWin64Dir=%ProjectOutput%\%ProjectOutputName%"
mkdir %OutputWin64Dir%
setx CreatorOutputEnv %OutputWin64Dir%
goto :eof

:GetLastestCommitMessage
echo BuildXmit GetLastestCommitMessage=%ProjectDir%
cd %ProjectDir%
set Command=git log -1 --oneline

FOR /F "delims= " %%A IN ('%Command%') DO (
    SET LastestVer=%%A
)
FOR /F "delims=" %%A IN ('%Command%') DO (
    set ProjectCommitInfo=%%A
)
goto :eof

:GetCurrentBranch
rem echo BuildXmit GetCurrentBranch... 
cd %ProjectDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:SwitchBranch
echo BuildXmit SwitchBranch... 
echo BuildXmit SwitchBranch CurrentBranch=%ProjectBranch%,BuildBranchName=%BuildBranchName%
if "%BuildBranchName%"=="" (
  set "BuildBranchName=%ProjectBranch%"
)
echo BuildXmit SwitchBranch Use BranchName=%BuildBranchName%
cd %ProjectDir%
if %BuildBranchName%==%ProjectBranch% (
echo BuildXmit No Need SwitchBranch(Current Branch Is %BuildBranchName%)
) else (
git checkout %BuildBranchName%
)
call :GetCurrentBranch
echo BuildBranchName=%BuildBranchName% CurrentBranch=%ProjectBranch%
if %BuildBranchName%==%ProjectBranch% (
echo BuildXmit SwitchBranch Success %BuildBranchName%
git stash
git pull --rebase
git log -2
) else (
echo SwitchBranch Fail OldBranch=%ProjectBranch%, %BuildBranchName%
set "ForceExit=true"
)
goto :eof

:GetBranchVersion
echo BuildXmit GetBranchVersion...
cd %ProjectDir%
set BranchVersionCommand=git rev-list --count --first-parent %ProjectBranch%
echo BuildXmit BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
SET ProjectBranchVersion=%%A
)
goto :eof

:Exit
echo BuildXmit Exit...%EmbedExit%
pause
goto :eof
