setlocal enabledelayedexpansion
@chcp 65001>nul
@cls
@echo off
rem add hosts System32/drivers/etc/hosts  below
rem **********		CreatorSamba

rem cmdkey /add:CreatorSamba /USER:**********\xverse_creator /Pass:xverse_creator >nul
rem net use \\CreatorSamba /USER:**********\xverse_creator xverse_creator /PERSISTENT:YES >nul
pushd %~dp0
cd ../
set PackageToolDir=%cd%\PackageTool
cd ../
set ProjectDir=C:\work\xverse\XVerseStudio
cd %PackageToolDir%
set ProjectOutput=C:\work\xverse\ProjectOutput
set EngineSearchDir=C:\work\xverse\EngineOutput
set OutPutName=XVersePluginEngine
set "ReleaseEngineDir="
set "DefineEngineDir="
set TranlateType=Dev
set ProjectBranch=dev
set "BuildBranchName="
set "ForceExit=false"

set AllParam=%*
set CurrentParam=%AllParam%
set "CurrentParamIndex=1"
echo BuildXversePluginTools Receive Params: %AllParam%
echo BuildXversePluginTools Start ReadParams...
call :ReadParams
call :GetCurrentTimeYMD
call :GetCurrentTimeYMDHMS
call :GetCurrentBranch
call :SwitchBranch
if "%ForceExit%"=="true" (
 goto :Exit
)
call :GetEngineDir
call :SetEnvConfigInfo
call :PrintInfo
call :CopyEngine
::remove old plugin
call :RemoveCache
call :RemoveXBaseLib
call :RemoveXCOMM
call :RemoveArtChecker
call :RemoveXUE
call :RemoveProtoBuf
call :RemovePrefabricator

::compile
call :CompileXBaseLib
call :CompileXCOMM
call :CompileProtoBuf
call :CompileArtChecker
call :CompileXUE
call :CompilePrefabricator

::copy
call :CopyXBaseLib
call :CopyXCOMM
call :CopyProtoBuf
call :CopyArtChecker
call :CopyXUE
call :CopyCommitInfo
call :CopyConfig
call :CopyTools
call :CopyPrefabricator

::upload
call :CompresseProject
call :UploadEngine
goto :Exit


:ReadParams
rem echo Start ReadParams...
rem ./BuildXversePluginTools.bat -deleteOldCache=false -tranlateType=Dev -engineDir=d:/ -engineSearchDir=d:/engine -gameConfigurations=Development -outPutDir=d:/ -outPutName=xstudio-123"
set QCmd="tokens=%CurrentParamIndex% delims= "
set "CurrentParam="
echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set "CurrentParam=%%b"
	echo CurrentParamContent.!CurrentParam!
	for /f "tokens=1 delims==" %%k  in ("!CurrentParam!") do ( 
		echo param_key=%%k	
		set "Key=%%k"
		for /f "tokens=2 delims==" %%k  in ("!CurrentParam!") do ( 
			echo param_value=%%k	
			set "Value=%%k"
			if "!Key!"=="-projectName" (
			    set ProjectName=!Value!
			)
			
			if "!Key!"=="-projectDir" (
			    set ProjectDir=!Value!
			)
			
			if "!Key!"=="-packageToolDir" (
			    set PackageToolDir=!Value!
			)
			
			if "!Key!"=="-targetPlatform" (
			    set TargetPlatform=!Value!
			)
			
			if "!Key!"=="-tranlateType" (
			    set TranlateType=!Value!
			)
			
			if "!Key!"=="-deleteOldCache" (
				set DeleteOldCache=!Value!
			)
			
			if "!Key!"=="-engineDir" (
				set DefineEngineDir=!Value!
			)
			
			if "!Key!"=="-engineSearchDir" (
				set EngineSearchDir=!Value!
			)
			if "!Key!"=="-gameConfigurations" (
				set GameConfigurations=!Value!
			)
			
			if "!Key!"=="-outPutDir" (
				set ProjectOutput=!Value!
			)
			
			if "!Key!"=="-outPutName" (
				set OutPutName=!Value!
			)
			
			if "!Key!"=="-autoUpload" (
				set AutoUpload=!Value!
			)
			
			if "!Key!"=="-embedExit" (
				set EmbedExit=!Value!
			)
			
			if "!Key!"=="-exRawParam" (
				set ExRawParam=!CurrentParam:~12!
			)
			
			if "!Key!"=="-branchName" (
				set BuildBranchName=!Value!
			)
		)
		
	)	

)
set /a "CurrentParamIndex=%CurrentParamIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadParams

:PrintInfo
echo BuildXversePluginTools Start PrintInfo..
echo CurrentTimeYMD=%CurrentTimeYMD%
echo CurrentTimeYMDHMS=%CurrentTimeYMDHMS%
echo DefineEngineDir=%DefineEngineDir%
echo ReleaseEngineDir=%ReleaseEngineDir%
echo EngineDir=%EngineDir%
echo ToolsCacheDir=%ToolsCacheDir%
echo TranlateType=%TranlateType%
echo ProjectOutput=%ProjectOutput%
goto :eof

:SetEnvConfigInfo
echo BuildXversePluginTools Start SetEnvConfigInfo..
cd %ProjectOutput%
set ToolsCacheDir=%ProjectOutput%\XversePluginTools
set OutPutProjectDirName=%OutPutName%-%CurrentTimeYMD%
set DestXVersePluginEngin=%ProjectOutput%\%OutPutProjectDirName%
set EngineDir=%DestXVersePluginEngin%
rmdir /S /Q %ToolsCacheDir%
goto :eof

:CopyEngine
echo BuildXversePluginTools Start CopyEngine..
if exist %ReleaseEngineDir% (

    if exist %DestXVersePluginEngin% (
        echo BuildXversePluginTools "Already exist Engine "%DestXVersePluginEngin%
    ) else (
        echo BuildXversePluginTools "Begin Copy "%ReleaseEngineDir% to %DestXVersePluginEngin%
        mkdir %DestXVersePluginEngin%
        robocopy /E %ReleaseEngineDir% %DestXVersePluginEngin%
    )
) else (
    echo BuildXversePluginTools "You must be build Engine first"
    pause
    goto :Exit
)

goto :eof

:CompileXBaseLib
echo BuildXversePluginTools Start CompileXBaseLib...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XBaseLib\XBaseLib.uplugin -CppStd=Cpp17 -Package=%ToolsCacheDir%\XBaseLib -VS2019 -NoDeleteHostProject -TargetPlatforms=Win64+Linux
goto :eof

:CompilePrefabricator
echo BuildXversePluginTools Start CompilePrefabricator...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\Marketplace\\Prefabricator\Prefabricator.uplugin -CppStd=Cpp17 -Package=%ToolsCacheDir%\Prefabricator -VS2019 -NoDeleteHostProject -TargetPlatforms=Win64+Linux
goto :eof

:CompileXCOMM
echo BuildXversePluginTools Start CompileXCOMM...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XCOMM\XCOMM.uplugin -Dependency=%ProjectDir%\Plugins\XBaseLib\XBaseLib.uplugin -Dependency=%ProjectDir%\Plugins\Marketplace\Protobuf\Protobuf.uplugin -CppStd=Cpp17 -TargetPlatforms="Win64+Linux" -Package=%ToolsCacheDir%\XCOMM -VS2019 -NoDeleteHostProject
goto :eof


:CompileArtChecker
echo BuildXversePluginTools Start Compile XverseArtChecker...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XDeps\XverseTurboEditing\XverseArtChecker\XverseArtChecker.uplugin -CppStd=Cpp17 -TargetPlatforms="Win64+Linux" -Package=%ToolsCacheDir%\XverseTurboEditing\XverseArtChecker -VS2019 -NoDeleteHostProject
goto :eof

:CompileXUE
echo BuildXversePluginTools Start Compile XUE...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\XUE\XUE.uplugin -Dependency=%ProjectDir%\Plugins\XBaseLib\XBaseLib.uplugin -Dependency=%ProjectDir%\Plugins\XCOMM\XCOMM.uplugin -Dependency=%ProjectDir%\Plugins\Marketplace\Protobuf\Protobuf.uplugin -Dependency=%ProjectDir%\Plugins\XDeps\XverseTurboEditing\XverseArtChecker\XverseArtChecker.uplugin -CppStd=Cpp17 -NoDeleteHostProject -TargetPlatforms="Win64+Linux" -Package=%ToolsCacheDir%\XUE -VS2019
goto :eof

:CompileProtoBuf
echo BuildXversePluginTools Start Compile ProtoBuf...
call %EngineDir%\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin=%ProjectDir%\Plugins\Marketplace\Protobuf\Protobuf.uplugin -CppStd=Cpp17 -NoDeleteHostProject -TargetPlatforms=Win64+Linux -Package=%ToolsCacheDir%\ProtoBuf -VS2019
goto :eof

:RemoveCache
echo BuildXversePluginTools RemoveCache...
cd %ProjectDir%
git clean -dxf
goto :eof

:RemoveXBaseLib
echo BuildXversePluginTools RemoveXBaseLib...
rd /s/q %EngineDir%\Engine\Plugins\XBaseLib
rd /s/q %ProjectDir%\Plugins\XBaseLib\Binaries
rd /s/q %ProjectDir%\Plugins\XBaseLib\Intermediate
goto :eof

:RemoveXCOMM
echo BuildXversePluginTools RemoveXCOMM...
rd /s/q %EngineDir%\Engine\Plugins\XCOMM
rd /s/q %ProjectDir%\Plugins\XCOMM\Binaries
rd /s/q %ProjectDir%\Plugins\XCOMM\Intermediate
goto :eof

:RemoveArtChecker
echo BuildXversePluginTools RemoveArtChecker...
rd /s/q %EngineDir%\Engine\Plugins\XverseTurboEditing
rd /s/q %ProjectDir%\Plugins\XDeps\XverseTurboEditing\XverseArtChecker\Binaries
rd /s/q %ProjectDir%\Plugins\XDeps\XverseTurboEditing\XverseArtChecker\Intermediate
rd /s/q %EngineDir%\Engine\Plugins\Protobuf
goto :eof

:RemoveXUE
echo BuildXversePluginTools RemoveXUE...
echo remove XUE.%EngineDir%\Engine\Plugins\XUE
rd /s/q %EngineDir%\Engine\Plugins\XUE
rd /s/q %ProjectDir%\Plugins\XUE\Binaries
rd /s/q %ProjectDir%\Plugins\XUE\Intermediate
goto :eof

:RemoveProtoBuf
echo BuildXversePluginTools RemoveProtoBuf...
rd /s/q %EngineDir%\Engine\Plugins\Protobuf
rd /s/q %ProjectDir%\Plugins\Marketplace\Protobuf\Binaries
rd /s/q %ProjectDir%\Plugins\Marketplace\Protobuf\Intermediate
goto :eof

:RemovePrefabricator
echo BuildXversePluginTools RemovePrefabricator...
rd /s/q %EngineDir%\Engine\Plugins\Prefabricator
rd /s/q %ProjectDir%\Plugins\Marketplace\Prefabricator\Binaries
rd /s/q %ProjectDir%\Plugins\Marketplace\Prefabricator\Intermediate
goto :eof

::copy xbaseLib
:CopyXBaseLib
echo BuildXversePluginTools CopyXBaseLib...
robocopy /E %ToolsCacheDir%\XBaseLib\HostProject\Plugins\XBaseLib %EngineDir%\Engine\Plugins\XBaseLib\
copy  %ProjectDir%\Plugins\XBaseLib\XBaseLib.uplugin %EngineDir%\Engine\Plugins\XBaseLib\XBaseLib.uplugin
rd /s/q %EngineDir%\Engine\Plugins\XBaseLib\Source
goto :eof

::copy xcomm
:CopyXCOMM
echo BuildXversePluginTools Start CopyXCOMM..
robocopy /E %ToolsCacheDir%\XCOMM\HostProject\Plugins\XCOMM %EngineDir%\Engine\Plugins\XCOMM\
copy  %ProjectDir%\Plugins\XCOMM\XCOMM.uplugin %EngineDir%\Engine\Plugins\XCOMM\XCOMM.uplugin
rd /s/q %EngineDir%\Engine\Plugins\XCOMM\Source
goto :eof

::copy XverseArtChecker
:CopyArtChecker
echo BuildXversePluginTools Start CopyArtChecker..
robocopy /E %ToolsCacheDir%\XverseTurboEditing %EngineDir%\Engine\Plugins\XverseTurboEditing\
copy  %ProjectDir%\XDeps\XverseTurboEditing\XverseArtChecker\XverseArtChecker.uplugin %EngineDir%\Engine\Plugins\XverseTurboEditing\XverseArtChecker\XverseArtChecker.uplugin
rd /s/q %EngineDir%\Engine\Plugins\XverseTurboEditing\XverseArtChecker\Source
goto :eof

::copy xue
:CopyXUE
echo BuildXversePluginTools Start CopyXUE..
robocopy /E %ToolsCacheDir%\XUE\HostProject\Plugins\XUE %EngineDir%\Engine\Plugins\XUE\
copy  %ProjectDir%\Plugins\XUE\XUE.uplugin %EngineDir%\Engine\Plugins\XUE\XUE.uplugin
rd /s/q %EngineDir%\Engine\Plugins\XUE\Source
goto :eof

::copy protobuf
:CopyProtoBuf
echo BuildXversePluginTools Start CopyProtoBuf..
robocopy /E %ToolsCacheDir%\Protobuf\HostProject\Plugins\Protobuf %EngineDir%\Engine\Plugins\Protobuf\
copy  %ProjectDir%\Plugins\Marketplace\Protobuf\ProtoBuf.uplugin %EngineDir%\Engine\Plugins\Protobuf\ProtoBuf.uplugin
rd /s/q %EngineDir%\Engine\Plugins\Protobuf\Source
goto :eof

:CopyPrefabricator
echo BuildXversePluginTools Start CopyPrefabricator..
robocopy /E %ToolsCacheDir%\Prefabricator\HostProject\Plugins\Prefabricator %EngineDir%\Engine\Plugins\Prefabricator\
copy  %ProjectDir%\Plugins\Marketplace\Prefabricator\Prefabricator.uplugin %EngineDir%\Engine\Plugins\Prefabricator\Prefabricator.uplugin
rd /s/q %EngineDir%\Engine\Plugins\Prefabricator\Source
goto :eof

::XverseCommunication
:CopyConfig
echo BuildXversePluginTools Start CopyConfig..
rd /s/q %EngineDir%\Engine\Config\BaseXConsole.ini
if "%TranlateType%"=="Dev" (
	copy  %PackageToolDir%\BaseXConsole_Dev.ini %EngineDir%\Engine\Config\BaseXConsole.ini
)

if "%TranlateType%"=="Release" (
	copy  %PackageToolDir%\BaseXConsole.ini %EngineDir%\Engine\Config\BaseXConsole.ini
)
rd /s/q %EngineDir%\Engine\Config\BaseXSkinSambaCache.ini
copy  %PackageToolDir%\BaseXSkinSambaCache.ini %EngineDir%\Engine\Config\BaseXSkinSambaCache.ini
goto :eof

::git commit info
:CopyCommitInfo
echo BuildXversePluginTools Start CopyCommitInfo..
call :GetLastestCommitMessage
echo "XversePluginTools:"%LastestVer% >%EngineDir%\XversePluginToolsVersion.ini
echo "BuildTime:"%CurrentTimeYMDHMS% >> %EngineDir%\XversePluginToolsVersion.ini
goto :eof

::copy lowpoly tools
:CopyTools
echo BuildXversePluginTools Start CopyTools..
rd /s/q %EngineDir%\Engine\lowpolyApplication
set DestLowpolyToolPath=\\CreatorSamba\XverseCreator\lowpolyApplication
robocopy /E %DestLowpolyToolPath% %EngineDir%\Engine\lowpolyApplication\
goto :eof

:CompresseProject
echo BuildXversePluginTools CompresseProject..
cd %ProjectOutput%
set DestEngineZipName=%OutPutName%.zip
bz c %DestEngineZipName% %EngineDir%
goto :eof

:UploadEngine
cd %ProjectOutput%
echo BuildXversePluginTools Start UploadEngine..
set RemotePluginEngineDirName=%OutPutName%-%CurrentTimeYMDHMS%
set UploadPath=\\CreatorSamba\XverseCreator\XStudio\PluginEngine\%RemotePluginEngineDirName%
mkdir %UploadPath%
copy  %DestEngineZipName% %UploadPath%
echo "UploadEngine Success %UploadPath%\%DestEngineZipName%"
goto :eof

:GetLastestCommitMessage
echo BuildXversePluginTools Start GetLastestCommitMessage..
cd %ProjectDir%
set Command=git log -1 --oneline
FOR /F "delims=" %%A IN ('%Command%') DO (
    SET LastestVer=%%A
    GOTO :Print 
)
goto :eof


:GetCurrentBranch
rem echo BuildXversePluginTools GetCurrentBranch
cd %ProjectDir%
set CurrentBranchCommand=git branch --show-current

FOR /F %%A IN ('%CurrentBranchCommand%') DO (
	SET ProjectBranch=%%A
)
goto :eof

:SwitchBranch
echo BuildXversePluginTools SwitchBranch...
echo BuildXversePluginTools SwitchBranch CurrentBranch=%ProjectBranch%,BuildBranchName=%BuildBranchName%
if "%BuildBranchName%"=="" (
set "BuildBranchName=%ProjectBranch%"
)
echo BuildXversePluginTools SwitchBranch Use BranchName=%BuildBranchName%
cd %ProjectDir%
if %BuildBranchName%==%ProjectBranch% (
echo BuildProject No Need SwitchBranch
goto :eof
) else (
echo Start CheckOut %BuildBranchName%
git checkout %BuildBranchName%
)
call :GetCurrentBranch
echo BuildXversePluginTools BuildBranchName=%BuildBranchName% CurrentBranch=%ProjectBranch%
if %BuildBranchName%==%ProjectBranch% (
echo BuildXversePluginTools SwitchBranch Success %BuildBranchName%
git stash
git pull --rebase
git log -5
) else (
echo SwitchBranch Fail OldBranch=%ProjectBranch%, %BuildBranchName%
set "ForceExit=true"
)
goto :eof

:GetBranchVersion
cd %ProjectDir%
set BranchVersionCommand=git rev-list --count --first-parent %ProjectBranch%
echo BuildProject BranchVersionCommand=%BranchVersionCommand%
FOR /F %%A IN ('%BranchVersionCommand%') DO (
SET ProjectBranchVersion=%%A
)
goto :eof

:GetCurrentTimeYMDHMS
echo BuildXversePluginTools Start GetCurrentTimeYMDHMS..
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd-HHmmss'"') do set TmpTimestamp=%%a
for /f "tokens=* delims= " %%a in ("%TmpTimestamp%") do (
        set "CurrentTimeYMDHMS=%%a"
    )
goto :eof

:GetCurrentTimeYMD
echo BuildXversePluginTools Start GetCurrentTime..
for /f %%a in ('powershell "Get-Date -Format 'yyyyMMdd'"') do set TmpTimestamp=%%a
for /f "tokens=* delims= " %%a in ("%TmpTimestamp%") do (
        set "CurrentTimeYMD=%%a"
        call :ie
    )

:ie
if "%CurrentTimeYMD:~-1%"==" " (set "CurrentTimeYMD=%CurrentTimeYMD:~0,-1%"&goto ie) 
goto :eof

:GetEngineDir
echo BuildXversePluginTools GetEngineDir...
echo BuildXversePluginTools Start search Engine...
set "SearchEngineDir="
cd %EngineSearchDir%
for /f "tokens=4 delims= " %%a in ('dir /AD XVerseEngine-* ^| findstr XVerseEngine') do set "SearchEngineDir=%%a"
echo BuildXversePluginTools Search SearchEngineDir=%SearchEngineDir%
if "%DefineEngineDir%"=="" (
set "ReleaseEngineDir=%EngineSearchDir%\%SearchEngineDir%"
) else (
set "ReleaseEngineDir=%DefineEngineDir%"
)
goto :eof

:Exit
pause
goto :eof