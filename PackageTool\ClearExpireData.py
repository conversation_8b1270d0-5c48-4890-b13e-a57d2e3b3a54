#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import os
#from datetime import datetime
import datetime
import shutil
import xverseUtil
import ciTools

LogTag = "ClearExpireData"

class ClearExpireParam():
    def __init__(self):
        # split by ;
        self.projectName : str = None
        self.projectNameList = set()
        # split by ;
        self.projectOutPutPath : str = None
        self.projectOutPutPathList = set()
        # split by ;
        self.engineOutPutPath : str = None

        self.engineOutPutPathList = set()

        self.maxProjectSaveDays : int = 2

        self.maxEngineSaveDays : int = 2

        self.projectDir = ciTools.XCiGlobalConfig.projectSourceDir


def loadClearParam(param : ClearExpireParam):
    # project out
    param.projectOutPutPathList.add("C:\\work\\xverse\\ProjectOutput")
    param.projectOutPutPathList.add("D:\\work\\xverse\\ProjectOutput")

    param.projectOutPutPathList.add("C:\\work\\xverse\\Project5Output")
    param.projectOutPutPathList.add("D:\\work\\xverse\\Project5Output")

    param.projectOutPutPathList.add("C:\\work\\xverse\\ArtProject5Output")
    param.projectOutPutPathList.add("D:\\work\\xverse\\ArtProject5Output")

    param.projectOutPutPathList.add("C:\\work\\xverse\\ArtProjectOutput")
    param.projectOutPutPathList.add("D:\\work\\xverse\\ArtProjectOutput")


    param.projectOutPutPathList.add("C:\\work\\xverse\\PluginOutput\\XVerseEnginePlugins")
    
    # engine out
    param.engineOutPutPathList.add("C:\\work\\xverse\\EngineOutput")
    param.engineOutPutPathList.add("D:\\work\\xverse\\EngineOutput")

    param.engineOutPutPathList.add("C:\\work\\xverse\\Engine5OutPut")
    param.engineOutPutPathList.add("D:\\work\\xverse\\Engine5OutPut")

    param.engineOutPutPathList.add("C:\\work\\xverse\\ArtEngine5OutPut")
    param.engineOutPutPathList.add("D:\\work\\xverse\\ArtEngine5OutPut")

    # ProjectName
    param.projectNameList.add("XTravelDogGoogle")
    param.projectNameList.add("XStudioPlugin")
    param.projectNameList.add("XStudioPluginEngine")
    param.projectNameList.add("XStudioPIE")
    param.projectNameList.add("XVerseEngine")

    # load project name
    loadProjectName(param)

def loadProjectName(param : ClearExpireParam):
    if param.projectDir is not None and os.path.exists(param.projectDir):
        projectScriptDir = os.path.join(param.projectDir, "Script", "Projects")
        if os.path.exists(projectScriptDir):
            dirs = os.listdir(projectScriptDir)
            for dirName in dirs:
                path = os.path.join(projectScriptDir, dirName)
                if os.path.isdir(path):
                    param.projectNameList.add(dirName)
def readParam(argv, param : ClearExpireParam):
    xverseUtil.readParams(argv, param)

    # split project
    if param.projectOutPutPath is not None:
        paths = param.projectOutPutPath.split(";")
        for path in paths:
            if path is not None and len(path) > 1:
                param.projectOutPutPathList.add(path)

    # split engine
    if param.engineOutPutPath is not None:
        paths = param.engineOutPutPath.split(";")
        for path in paths:
            if path is not None and len(path) > 1:
                param.engineOutPutPathList.add(path)
    # split projectName

    if param.projectName is not None:
        paths = param.projectName.split(";")
        for path in paths:
            if path is not None and len(path) > 1:
                param.projectNameList.add(path)

def checkClearParam(param : ClearExpireParam):
    print(f"checkClearParam {param.maxEngineSaveDays}, {param.maxProjectSaveDays}")
    if param.maxEngineSaveDays < 0:
        param.maxEngineSaveDays = 0

    if param.maxProjectSaveDays < 0:
        param.maxProjectSaveDays = 0

def clearProjectOutPut(param : ClearExpireParam):
    for path in param.projectOutPutPathList:
        if not os.path.exists(path):
            xverseUtil.printLogTag(LogTag, "clearProjectOutPut error %s (projectPath not exist)"%(path))
            continue
        clearProjectOutPutInner(param, path)

def clearProjectOutPutInner(param : ClearExpireParam, projectPath):
    print("{0} clearProjectOutPutInner ProjectOutPut={1}".format(LogTag, projectPath))
    if not os.path.exists(projectPath):
        print("clearProjectOutPutInner not exists", projectPath)
        return
    file_list = os.listdir(projectPath)
    file_list.sort(reverse=True)
    if file_list is None or len(file_list) < 1:
        print("clearProjectOutPutInner did not exist any project in {}".format(projectPath))
        return
    expireTime = datetime.datetime.now() + datetime.timedelta(days=param.maxProjectSaveDays * -1)
    date_time = expireTime.strftime("%Y%m%d")
    print("{0} clearProjectOutPutInner expireTime date_time={1}".format(LogTag, date_time))
    for fname in file_list:
        fpath = os.path.join(projectPath, fname)
        fileNameArr = fname.split("-")
        miniFName = fileNameArr[0]
        if miniFName in param.projectNameList and len(fileNameArr) > 0:
            fileDate = fileNameArr[1]
            if fileDate < date_time:
                print("{0} Delete ProjectOutput={1}".format(LogTag, fpath))
                simpleFname = fname
                if os.path.isfile(fpath):
                    lastPos = fname.rfind(".")
                    simpleFname = fname[:lastPos]
                deleteDirAndZip(projectPath, simpleFname)
    print("clearProjectOutPutInner End %s"%(projectPath))

def deleteDirAndZip(dir, name):
    filepath = os.path.join(dir, "{0}.zip".format(name))
    dirpath = os.path.join(dir, "{0}".format(name))
    if os.path.exists(filepath):
        print("deleteDirAndZip Delete Zip", filepath)
        os.remove(filepath)
    try:
        if os.path.exists(dirpath):
            print("deleteDirAndZip Delete Dir", dirpath)
            shutil.rmtree(dirpath)
    except:
        print("deleteDirAndZip Delete Dir error", dirpath)

def clearEngineOutPut(param : ClearExpireParam):
    for path in param.engineOutPutPathList:
        if not os.path.exists(path):
            print("clearEngineOutPut error %s (enginePath not exist)"%(path))
            continue
        clearEngineOutPutInner(param, path)

def clearEngineOutPutInner(param : ClearExpireParam, enginePath):
    print("======================****clearEngineOutPutInner Begin****=================")
    print("{0} clearEngineOutPutInner OutPutPath={1}".format(LogTag, enginePath))
    if not os.path.exists(enginePath):
        print("clearEngineOutPutInner Path not exists", enginePath)
        return
    file_list = os.listdir(enginePath)
    file_list.sort(reverse=True)
    if file_list is None or len(file_list) < 1:
        print("clearEngineOutPutInner did not exist any engine in {}".format(enginePath))
        return
    expireTime = datetime.datetime.now() + datetime.timedelta(days=param.maxEngineSaveDays * -1)
    keepEngineMap = {}
    deleteEngineMap = {}
    branchEngineMap = {}
    date_time = expireTime.strftime("%Y%m%d")
    print("{0} clearEngineOutPutInner expireTime date_time={1}".format(LogTag, date_time))
    for f in file_list:
        fpath = os.path.join(enginePath, f)
        fileNameArr = f.split("-")
        if not f.startswith("XVerseEngine-") or len(fileNameArr) < 1:
            #print("clearEngineOutPutInner error (%s not an engine path)"%(fpath))
            continue
        fileDate = fileNameArr[1]
        engineBranch = ""
        if os.path.isdir(fpath):
            engineBranch = ReadEngineBranch(fpath)
        if fileDate > date_time:
            if os.path.isdir(fpath):
                print("BuildHelper engine not expire", f)
                keepEngineMap[f]= "not expire branch=" + engineBranch
            continue
        if os.path.isfile(fpath):
            dirName = f[:-4]
            engineDirPath = os.path.join(enginePath, dirName);
            if not os.path.exists(engineDirPath):
                print("Delete Expire Zip Engine File", dirName, engineDirPath)
                os.remove(fpath)
        else:                
            if engineBranch is None or len(engineBranch) < 1:
                engineVersionPath = os.path.join(fpath, "Engine\Version\EngineVersion.ini")
                print("engine no exist EngineVersion File",f, engineVersionPath)
                keepEngineMap[f]= "no exist EngineVersion File"
                deleteDirAndZip(enginePath, f)
                continue
            engineInstalledPath = os.path.join(fpath, "Engine\Version\EngineInstalled.ini")
            if not os.path.exists(engineInstalledPath):
                print("engine no exist EngineInstalled",f, engineInstalledPath)
                deleteEngineMap[f]= "no exist EngineInstalled branch=" + engineBranch
                deleteDirAndZip(enginePath, f)
                continue
            lockPath = os.path.join(fpath, "Engine\Version\EngineLock.ini")
            if os.path.exists(lockPath):
                print("BuildHelper engine has locked unavailable", f, lockPath)
                keepEngineMap[f]= "had been EngineLock branch=" + engineBranch
                continue
            uatPath = os.path.join(fpath, "Engine\Build\BatchFiles\RunUAT.bat")
            if not os.path.exists(uatPath):
                print("delete ClearEngineOutPut zip File", f, uatPath)
                deleteEngineMap[f]= "no exist RunUAT.bat branch=" + engineBranch
                deleteDirAndZip(enginePath, f)
                continue
            if len(engineBranch) < 1:
                deleteEngineMap[f]= "no exist branch Info branch=" + engineBranch
                deleteDirAndZip(enginePath, f)
                continue
            if not branchEngineMap.get(engineBranch):
                branchEngineMap[engineBranch] = f
                print("insert new branch",engineBranch, f)
                keepEngineMap[f]= "avaliable branch=" + engineBranch
            else:
                print("delete duplix branch engine", engineBranch, f, fpath)
                deleteEngineMap[f]= "duplix branch=" + engineBranch
                deleteDirAndZip(enginePath, f)
    if len(deleteEngineMap) > 0:
        print("===deleteEngine begin=======")
        for engine in deleteEngineMap:
            print("Delete Engine fileName={}, reason={}".format(engine, deleteEngineMap[engine]))
    if len(branchEngineMap) > 0:
        print("===branchEngine begin===")
        for engine in branchEngineMap:
            print("Branch Engine branch={}, fileName={}".format(engine, branchEngineMap[engine]))
    if len(keepEngineMap) > 0:
        print("===keepEngine begin===")
        for engine in keepEngineMap:
            print("Keep Engine fileName={}, reason={}".format(engine, keepEngineMap[engine]))
    print("clearEngineOutPutInner End %s"%(enginePath))
    print("======================****clearEngineOutPutInner End****=================")
def ReadEngineBranch(EnginePath):
    engineVersionPath = os.path.join(EnginePath, "Engine\Version\EngineVersion.ini")
    if not os.path.exists(engineVersionPath):
        return ""
    engineVersionFile = open(engineVersionPath, "r" , encoding="utf-8")
    fileBranch = ""
    for line in engineVersionFile:
        if line.startswith("Branch"):
            #print("ReadEngineBranch", engineVersionPath, line)
            lines = line.split(":")
            if len(lines) > 1:
                fileBranch = lines[1]
                fileBranch = fileBranch.strip()
    return fileBranch
if __name__ == "__main__":
    clearParam = ClearExpireParam()
    readParam(sys.argv, clearParam)
    loadClearParam(clearParam)
    ciTools.printBuildParam(clearParam, "ClearExpireParam read")
    clearProjectOutPut(clearParam)
    clearEngineOutPut(clearParam)