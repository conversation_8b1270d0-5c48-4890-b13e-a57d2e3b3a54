; This file defines the default settings for core and engine-level features
; These settings are overridden by a project's DefaultEngine.ini file and per-platform overrides
; Some of these settings can be modified from the project settings in the editor

[/Script/EngineSettings.GameMapsSettings]
GameInstanceClass=/Script/Engine.GameInstance
EditorStartupMap=/Engine/Maps/Templates/Template_Default
GameDefaultMap=/Engine/Maps/Entry
ServerDefaultMap=/Engine/Maps/Entry
GlobalDefaultGameMode="/Script/Engine.GameModeBase"
; Example for map prefix and game mode name setting
; +GameModeMapPrefixes=(Name="DM-",GameMode="/Script/GamePackage.DMGameMode")
; +GameModeClassAliases=(Name="DM",GameMode="/Script/GamePackage.DMGameMode")
LocalMapOptions=

[InstallBundleManager]
ModuleName=NullInstallBundleManager
EditorModuleName=NullInstallBundleManager

[URL]
Protocol=unreal
Name=Player
SaveExt=usa
Port=7777

[HTTP]
HttpTimeout=180
HttpConnectionTimeout=60
HttpReceiveTimeout=30
HttpSendTimeout=30

[HTTP.Curl]
bAllowSeekFunction=false

[BackgroundHttp]
; How many BackgroundHTTP tasks can be running simultaneously. Turning this number too high may cause timeouts
MaxActiveDownloads=4

; How many days we will wait to delete any found BackgroundHTTP Temp Files that haven't been claimed. Default is 259200 seconds (3 days). -1 to disable.
TempFileTimeOutSeconds=259200

; If we should delete any Temp Files in our BackgroundHTTP temp folder that don't have associated URL Mapping entries.
; This means we don't really know what URL this temp work belongs to anymore
DeleteTempFilesWithoutURLMappingEntries=true

; If we should remove URL Mapping Entries on launch for any entries that don't have a corresponding temp file
; This could be stale data from a previous run that was either never actually used, wasn't saved after the temp file was moved, etc.
RemoveURLMappingEntriesWithoutPhysicalTempFiles=true

[WebSockets.LibWebSockets]
ThreadStackSize=131072
ThreadTargetFrameTimeInSeconds=0.0333
ThreadMinimumSleepTimeInSeconds=0.0
MaxHttpHeaderData=32768

[Ping]
StackSize=1048576

[Voice]
bEnabled=false
bDuckingOptOut=true

[SlateStyle]
DefaultFontName=/Engine/EngineFonts/Roboto

[PlatformMemoryBuckets]
LargestMemoryBucket_MinGB=32
LargerMemoryBucket_MinGB=12
DefaultMemoryBucket_MinGB=8
SmallerMemoryBucket_MinGB=6
; if SmallestMemoryBucket_MinGB is set, then the engine will throw up an error and exit(0) if the device has less
SmallestMemoryBucket_MinGB=0

[/Script/Engine.Engine]
ConsoleClassName=/Script/Engine.Console
GameViewportClientClassName=/Script/Engine.GameViewportClient
LocalPlayerClassName=/Script/Engine.LocalPlayer
WorldSettingsClassName=/Script/Engine.WorldSettings
NavigationSystemClassName=/Script/NavigationSystem.NavigationSystemV1
NavigationSystemConfigClassName=/Script/NavigationSystem.NavigationSystemModuleConfig
AvoidanceManagerClassName=/Script/Engine.AvoidanceManager
PhysicsCollisionHandlerClassName=/Script/Engine.PhysicsCollisionHandler
LevelScriptActorClassName=/Script/Engine.LevelScriptActor
DefaultBlueprintBaseClassName=/Script/Engine.Actor
GameUserSettingsClassName=/Script/Engine.GameUserSettings
AIControllerClassName=/Script/AIModule.AIController
AssetManagerClassName=/Script/Engine.AssetManager
bAllowMatureLanguage=false
GameEngine=/Script/Engine.GameEngine
EditorEngine=/Script/UnrealEd.EditorEngine
UnrealEdEngine=/Script/UnrealEd.UnrealEdEngine
; Leaving these unset causes the engine to use FCoreStyle::GetDefaultFont() instead
;TinyFontName=/Engine/EngineFonts/RobotoTiny.RobotoTiny
;SmallFontName=/Engine/EngineFonts/Roboto.Roboto
;MediumFontName=/Engine/EngineFonts/Roboto.Roboto
;LargeFontName=/Engine/EngineFonts/Roboto.Roboto
;SubtitleFontName=/Engine/EngineFonts/Roboto.Roboto
WireframeMaterialName=/Engine/EngineDebugMaterials/WireframeMaterial.WireframeMaterial
DefaultMaterialName=/Engine/EngineMaterials/WorldGridMaterial.WorldGridMaterial
DefaultLightFunctionMaterialName=/Engine/EngineMaterials/DefaultLightFunctionMaterial.DefaultLightFunctionMaterial
DefaultTextureName=/Engine/EngineResources/DefaultTexture.DefaultTexture
DefaultDiffuseTextureName=/Engine/EngineMaterials/DefaultDiffuse.DefaultDiffuse
DefaultBSPVertexTextureName=/Engine/EditorResources/BSPVertex.BSPVertex
HighFrequencyNoiseTextureName=/Engine/EngineMaterials/Good64x64TilingNoiseHighFreq.Good64x64TilingNoiseHighFreq
DefaultBokehTextureName=/Engine/EngineMaterials/DefaultBokeh.DefaultBokeh
DefaultBloomKernelTextureName=/Engine/EngineMaterials/DefaultBloomKernel.DefaultBloomKernel
GeomMaterialName=/Engine/EngineDebugMaterials/GeomMaterial.GeomMaterial
DebugMeshMaterialName=/Engine/EngineDebugMaterials/DebugMeshMaterial.DebugMeshMaterial
EmissiveMeshMaterialName=/Engine/EngineMaterials/EmissiveMeshMaterial.EmissiveMeshMaterial
PreIntegratedSkinBRDFTextureName=/Engine/EngineMaterials/PreintegratedSkinBRDF.PreintegratedSkinBRDF
BlueNoiseTextureName=/Engine/EngineMaterials/BlueNoise.BlueNoise
MiniFontTextureName=/Engine/EngineMaterials/MiniFont.MiniFont
WeightMapPlaceholderTextureName=/Engine/EngineMaterials/WeightMapPlaceholderTexture.WeightMapPlaceholderTexture
LightMapDensityTextureName=/Engine/EngineMaterials/DefaultWhiteGrid.DefaultWhiteGrid
LevelColorationLitMaterialName=/Engine/EngineDebugMaterials/LevelColorationLitMaterial.LevelColorationLitMaterial
LevelColorationUnlitMaterialName=/Engine/EngineDebugMaterials/LevelColorationUnlitMaterial.LevelColorationUnlitMaterial
LightingTexelDensityName=/Engine/EngineDebugMaterials/MAT_LevelColorationLitLightmapUV.MAT_LevelColorationLitLightmapUV
ShadedLevelColorationUnlitMaterialName=/Engine/EngineDebugMaterials/ShadedLevelColorationUnlitMateri.ShadedLevelColorationUnlitMateri
ShadedLevelColorationLitMaterialName=/Engine/EngineDebugMaterials/ShadedLevelColorationLitMaterial.ShadedLevelColorationLitMaterial
RemoveSurfaceMaterialName=/Engine/EngineMaterials/RemoveSurfaceMaterial.RemoveSurfaceMaterial
VertexColorMaterialName=/Engine/EngineDebugMaterials/VertexColorMaterial.VertexColorMaterial
VertexColorViewModeMaterialName_ColorOnly=/Engine/EngineDebugMaterials/VertexColorViewMode_ColorOnly.VertexColorViewMode_ColorOnly
VertexColorViewModeMaterialName_AlphaAsColor=/Engine/EngineDebugMaterials/VertexColorViewMode_AlphaAsColor.VertexColorViewMode_AlphaAsColor
VertexColorViewModeMaterialName_RedOnly=/Engine/EngineDebugMaterials/VertexColorViewMode_RedOnly.VertexColorViewMode_RedOnly
VertexColorViewModeMaterialName_GreenOnly=/Engine/EngineDebugMaterials/VertexColorViewMode_GreenOnly.VertexColorViewMode_GreenOnly
VertexColorViewModeMaterialName_BlueOnly=/Engine/EngineDebugMaterials/VertexColorViewMode_BlueOnly.VertexColorViewMode_BlueOnly
PhysicalMaterialMaskMaterialName=/Engine/EngineDebugMaterials/PhysicalMaterialMaskMaterial.PhysicalMaterialMaskMaterial
BoneWeightMaterialName=/Engine/EngineDebugMaterials/BoneWeightMaterial.BoneWeightMaterial
ClothPaintMaterialName=/Engine/EngineDebugMaterials/ClothMaterial.ClothMaterial
ClothPaintMaterialWireframeName=/Engine/EngineDebugMaterials/ClothMaterial_WF.ClothMaterial_WF
DebugEditorMaterialName=/Engine/EngineDebugMaterials/DebugEditorMaterial.DebugEditorMaterial
InvalidLightmapSettingsMaterialName=/Engine/EngineMaterials/M_InvalidLightmapSettings.M_InvalidLightmapSettings
PreviewShadowsIndicatorMaterialName=/Engine/EditorMaterials/PreviewShadowIndicatorMaterial.PreviewShadowIndicatorMaterial
EditorBrushMaterialName=/Engine/EngineMaterials/EditorBrushMaterial.EditorBrushMaterial
DefaultPhysMaterialName=/Engine/EngineMaterials/DefaultPhysicalMaterial.DefaultPhysicalMaterial
DefaultDeferredDecalMaterialName=/Engine/EngineMaterials/DefaultDeferredDecalMaterial.DefaultDeferredDecalMaterial
DefaultPostProcessMaterialName=/Engine/EngineMaterials/DefaultPostProcessMaterial.DefaultPostProcessMaterial
TimecodeProviderClassName=None
ArrowMaterialName=/Engine/EngineMaterials/GizmoMaterial.GizmoMaterial
ParticleEventManagerClassPath="/Script/Engine.ParticleEventManager"
LightingOnlyBrightness=(R=0.3,G=0.3,B=0.3,A=1.0)
+ShaderComplexityColors=(R=0.0,G=1.0,B=0.127,A=1.0)
+ShaderComplexityColors=(R=0.0,G=1.0,B=0.0,A=1.0)
+ShaderComplexityColors=(R=0.046,G=0.52,B=0.0,A=1.0)
+ShaderComplexityColors=(R=0.215,G=0.215,B=0.0,A=1.0)
+ShaderComplexityColors=(R=0.52,G=0.046,B=0.0,A=1.0)
+ShaderComplexityColors=(R=0.7,G=0.0,B=0.0,A=1.0)
+ShaderComplexityColors=(R=1.0,G=0.0,B=0.0,A=1.0)
+ShaderComplexityColors=(R=1.0,G=0.0,B=0.5,A=1.0)
+ShaderComplexityColors=(R=1.0,G=0.9,B=0.9,A=1.0)
+QuadComplexityColors=(R=0.0,G=0.0,B=0.0,A=1.0)
+QuadComplexityColors=(R=0.0,G=0.0,B=0.4,A=1.0)
+QuadComplexityColors=(R=0.0,G=0.3,B=1.0,A=1.0)
+QuadComplexityColors=(R=0.0,G=0.7,B=0.4,A=1.0)
+QuadComplexityColors=(R=0.0,G=1.0,B=0.0,A=1.0)
+QuadComplexityColors=(R=0.8,G=0.8,B=0.0,A=1.0)
+QuadComplexityColors=(R=1.0,G=0.3,B=0.0,A=1.0)
+QuadComplexityColors=(R=0.7,G=0.0,B=0.0,A=1.0)
+QuadComplexityColors=(R=0.5,G=0.0,B=0.5,A=1.0)
+QuadComplexityColors=(R=0.7,G=0.3,B=0.7,A=1.0)
+QuadComplexityColors=(R=1.0,G=0.9,B=0.9,A=1.0)
+LightComplexityColors=(R=0.0,G=0.0,B=0.0,A=1.0)
+LightComplexityColors=(R=0.0,G=0.0,B=0.4,A=1.0)
+LightComplexityColors=(R=0.0,G=0.3,B=1.0,A=1.0)
+LightComplexityColors=(R=0.0,G=0.7,B=0.4,A=1.0)
+LightComplexityColors=(R=0.0,G=1.0,B=0.0,A=1.0)
+LightComplexityColors=(R=0.8,G=0.8,B=0.0,A=1.0)
+LightComplexityColors=(R=1.0,G=0.3,B=0.0,A=1.0)
+LightComplexityColors=(R=0.7,G=0.0,B=0.0,A=1.0)
+LightComplexityColors=(R=0.5,G=0.0,B=0.5,A=1.0)
+LightComplexityColors=(R=0.7,G=0.3,B=0.7,A=1.0)
+LightComplexityColors=(R=1.0,G=0.9,B=0.9,A=1.0)
+StationaryLightOverlapColors=(R=0.0,G=1.0,B=0.127,A=1.0)
+StationaryLightOverlapColors=(R=0.0,G=1.0,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=0.046,G=0.52,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=0.215,G=0.215,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=0.52,G=0.046,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=0.7,G=0.0,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=1.0,G=0.0,B=0.0,A=1.0)
+StationaryLightOverlapColors=(R=1.0,G=0.0,B=0.5,A=1.0)
+StationaryLightOverlapColors=(R=1.0,G=0.9,B=0.9,A=1.0)
+LODColorationColors=(R=1.0,G=1.0,B=1.0,A=1.0) ; white (LOD 0)
+LODColorationColors=(R=1.0,G=0.0,B=0.0,A=1.0) ; red (LOD 1)
+LODColorationColors=(R=0.0,G=1.0,B=0.0,A=1.0) ; green (etc...)
+LODColorationColors=(R=0.0,G=0.0,B=1.0,A=1.0) ; blue
+LODColorationColors=(R=1.0,G=1.0,B=0.0,A=1.0) ; yellow
+LODColorationColors=(R=1.0,G=0.0,B=1.0,A=1.0) ; fuchsia (bright purple)
+LODColorationColors=(R=0.0,G=1.0,B=1.0,A=1.0) ; cyan
+LODColorationColors=(R=0.5,G=0.0,B=0.5,A=1.0) ; purple
+StreamingAccuracyColors=(R=1.0,G=0.0,B=0.0,A=1.0)
+StreamingAccuracyColors=(R=0.8,G=0.5,B=0.0,A=1.0)
+StreamingAccuracyColors=(R=0.7,G=0.7,B=0.7,A=1.0)
+StreamingAccuracyColors=(R=0.0,G=0.8,B=0.5,A=1.0)
+StreamingAccuracyColors=(R=0.0,G=1.0,B=0.0,A=1.0)
+HLODColorationColors=(R=1.0,G=1.0,B=1.0,A=1.0) ; white (not part of HLOD)
+HLODColorationColors=(R=0.0,G=1.0,B=0.0,A=1.0) ; green (part of HLOD but being drawn outside of it)
+HLODColorationColors=(R=0.0,G=0.0,B=1.0,A=1.0) ; blue (HLOD level 0)
+HLODColorationColors=(R=1.0,G=1.0,B=0.0,A=1.0) ; yellow (HLOD level 1, etc...)
+HLODColorationColors=(R=1.0,G=0.0,B=1.0,A=1.0) ; purple
+HLODColorationColors=(R=0.0,G=1.0,B=1.0,A=1.0) ; cyan
+HLODColorationColors=(R=0.5,G=0.5,B=0.5,A=1.0) ; grey
MaxPixelShaderAdditiveComplexityCount=2000
MaxES3PixelShaderAdditiveComplexityCount=1700
bSubtitlesEnabled=True
bSubtitlesForcedOff=false
MaximumLoopIterationCount = 1000000
; Allows Blueprint classes based on a native Actor or Component subclass to tick even if their parent has bCanEverTick flag set to false.
bCanBlueprintsTickByDefault=true
; Controls whether anim blueprint nodes that access member variables of their class directly should use the optimized path that avoids a thunk to the Blueprint VM.
bOptimizeAnimBlueprintMemberVariableAccess=true
CameraRotationThreshold=45.0
CameraTranslationThreshold=10000
PrimitiveProbablyVisibleTime = 8.0
MaxOcclusionPixelsFraction = 0.1
MinLightMapDensity=0.0
IdealLightMapDensity=0.2
MaxLightMapDensity=0.8
RenderLightMapDensityGrayscaleScale=1.0
RenderLightMapDensityColorScale=1.0
bRenderLightMapDensityGrayscale=false
LightMapDensityVertexMappedColor=(R=0.65,G=0.65,B=0.25,A=1.0)
LightMapDensitySelectedColor=(R=1.0,G=0.2,B=1.0,A=1.0)
bPauseOnLossOfFocus=false
MaxParticleResize=0
MaxParticleResizeWarn=0
NetClientTicksPerSecond=200
+StatColorMappings=(StatName="AverageFPS",ColorMap=((In=15.0,Out=(R=255)),(In=30,Out=(R=255,G=255)),(In=45.0,Out=(G=255))))
+StatColorMappings=(StatName="Frametime",ColorMap=((In=1.0,Out=(G=255)),(In=25.0,Out=(G=255)),(In=29.0,Out=(R=255,G=255)),(In=33.0,Out=(R=255))))
+StatColorMappings=(StatName="Streaming fudge factor",ColorMap=((In=0.0,Out=(G=255)),(In=1.0,Out=(G=255)),(In=2.5,Out=(R=255,G=255)),(In=5.0,Out=(R=255)),(In=10.0,Out=(R=255))))
DisplayGamma=2.2
MinDesiredFrameRate=35.000000
NetDriverDefinitions=(DefName="GameNetDriver",DriverClassName="/Script/OnlineSubsystemUtils.IpNetDriver",DriverClassNameFallback="/Script/OnlineSubsystemUtils.IpNetDriver")
+NetDriverDefinitions=(DefName="DemoNetDriver",DriverClassName="/Script/Engine.DemoNetDriver",DriverClassNameFallback="/Script/Engine.DemoNetDriver")
NetErrorLogInterval=1.0
SerializationOutOfBoundsErrorMessage=NSLOCTEXT("","SerializationOutOfBoundsErrorMessage","Corrupt data found, please verify your installation.")
SerializationOutOfBoundsErrorMessageCaption=NSLOCTEXT("","SerializationOutOfBoundsErrorMessageCaption","Serialization Error : Action Needed")
bSmoothFrameRate=false
SmoothedFrameRateRange=(LowerBound=(Type="ERangeBoundTypes::Inclusive",Value=22),UpperBound=(Type="ERangeBoundTypes::Exclusive",Value=62))
bCheckForMultiplePawnsSpawnedInAFrame=false
NumPawnsAllowedToBeSpawnedInAFrame=2
; This is the default (additive) color of selected objects in the editor
DefaultSelectedMaterialColor=(R=0.84,G=0.92,B=0.02,A=1.0)
; This is the startup state of the OnScreenDebugMessage system
bEnableOnScreenDebugMessages=true
; This shows errors and warnings on screen
DurationOfErrorsAndWarningsOnHUD=0
NearClipPlane=10.0

; Matinee redirects (starts-with match and replace)
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="PostProcessSettings.EyeAdaptationLowPercent",NewFieldName="CameraComponent.PostProcessSettings.AutoExposureLowPercent")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="PostProcessSettings.EyeAdaptationHighPercent",NewFieldName="CameraComponent.PostProcessSettings.AutoExposureHighPercent")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="PostProcessSettings.EyeAdaptationMinBrightness",NewFieldName="CameraComponent.PostProcessSettings.AutoExposureMinBrightness")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="PostProcessSettings.EyeAdaptationMaxBrightness",NewFieldName="CameraComponent.PostProcessSettings.AutoExposureMaxBrightness")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="PostProcessSettings.EyeAdaptionSpeedDown",NewFieldName="CameraComponent.PostProcessSettings.AutoExposureSpeedDown")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="PostProcessSettings.EyeAdaptionSpeedUp",NewFieldName="CameraComponent.PostProcessSettings.AutoExposureSpeedUp")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="PostProcessSettings.ExposureOffset",NewFieldName="CameraComponent.PostProcessSettings.AutoExposureBias")

+MatineeTrackRedirects=(TargetClassName="/Script/Engine.PostprocessVolume",OldFieldName="Settings.EyeAdaptationLowPercent",NewFieldName="Settings.AutoExposureLowPercent")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.PostprocessVolume",OldFieldName="Settings.EyeAdaptationHighPercent",NewFieldName="Settings.AutoExposureHighPercent")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.PostprocessVolume",OldFieldName="Settings.EyeAdaptationMinBrightness",NewFieldName="Settings.AutoExposureMinBrightness")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.PostprocessVolume",OldFieldName="Settings.EyeAdaptationMaxBrightness",NewFieldName="Settings.AutoExposureMaxBrightness")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.PostprocessVolume",OldFieldName="Settings.EyeAdaptionSpeedDown",NewFieldName="Settings.AutoExposureSpeedDown")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.PostprocessVolume",OldFieldName="Settings.EyeAdaptionSpeedUp",NewFieldName="Settings.AutoExposureSpeedUp")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.PostprocessVolume",OldFieldName="Settings.ExposureOffset",NewFieldName="Settings.AutoExposureBias")

+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="FOVAngle",NewFieldName="CameraComponent.FieldOfView")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="AspectRatio",NewFieldName="CameraComponent.AspectRatio")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="bConstrainAspectRatio",NewFieldName="CameraComponent.bConstrainAspectRatio")
+MatineeTrackRedirects=(TargetClassName="/Script/Engine.CameraActor",OldFieldName="PostProcessSettings.",NewFieldName="CameraComponent.PostProcessSettings.")

[CoreRedirects]

; All redirects prior to UE 4.10 are now in FCoreRedirects::RegisterNativeRedirects, new ones should be added to the bottom of this section
; Redirects can now go in any ini file, including Plugin inis
; This new redirect format has replaced the old ActiveClassRedirects/K2FieldRedirects format, and the syntax is the same for every type
;
; +ClassRedirects=(OldName="OldClass", NewName="/Script/Module.NewClass")
; +ClassRedirects=(OldName="/Script/Module.OldClass", NewName="/Script/Module.NewClass")
;
; Those two will do the same thing, assuming OldClass was in Module. It uses as much of the name as you give it. You can also add options or add a value change map:
;
; +ClassRedirects=(OldName="OldClass", NewName="/Script/GameName.GameSpecificClass", bInstanceOnly=true)
; +PackageRedirects=(OldName="/OldPlugin/", NewName="/NewPlugin/", MatchSubstring=true)
; +PackageRedirects=(OldName="/Game/DeletedContentPackage", Removed=true)
; +EnumRedirects=(OldName="/Script/Core.EEnumType", NewName="/Script/NewModule.ENewEnumType", ValueChanges=(("OldValue", "NewValue"), ("OldValue2", "NewValue2")) )

; 4.10/4.11

+ClassRedirects=(OldName="AnimGraphNode_BlendSpace",NewName="/Script/AnimGraph.AnimGraphNode_BlendSpacePlayer")
+ClassRedirects=(OldName="AnimNotify_PlayParticleEffect_C",NewName="/Script/Engine.AnimNotify_PlayParticleEffect",OverrideClassName="/Script/CoreUObject.Class")
+ClassRedirects=(OldName="AnimNotify_PlaySound_C",NewName="/Script/Engine.AnimNotify_PlaySound",OverrideClassName="/Script/CoreUObject.Class")
+ClassRedirects=(OldName="MovieSceneMaterialParameterSection",NewName="/Script/MovieSceneTracks.MovieSceneParameterSection")

+PackageRedirects=(OldName="/Engine/EngineAnimNotifies/AnimNotify_PlayParticleEffect",Removed=True)
+PackageRedirects=(OldName="/Engine/EngineAnimNotifies/AnimNotify_PlaySound",Removed=True)

+StructRedirects=(OldName="AnimNode_ApplyAdditive",NewName="/Script/AnimGraphRuntime.AnimNode_ApplyAdditive")
+StructRedirects=(OldName="AnimNode_BlendListBase",NewName="/Script/AnimGraphRuntime.AnimNode_BlendListBase")
+StructRedirects=(OldName="AnimNode_BlendListByBool",NewName="/Script/AnimGraphRuntime.AnimNode_BlendListByBool")
+StructRedirects=(OldName="AnimNode_BlendListByEnum",NewName="/Script/AnimGraphRuntime.AnimNode_BlendListByEnum")
+StructRedirects=(OldName="AnimNode_BlendListByInt",NewName="/Script/AnimGraphRuntime.AnimNode_BlendListByInt")
+StructRedirects=(OldName="AnimNode_BlendSpace",NewName="/Script/AnimGraphRuntime.AnimNode_BlendSpacePlayer")
+StructRedirects=(OldName="AnimNode_BlendSpaceEvaluator",NewName="/Script/AnimGraphRuntime.AnimNode_BlendSpaceEvaluator")
+StructRedirects=(OldName="AnimNode_BlendSpacePlayer",NewName="/Script/AnimGraphRuntime.AnimNode_BlendSpacePlayer")
+StructRedirects=(OldName="AnimNode_LayeredBoneBlend",NewName="/Script/AnimGraphRuntime.AnimNode_LayeredBoneBlend")
+StructRedirects=(OldName="AnimNode_MeshSpaceRefPose",NewName="/Script/AnimGraphRuntime.AnimNode_MeshSpaceRefPose")
+StructRedirects=(OldName="AnimNode_RefPose",NewName="/Script/AnimGraphRuntime.AnimNode_RefPose")
+StructRedirects=(OldName="AnimNode_RotateRootBone",NewName="/Script/AnimGraphRuntime.AnimNode_RotateRootBone")
+StructRedirects=(OldName="AnimNode_RotationOffsetBlendSpace",NewName="/Script/AnimGraphRuntime.AnimNode_RotationOffsetBlendSpace")
+StructRedirects=(OldName="AnimNode_SequenceEvaluator",NewName="/Script/AnimGraphRuntime.AnimNode_SequenceEvaluator")
+StructRedirects=(OldName="AnimNode_Slot",NewName="/Script/AnimGraphRuntime.AnimNode_Slot")
+StructRedirects=(OldName="FormatTextArgument",NewName="/Script/Engine.FormatArgumentData")

+FunctionRedirects=(OldName="ConvertTransformToRelative",NewName="/Script/Engine.KismetMathLibrary.MakeRelativeTransform")
+PropertyRedirects=(OldName="KismetMathLibrary.MakeRelativeTransform.LocalTransform",NewName="A")
+PropertyRedirects=(OldName="KismetMathLibrary.MakeRelativeTransform.WorldTransform",NewName="RelativeTo")

; Note that these are reversed from what you'd expect, because the code used to do the reversed calculation.
+PropertyRedirects=(OldName="KismetMathLibrary.MakeRelativeTransform.ParentTransform",NewName="A")
+PropertyRedirects=(OldName="KismetMathLibrary.MakeRelativeTransform.Transform",NewName="RelativeTo")

+PropertyRedirects=(OldName="MaterialInstanceDynamic.K2_CopyMaterialInstanceParameters.SourceMaterialToCopyFrom",NewName="Source")
+PropertyRedirects=(OldName="MaterialInstanceDynamic.K2_InterpolateMaterialInstanceParams.MaterialA",NewName="SourceA")
+PropertyRedirects=(OldName="MaterialInstanceDynamic.K2_InterpolateMaterialInstanceParams.MaterialB",NewName="SourceB")
+PropertyRedirects=(OldName="MaterialInstanceDynamic.K2_InterpolateMaterialInstanceParams.MaterialInstanceA",NewName="SourceA")
+PropertyRedirects=(OldName="MaterialInstanceDynamic.K2_InterpolateMaterialInstanceParams.MaterialInstanceB",NewName="SourceB")

+PropertyRedirects=(OldName="AnimNode_Trail.TrailRelaxationCurve",NewName="AnimNode_Trail.TrailRelaxationSpeed")
+PropertyRedirects=(OldName="FormatArgumentData.TextValue",NewName="FormatArgumentData.ArgumentValue")
+PropertyRedirects=(OldName="LandscapeSplineMeshEntry.Offset",NewName="LandscapeSplineMeshEntry.CenterAdjust")
+PropertyRedirects=(OldName="MovieScenePossessable.ParentSpawnableGuid",NewName="MovieScenePossessable.ParentGuid")
+PropertyRedirects=(OldName="MultiLineEditableText.bAutoWrapText",NewName="MultiLineEditableText.AutoWrapText")
+PropertyRedirects=(OldName="MultiLineEditableTextBox.bAutoWrapText",NewName="MultiLineEditableTextBox.AutoWrapText")
+PropertyRedirects=(OldName="AnimNode_SequenceEvaluator.bShouldLoopWhenInSyncGroup",NewName="AnimNode_SequenceEvaluator.bShouldLoop")

+EnumRedirects=(OldName="EControllerHand",NewName="/Script/InputCore.EControllerHand")
+EnumRedirects=(OldName="AnimPhysConstraintType",NewName="AnimPhysAngularConstraintType")
+EnumRedirects=(OldName="AnimPhysAxisType",NewName="AnimPhysLinearConstraintType")
+EnumRedirects=(OldName="EEnvQueryParam",NewName="/Script/AIModule.EAIParamType")

; 4.12

+ClassRedirects=(OldName="CineCameraActor",NewName="/Script/CinematicCamera.CineCameraActor")
+ClassRedirects=(OldName="CineCameraComponent",NewName="/Script/CinematicCamera.CineCameraComponent")
+ClassRedirects=(OldName="MovieSceneShotSection",NewName="/Script/MovieSceneTracks.MovieSceneCameraCutSection")
+ClassRedirects=(OldName="MovieSceneShotTrack",NewName="/Script/MovieSceneTracks.MovieSceneCameraCutTrack")

+FunctionRedirects=(OldName="Actor.SetActorRotation",NewName="Actor.K2_SetActorRotation")
+FunctionRedirects=(OldName="KismetSystemLibrary.SetSupressViewportTransitionMessage",NewName="KismetSystemLibrary.SetSuppressViewportTransitionMessage")
+FunctionRedirects=(OldName="SteamVRFunctionLibrary.GetTrackingSpace",NewName="HeadMountedDisplayFunctionLibrary.GetTrackingOrigin")
+FunctionRedirects=(OldName="SteamVRFunctionLibrary.SetTrackingSpace",NewName="HeadMountedDisplayFunctionLibrary.SetTrackingOrigin")

+PropertyRedirects=(OldName="AudioEQEffect.HFFrequency",NewName="AudioEQEffect.FrequencyCenter2")
+PropertyRedirects=(OldName="AudioEQEffect.HFGain",NewName="AudioEQEffect.Gain2")
+PropertyRedirects=(OldName="AudioEQEffect.LFFrequency",NewName="AudioEQEffect.FrequencyCenter0")
+PropertyRedirects=(OldName="AudioEQEffect.LFGain",NewName="AudioEQEffect.Gain0")
+PropertyRedirects=(OldName="AudioEQEffect.MFBandwidth",NewName="AudioEQEffect.Bandwidth1")
+PropertyRedirects=(OldName="AudioEQEffect.MFCutoffFrequency",NewName="AudioEQEffect.FrequencyCenter1")
+PropertyRedirects=(OldName="AudioEQEffect.MFGain",NewName="AudioEQEffect.Gain1")
+PropertyRedirects=(OldName="BodyInstance.MassInKg",NewName="BodyInstance.MassInKgOverride")
+PropertyRedirects=(OldName="EnvQueryTest.SweetSpotValue",NewName="EnvQueryTest.ReferenceValue")
+PropertyRedirects=(OldName="EnvQueryTest.bDefineSweetSpot",NewName="EnvQueryTest.bDefineReferenceValue")
+PropertyRedirects=(OldName="MovieScene.ShotTrack",NewName="MovieScene.CameraCutTrack")
+PropertyRedirects=(OldName="MovieSceneShotSection.ShotNumber",NewName="MovieSceneShotSection.CameraCutNumber")

+EnumRedirects=(OldName="ESteamVRTrackingSpace",NewName="EHMDTrackingOrigin")

; 4.13

+ClassRedirects=(OldName="EdGraphPin",NewName="/Script/Engine.EdGraphPin_Deprecated")
+ClassRedirects=(OldName="HapticFeedbackEffect",NewName="/Script/Engine.HapticFeedbackEffect_Curve")
+ClassRedirects=(OldName="LandscapeProxy",NewName="/Script/Landscape.LandscapeStreamingProxy",InstanceOnly=True)

+StructRedirects=(OldName="HapticFeedbackDetails",NewName="HapticFeedbackDetails_Curve")
+StructRedirects=(OldName="AnimNode_SaveCachedPose",NewName="/Script/Engine.AnimNode_SaveCachedPose")

+FunctionRedirects=(OldName="SceneCaptureComponent2D.UpdateContent",NewName="SceneCaptureComponent2D.CaptureScene")
+FunctionRedirects=(OldName="SceneCaptureComponentCube.UpdateContent",NewName="SceneCaptureComponentCube.CaptureScene")

+PropertyRedirects=(OldName="Blueprint.PinWatches",NewName="Blueprint.DeprecatedPinWatches")
+PropertyRedirects=(OldName="Box2D.bIsValid",NewName="Box2D.IsValid")
+PropertyRedirects=(OldName="EdGraphNode.Pins",NewName="EdGraphNode.DeprecatedPins")
+PropertyRedirects=(OldName="PhysicsAsset.Profiles",NewName="PhysicsAsset.PhysicalAnimationProfiles")
+PropertyRedirects=(OldName="PrimitiveComponent.bReceiveCSMFromDynamicObjects",NewName="PrimitiveComponent.bReceiveCombinedCSMAndStaticShadowsFromStationaryLights")
+PropertyRedirects=(OldName="SplineComponent.bAlwaysRenderInEditor",NewName="SplineComponent.bDrawDebug")

+EnumRedirects=(OldName="ENoiseFunction",ValueChanges=(("NOISEFUNCTION_FastGradient","NOISEFUNCTION_GradientTex3D"), ("NOISEFUNCTION_Gradient","NOISEFUNCTION_ValueALU"), ("NOISEFUNCTION_Perlin","NOISEFUNCTION_GradientTex"), ("NOISEFUNCTION_Simplex","NOISEFUNCTION_SimplexTex")) )
+EnumRedirects=(OldName="EPathFollowingResult",ValueChanges=(("EPathFollowingResult::Skipped","EPathFollowingResult::Skipped_DEPRECATED")) )
+EnumRedirects=(OldName="EStereoLayerType",ValueChanges=(("EStereoLayerType::SLT_TorsoLocked","EStereoLayerType::SLT_TrackerLocked")) )

; 4.14

+ClassRedirects=(OldName="AnimGraphNode_OrientationDriver",NewName="/Script/AnimGraph.AnimGraphNode_PoseDriver")
+ClassRedirects=(OldName="K2Node_AIMoveTo",NewName="/Script/AIGraph.K2Node_AIMoveTo")
+StructRedirects=(OldName="AnimNode_OrientationDriver",NewName="/Script/AnimGraphRuntime.AnimNode_PoseDriver")

+FunctionRedirects=(OldName="KismetMathLibrary.GetDirectionVector",NewName="GetDirectionUnitVector")

+PropertyRedirects=(OldName="SCS_Node.VariableName",NewName="SCS_Node.InternalVariableName")

+EnumRedirects=(OldName="ESuggestProjVelocityTraceOption",ValueChanges=(("OnlyTraceWhileAsceding","OnlyTraceWhileAscending")) )

; 4.15

+ClassRedirects=(OldName="BackgroundBlurWidget",NewName="/Script/UMG.BackgroundBlur")
+ClassRedirects=(OldName="MovieSceneVisibilitySection",NewName="/Script/MovieScene.MovieSceneBoolSection")
+ClassRedirects=(OldName="SoundClassGraph",NewName="/Script/AudioEditor.SoundClassGraph")
+ClassRedirects=(OldName="SoundClassGraphNode",NewName="/Script/AudioEditor.SoundClassGraphNode")
+ClassRedirects=(OldName="SoundClassGraphSchema",NewName="/Script/AudioEditor.SoundClassGraphSchema")
+ClassRedirects=(OldName="SoundCueGraph",NewName="/Script/AudioEditor.SoundCueGraph")
+ClassRedirects=(OldName="SoundCueGraphNode",NewName="/Script/AudioEditor.SoundCueGraphNode")
+ClassRedirects=(OldName="SoundCueGraphNode_Base",NewName="/Script/AudioEditor.SoundCueGraphNode_Base")
+ClassRedirects=(OldName="SoundCueGraphNode_Root",NewName="/Script/AudioEditor.SoundCueGraphNode_Root")
+ClassRedirects=(OldName="SoundCueGraphSchema",NewName="/Script/AudioEditor.SoundCueGraphSchema")

+StructRedirects=(OldName="AnimationNode_TwoWayBlend",NewName="/Script/AnimGraphRuntime.AnimNode_TwoWayBlend")
+StructRedirects=(OldName="AttenuationSettings",NewName="SoundAttenuationSettings")
+StructRedirects=(OldName="LevelSequencePlaybackSettings",NewName="/Script/MovieScene.MovieSceneSequencePlaybackSettings")

+FunctionRedirects=(OldName="BlueprintGameplayTagLibrary.DoGameplayTagsMatch",NewName="BlueprintGameplayTagLibrary.MatchesTag")
+FunctionRedirects=(OldName="BlueprintGameplayTagLibrary.DoesContainerHaveTag",NewName="BlueprintGameplayTagLibrary.HasTag")
+FunctionRedirects=(OldName="BlueprintGameplayTagLibrary.DoesContainerMatchAllTagsInContainer",NewName="BlueprintGameplayTagLibrary.HasAllTags")
+FunctionRedirects=(OldName="BlueprintGameplayTagLibrary.DoesContainerMatchAnyTagsInContainer",NewName="BlueprintGameplayTagLibrary.HasAnyTags")
+PropertyRedirects=(OldName="BlueprintGameplayTagLibrary.IsGameplayTagValid.TagContainer",NewName="GameplayTag")
+FunctionRedirects=(OldName="BlueprintGameplayTagLibrary.AddGameplayTagToContainer",NewName="BlueprintGameplayTagLibrary.AddGameplayTag")
+PropertyRedirects=(OldName="BlueprintGameplayTagLibrary.AddGameplayTag.InOutTagContainer",NewName="TagContainer")
+FunctionRedirects=(OldName="GameplayStatics.PredictProjectilePath",NewName="GameplayStatics.Blueprint_PredictProjectilePath_ByObjectType")
+FunctionRedirects=(OldName="KismetSystemLibrary.BoxOverlapActors_NEW",NewName="KismetSystemLibrary.BoxOverlapActors")
+FunctionRedirects=(OldName="KismetSystemLibrary.BoxOverlapComponents_NEW",NewName="KismetSystemLibrary.BoxOverlapComponents")
+FunctionRedirects=(OldName="KismetSystemLibrary.CapsuleOverlapActors_NEW",NewName="KismetSystemLibrary.CapsuleOverlapActors")
+FunctionRedirects=(OldName="KismetSystemLibrary.CapsuleOverlapComponents_NEW",NewName="KismetSystemLibrary.CapsuleOverlapComponents")
+FunctionRedirects=(OldName="KismetSystemLibrary.CapsuleTraceMulti_NEW",NewName="KismetSystemLibrary.CapsuleTraceMulti")
+FunctionRedirects=(OldName="KismetSystemLibrary.CapsuleTraceSingle_NEW",NewName="KismetSystemLibrary.CapsuleTraceSingle")
+FunctionRedirects=(OldName="KismetSystemLibrary.ComponentOverlapActors_NEW",NewName="KismetSystemLibrary.ComponentOverlapActors")
+FunctionRedirects=(OldName="KismetSystemLibrary.ComponentOverlapComponents_NEW",NewName="KismetSystemLibrary.ComponentOverlapComponents")
+FunctionRedirects=(OldName="KismetSystemLibrary.LineTraceMulti_NEW",NewName="KismetSystemLibrary.LineTraceMulti")
+FunctionRedirects=(OldName="KismetSystemLibrary.LineTraceSingle_NEW",NewName="KismetSystemLibrary.LineTraceSingle")
+FunctionRedirects=(OldName="KismetSystemLibrary.SphereOverlapActors_NEW",NewName="KismetSystemLibrary.SphereOverlapActors")
+FunctionRedirects=(OldName="KismetSystemLibrary.SphereOverlapComponents_NEW",NewName="KismetSystemLibrary.SphereOverlapComponents")
+FunctionRedirects=(OldName="KismetSystemLibrary.SphereTraceMulti_NEW",NewName="KismetSystemLibrary.SphereTraceMulti")
+FunctionRedirects=(OldName="KismetSystemLibrary.SphereTraceSingle_NEW",NewName="KismetSystemLibrary.SphereTraceSingle")

+PropertyRedirects=(OldName="MediaPlayer.Seek.InTime",NewName="Time")
+PropertyRedirects=(OldName="MediaPlayer.SetLooping.InLooping",NewName="Looping")

+EnumRedirects=(OldName="EFontLoadingPolicy",ValueChanges=(("EFontLoadingPolicy::PreLoad","EFontLoadingPolicy::LazyLoad")) )
+EnumRedirects=(OldName="ESoundDistanceModel",NewName="/Script/Engine.EAttenuationDistanceModel",ValueChanges=(("ATTENUATION_Custom","EAttenuationDistanceModel::Custom"),("ATTENUATION_Inverse","EAttenuationDistanceModel::Inverse"),("ATTENUATION_Linear","EAttenuationDistanceModel::Linear"),("ATTENUATION_LogReverse","EAttenuationDistanceModel::LogReverse"),("ATTENUATION_Logarithmic","EAttenuationDistanceModel::Logarithmic"),("ATTENUATION_NaturalSound","EAttenuationDistanceModel::NaturalSound")) )

; 4.16

; Clothing System Redirects
+StructRedirects=(OldName="ClothingAssetData",NewName="ClothingAssetData_Legacy")
+StructRedirects=(OldName="ClothPhysicsProperties",NewName="ClothPhysicsProperties_Legacy")

; Move immediate mode anim nodes into immediate mode module
+ClassRedirects=(OldName="AnimGraphNode_Ragdoll",NewName="/Script/ImmediatePhysicsEditor.AnimGraphNode_RigidBody")
+StructRedirects=(OldName="AnimNode_Ragdoll",NewName="/Script/ImmediatePhysics.AnimNode_RigidBody")

; Renamed MovieSceneObjectBindingPtr to MovieSceneObjectBindingID
+StructRedirects=(OldName="MovieSceneObjectBindingPtr",NewName="/Script/MovieScene.MovieSceneObjectBindingID")

+PropertyRedirects=(OldName="Box2D.IsValid",NewName="bIsValid")

; 4.17

+PropertyRedirects=(OldName="StaticMesh.bRequiresAreaWeightedSampling",NewName="StaticMesh.bSupportUniformlyDistributedSampling")
+PropertyRedirects=(OldName="FPostProcessSettings.BloomConvolutionPreFilter", NewName="FPostProcessSettings.BloomConvolutionPreFilter_DEPRECATED")
+PropertyRedirects=(OldName="FPostProcessSettings.bOverride_BloomConvolutionPreFilter", NewName="FPostProcessSettings.bOverride_BloomConvolutionPreFilter_DEPRECATED")

; 4.18

+PropertyRedirects=(OldName="GoogleVRControllerEventManager.OnControllerRecenteredDelegate", NewName="GoogleVRControllerEventManager.OnControllerRecenteredDelegate_DEPRECATED")

+ClassRedirects=(OldName="ARBlueprintFunctionLibrary", NewName="/Script/AugmentedReality.ARBlueprintLibrary")
+StructRedirects=(OldName="ARHitTestResult",NewName="/Script/AugmentedReality.ARHitTestResult")

;SpectatorScreen Refactoring Redirects
+ClassRedirects=(OldName="HeadMountedDisplayFunctionLibrary",NewName="/Script/HeadMountedDisplay.HeadMountedDisplayFunctionLibrary")
+EnumRedirects=(OldName="EWidgetClipping",ValueChanges=(("EWidgetClipping::No","EWidgetClipping::Inherit"),("EWidgetClipping::Yes","EWidgetClipping::ClipToBounds"),("EWidgetClipping::YesWithoutIntersecting","EWidgetClipping::ClipToBoundsWithoutIntersecting"),("EWidgetClipping::YesAlways","EWidgetClipping::ClipToBoundsAlways")) )
+EnumRedirects=(OldName="EOrientPositionSelector",NewName="/Script/HeadMountedDisplay.EOrientPositionSelector")
+EnumRedirects=(OldName="EHMDTrackingOrigin",NewName="/Script/HeadMountedDisplay.EHMDTrackingOrigin")
+EnumRedirects=(OldName="EHMDWornState",NewName="/Script/HeadMountedDisplay.EHMDWornState")
+EnumRedirects=(OldName="ESocialScreenModes",NewName="/Script/HeadMountedDisplay.ESpectatorScreenMode",ValueChanges=(("SystemMirror","ESpectatorScreenMode::SingleEyeCroppedToFill"),("SeparateTest","ESpectatorScreenMode::Undistorted"),("SeparateTexture","ESpectatorScreenMode::Texture"),("SeparateSoftwareMirror","ESpectatorScreenMode::Undistorted")) )
+FunctionRedirects=(OldName="SetSocialScreenMode",NewName="/Script/HeadMountedDisplay.HeadMountedDisplayFunctionLibrary.SetSpectatorScreenMode")
+FunctionRedirects=(OldName="SetSocialScreenTexture",NewName="/Script/HeadMountedDisplay.HeadMountedDisplayFunctionLibrary.SetSpectatorScreenTexture")
+PropertyRedirects=(OldName="Widget.ClipToBounds",NewName="Clipping")


; 4.18
+StructRedirects=(OldName="TargetReference",NewName="/Script/AnimGraphRuntime.BoneSocketTarget")

; StringAssetReference->SoftObjectPath/AssetPtr->SoftObjectPtr rename
+StructRedirects=(OldName="StringAssetReference",NewName="/Script/CoreUObject.SoftObjectPath")
+StructRedirects=(OldName="StringClassReference",NewName="/Script/CoreUObject.SoftClassPath")
+ClassRedirects=(OldName="AssetObjectProperty",NewName="/Script/CoreUObject.SoftObjectProperty")
+ClassRedirects=(OldName="AssetClassProperty",NewName="/Script/CoreUObject.SoftClassProperty")
+FunctionRedirects=(OldName="MakeStringAssetReference",NewName="/Script/Engine.KismetSystemLibrary.MakeSoftObjectPath")
+PropertyRedirects=(OldName="MakeSoftObjectPath.AssetLongPathname",NewName="PathString")
+FunctionRedirects=(OldName="SetAssetPropertyByName",NewName="/Script/Engine.KismetSystemLibrary.SetSoftObjectPropertyByName")
+FunctionRedirects=(OldName="SetAssetClassPropertyByName",NewName="/Script/Engine.KismetSystemLibrary.SetSoftClassPropertyByName")

+FunctionRedirects=(OldName="RandomUnitVectorInCone",NewName="/Script/Engine.KismetMathLibrary.RandomUnitVectorInConeInRadians")
+FunctionRedirects=(OldName="RandomUnitVectorInConeWithYawAndPitch",NewName="/Script/Engine.KismetMathLibrary.RandomUnitVectorInEllipticalConeInDegrees")
+PropertyRedirects=(OldName="KismetMathLibrary.RandomUnitVectorInConeInRadians.ConeHalfAngle",NewName="ConeHalfAngleInRadians")

+PropertyRedirects=(OldName="Widget.Visiblity",NewName="Widget.Visibility")
+PropertyRedirects=(OldName="WidgetBlueprintLibrary.SetInputMode_UIOnlyEx.Target",NewName="PlayerController")
+PropertyRedirects=(OldName="WidgetBlueprintLibrary.SetInputMode_GameAndUIEx.Target",NewName="PlayerController")
+PropertyRedirects=(OldName="WidgetBlueprintLibrary.SetInputMode_GameOnly.Target",NewName="PlayerController")

; Material layers property refactor
+PropertyRedirects=(OldName="FScalarParameterValue.ParameterName", NewName="FScalarParameterValue.ParameterName_DEPRECATED")
+PropertyRedirects=(OldName="FVectorParameterValue.ParameterName", NewName="FVectorParameterValue.ParameterName_DEPRECATED")
+PropertyRedirects=(OldName="FTextureParameterValue.ParameterName", NewName="FTextureParameterValue.ParameterName_DEPRECATED")
+PropertyRedirects=(OldName="FFontParameterValue.ParameterName", NewName="FFontParameterValue.ParameterName_DEPRECATED")

; 4.19
+ClassRedirects=(OldName="/Script/MovieSceneTracks.MovieSceneSubTrack",NewName="/Script/MovieScene.MovieSceneSubTrack")
+ClassRedirects=(OldName="/Script/MovieSceneTracks.MovieSceneSubSection",NewName="/Script/MovieScene.MovieSceneSubSection")
+FunctionRedirects=(OldName="InverseLerp",NewName="/Script/Engine.KismetMathLibrary.NormalizeToRange")
+PropertyRedirects=(OldName="NormalizeToRange.A",NewName="RangeMin")
+PropertyRedirects=(OldName="NormalizeToRange.B",NewName="RangeMax")
+ClassRedirects=(OldName="WebBrowserTexture",NewName="/Script/WebBrowserTexture.WebBrowserTexture")

+PropertyRedirects=(OldName="Widget.Opacity", NewName="Widget.RenderOpacity")
+FunctionRedirects=(OldName="Widget.GetOpacity", NewName="Widget.GetRenderOpacity")
+FunctionRedirects=(OldName="Widget.SetOpacity", NewName="Widget.SetRenderOpacity")
+EnumRedirects=(OldName="ENetDormancy",ValueChanges=(("DORN_MAX","DORM_MAX"))

+PackageRedirects=(OldName="/Script/EditorScriptingUtilitiesEditor", NewName="/Script/AssetScriptingUtilitiesEditor")

+PropertyRedirects=(OldName="PrimitiveComponent.GetOverlappingComponents.InOverlappingComponents",NewName="OutOverlappingComponents")

; 4.20

+ClassRedirects=(OldName="USkeletalMeshReductionSettings",NewName="/Script/Engine.SkeletalMeshLODSettings")
+PropertyRedirects=(OldName="SkeletalMeshLODGroupSettings.OptimizationSettings", NewName="ReductionSettings")
+PropertyRedirects=(OldName="SkeletalMeshLODSettings.Settings", NewName="LODGroups")

+FunctionRedirects=(OldName="/Script/HeadMountedDisplay.HeadMountedDisplayFunctionLibrary.AddDeviceVisualizationComponent",NewName="/Script/HeadMountedDisplay.XRAssetFunctionLibrary.AddDeviceVisualizationComponentBlocking")
+FunctionRedirects=(OldName="/Script/HeadMountedDisplay.HeadMountedDisplayFunctionLibrary.AddNamedDeviceVisualizationComponent",NewName="/Script/HeadMountedDisplay.XRAssetFunctionLibrary.AddNamedDeviceVisualizationComponentBlocking")
+EnumRedirects=(OldName="ESimulationSpace",ValueChanges=(("RootBoneSpace", "BaseBoneSpace")))
+EnumRedirects=(OldName="EColorVisionDeficiency", NewName="/Script/SlateCore.EColorVisionDeficiency", ValueChanges=(("CVD_NormalVision", "NormalVision"), ("CVD_Deuteranomly", "NormalVision"), ("CVD_Deuteranopia", "Deuteranope"), ("CVD_Protanomly", "NormalVision"), ("CVD_Protanopia", "Protanope"), ("CVD_Tritanomaly", "NormalVision"), ("CVD_Tritanopia", "Tritanope"), ("CVD_Achromatopsia", "NormalVision"))

+ClassRedirects=(OldName="NavigationSystem",NewName="/Script/NavigationSystem.NavigationSystemV1")
+ClassRedirects=(OldName="NavMeshBoundsVolume",NewName="/Script/NavigationSystem.NavMeshBoundsVolume")
+ClassRedirects=(OldName="NavArea",NewName="/Script/NavigationSystem.NavArea")
+ClassRedirects=(OldName="NavAreaMeta",NewName="/Script/NavigationSystem.NavAreaMeta")
+ClassRedirects=(OldName="NavArea_Default",NewName="/Script/NavigationSystem.NavArea_Default")
+ClassRedirects=(OldName="NavArea_LowHeight",NewName="/Script/NavigationSystem.NavArea_LowHeight")
+ClassRedirects=(OldName="NavArea_Null",NewName="/Script/NavigationSystem.NavArea_Null")
+ClassRedirects=(OldName="NavArea_Obstacle",NewName="/Script/NavigationSystem.NavArea_Obstacle")
+ClassRedirects=(OldName="NavAreaMeta_SwitchByAgent",NewName="/Script/NavigationSystem.NavAreaMeta_SwitchByAgent")
+ClassRedirects=(OldName="NavigationQueryFilter",NewName="/Script/NavigationSystem.NavigationQueryFilter")
+ClassRedirects=(OldName="NavMeshRenderingComponent",NewName="/Script/NavigationSystem.NavMeshRenderingComponent")
+ClassRedirects=(OldName="RecastNavMesh",NewName="/Script/NavigationSystem.RecastNavMesh")
+ClassRedirects=(OldName="RecastNavMeshDataChunk",NewName="/Script/NavigationSystem.RecastNavMeshDataChunk")
+ClassRedirects=(OldName="AbstractNavData",NewName="/Script/NavigationSystem.AbstractNavData")
+ClassRedirects=(OldName="CrowdManagerBase",NewName="/Script/NavigationSystem.CrowdManagerBase")
+ClassRedirects=(OldName="NavCollision",NewName="/Script/NavigationSystem.NavCollision")
+ClassRedirects=(OldName="NavigationData",NewName="/Script/NavigationSystem.NavigationData")
+ClassRedirects=(OldName="NavigationInvokerComponent",NewName="/Script/NavigationSystem.NavigationInvokerComponent")
+ClassRedirects=(OldName="NavigationPath",NewName="/Script/NavigationSystem.NavigationPath")
+ClassRedirects=(OldName="NavigationTestingActor",NewName="/Script/NavigationSystem.NavigationTestingActor")
+ClassRedirects=(OldName="NavLinkComponent",NewName="/Script/NavigationSystem.NavLinkComponent")
+ClassRedirects=(OldName="NavLinkCustomComponent",NewName="/Script/NavigationSystem.NavLinkCustomComponent")
+ClassRedirects=(OldName="NavLinkRenderingComponent",NewName="/Script/NavigationSystem.NavLinkRenderingComponent")
+ClassRedirects=(OldName="NavLinkTrivial",NewName="/Script/NavigationSystem.NavLinkTrivial")
+ClassRedirects=(OldName="NavModifierComponent",NewName="/Script/NavigationSystem.NavModifierComponent")
+ClassRedirects=(OldName="NavModifierVolume",NewName="/Script/NavigationSystem.NavModifierVolume")
+ClassRedirects=(OldName="NavRelevantComponent",NewName="/Script/NavigationSystem.NavRelevantComponent")
+ClassRedirects=(OldName="RecastFilter_UseDefaultArea",NewName="/Script/NavigationSystem.RecastFilter_UseDefaultArea")
+ClassRedirects=(OldName="NavigationGraph",NewName="/Script/NavigationSystem.NavigationGraph")
+ClassRedirects=(OldName="NavigationGraphNode",NewName="/Script/NavigationSystem.NavigationGraphNode")
+ClassRedirects=(OldName="NavigationGraphNodeComponent",NewName="/Script/NavigationSystem.NavigationGraphNodeComponent")
+ClassRedirects=(OldName="NavigationPathGenerator",NewName="/Script/NavigationSystem.NavigationPathGenerator")
+ClassRedirects=(OldName="NavLinkCustomInterface",NewName="/Script/NavigationSystem.NavLinkCustomInterface")
+ClassRedirects=(OldName="NavLinkHostInterface",NewName="/Script/NavigationSystem.NavLinkHostInterface")
+ClassRedirects=(OldName="NavNodeInterface",NewName="/Script/NavigationSystem.NavNodeInterface")
+ClassRedirects=(OldName="NavLinkProxy",NewName="/Script/AIModule.NavLinkProxy")
+StructRedirects=(OldName="NavigationFilterArea",NewName="/Script/NavigationSystem.NavigationFilterArea")
+StructRedirects=(OldName="NavigationFilterFlags",NewName="/Script/NavigationSystem.NavigationFilterFlags")
+StructRedirects=(OldName="NavGraphEdge",NewName="/Script/NavigationSystem.NavGraphEdge")
+StructRedirects=(OldName="NavGraphNode",NewName="/Script/NavigationSystem.NavGraphNode")
+StructRedirects=(OldName="NavCollisionCylinder",NewName="/Script/NavigationSystem.NavCollisionCylinder")
+StructRedirects=(OldName="NavCollisionBox",NewName="/Script/NavigationSystem.NavCollisionBox")
+StructRedirects=(OldName="SupportedAreaData",NewName="/Script/NavigationSystem.SupportedAreaData")
+FunctionRedirects=(OldName="NavigationSystemV1.SimpleMoveToActor",NewName="AIBlueprintHelperLibrary.SimpleMoveToActor")
+FunctionRedirects=(OldName="NavigationSystemV1.SimpleMoveToLocation",NewName="AIBlueprintHelperLibrary.SimpleMoveToLocation")

+PropertyRedirects=(OldName="UserWidget.bCanEverTick", NewName="bHasScriptImplementedTick")
+PropertyRedirects=(OldName="UserWidget.bCanEverPaint", NewName="bHasScriptImplementedPaint")

+PropertyRedirects=(OldName="MovieScene.FrameResolution",NewName="TickResolution")
+PropertyRedirects=(OldName="MovieScene.PlayRate",NewName="DisplayRate")
+ClassRedirects=(OldName="/Script/MovieSceneCapture.AutomatedLevelSequenceCapture", NewName="/Script/MovieSceneTools.AutomatedLevelSequenceCapture")
+PackageRedirects=(OldName="/Script/AssetScriptingUtilitiesEditor", NewName="/Script/EditorScriptingUtilities")
+ClassRedirects=(OldName="/Script/AssetScriptingUtilities.StaticMeshUtilitiesLibrary", NewName="/Script/EditorScriptingUtilities.EditorStaticMeshLibrary")
+ClassRedirects=(OldName="/Script/AssetScriptingUtilities.SkeletalMeshUtilitiesLibrary", NewName="/Script/EditorScriptingUtilities.EditorSkeletalMeshLibrary")
+FunctionRedirects=(OldName="StaticMeshUtilitiesLibrary.GetLODScreenSizes",NewName="EditorStaticMeshLibrary.GetLodScreenSizes")

+ClassRedirects=(OldName="AppleARKitFaceMeshComponent",NewName="/Script/AppleARKitFaceSupport.AppleARKitFaceMeshComponent")

+StructRedirects=(OldName="FrameNumber",NewName="/Script/CoreUObject.FrameNumber")
+StructRedirects=(OldName="FrameRate",NewName="/Script/CoreUObject.FrameRate")
+StructRedirects=(OldName="FrameTime",NewName="/Script/CoreUObject.FrameTime")
+StructRedirects=(OldName="QualifiedFrameTime",NewName="/Script/CoreUObject.QualifiedFrameTime")
+StructRedirects=(OldName="Timecode",NewName="/Script/CoreUObject.Timecode")

; 4.21
+EnumRedirects=(OldName="EMeshComponentUpdateFlag",NewName="/Script/Engine.EVisibilityBasedAnimTickOption")
+PropertyRedirects=(OldName="SkinnedMeshComponent.MeshComponentUpdateFlag", NewName="VisibilityBasedAnimTickOption")

+ClassRedirects=(OldName="LevelStreamingKismet",NewName="/Script/Engine.LevelStreamingDynamic")

; Move immediate mode anim nodes into engine as we now rely on it there
+PackageRedirects=(OldName="/Script/ImmediatePhysicsEditor", NewName="/Script/AnimGraph")
+PackageRedirects=(OldName="/Script/ImmediatePhysics", NewName="/Script/AnimGraphRuntime")
+ClassRedirects=(OldName="/Script/ImmediatePhysicsEditor.AnimGraphNode_RigidBody",NewName="/Script/AnimGraph.AnimGraphNode_RigidBody")
+StructRedirects=(OldName="/Script/ImmediatePhysics.AnimNode_RigidBody",NewName="/Script/AnimGraphRuntime.AnimNode_RigidBody")

; 4.21

; MagicLeap Gestures->HandTracking
+PackageRedirects=(OldName="/Script/MagicLeapGestures", NewName="/Script/MagicLeapHandTracking")
+ClassRedirects=(OldName="/Script/MagicLeapGestures.MagicLeapGesturesFunctionLibrary",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary")
+EnumRedirects=(OldName="/Script/MagicLeapGestures.EStaticGestures",NewName="/Script/MagicLeapHandTracking.EHandTrackingGesture")
+EnumRedirects=(OldName="/Script/MagicLeapGestures.EGestureKeypointsFilterLevel",NewName="/Script/MagicLeapHandTracking.EHandTrackingKeypointFilterLevel")
+EnumRedirects=(OldName="/Script/MagicLeapGestures.EGestureRecognitionFilterLevel",NewName="/Script/MagicLeapHandTracking.EHandTrackingGestureFilterLevel")
+EnumRedirects=(OldName="/Script/MagicLeapGestures.EGestureTransformSpace",NewName="/Script/MagicLeapHandTracking.EGestureTransformSpace")
+EnumRedirects=(OldName="ESceneTextureId",ValueChanges=(("PPI_ShadingModel","PPI_ShadingModelColor"))
+FunctionRedirects=(OldName="NavigationSystemV1.SimpleMoveToLocation",NewName="AIBlueprintHelperLibrary.SimpleMoveToLocation")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapGesturesFunctionLibrary.GetHandCenter",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetHandCenter")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetHandPointer",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetHandIndexFingerTip")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetHandSecondary",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetHandThumbTip")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetHandCenterNormalized",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetHandCenterNormalized")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetGestureKeypoints",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetGestureKeypoints")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.SetConfiguration",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.SetConfiguration")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetConfiguration",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetConfiguration")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.SetStaticGestureConfidenceThreshold",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.SetStaticGestureConfidenceThreshold")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetStaticGestureConfidenceThreshold",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetStaticGestureConfidenceThreshold")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetHandGestureConfidence",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetCurrentGestureConfidence")
+FunctionRedirects=(OldName="/Script/MagicLeapHandTracking.MagicLeapGesturesFunctionLibrary.GetCurrentGesture",NewName="/Script/MagicLeapHandTracking.MagicLeapHandTrackingFunctionLibrary.GetCurrentGesture")

; LevelSequenceDirectorBlueprint removal
+ClassRedirects=(OldName="LevelSequenceDirectorBlueprint",NewName="/Script/LevelSequence.LegacyLevelSequenceDirectorBlueprint")
+ClassRedirects=(OldName="LevelSequenceDirectorGeneratedClass",NewName="/Script/Engine.BlueprintGeneratedClass")

; 4.22
+FunctionRedirects=(OldName="UserWidget.PlayAnimationTo",NewName="UserWidget.PlayAnimationTimeRange")
+FunctionRedirects=(OldName="UserWidget.PlayAnimationAtTime",NewName="UserWidget.PlayAnimation")
+FunctionRedirects=(OldName="AddChildWrapBox", NewName="AddChildToWrapBox")

+PropertyRedirects=(OldName="GameplayStatics.CreatePlayer.bSpawnPawn",NewName="bSpawnPlayerController")

+EnumRedirects=(OldName="ETiledMultiResLevel",NewName="EFixedFoveatedRenderingLevel",ValueChanges=(("ETiledMultiResLevel_Off","FFR_Off"),("ETiledMultiResLevel_LMSLow","FFR_Low"),("ETiledMultiResLevel_LMSMedium","FFR_Medium"),("ETiledMultiResLevel_LMSHigh","FFR_High"),("ETiledMultiResLevel_LMSHighTop","FFR_HighTop")))
+FunctionRedirects=(OldName="GetTiledMultiresLevel",NewName="GetFixedFoveatedRenderingLevel")
+FunctionRedirects=(OldName="SetTiledMultiresLevel",NewName="SetFixedFoveatedRenderingLevel")

; 4.23
+FunctionRedirects=(OldName="SkeletalMeshComponent.SetAnimInstanceClass",NewName="SkeletalMeshComponent.SetAnimClass")
+FunctionRedirects=(OldName="SkeletalMeshComponent.K2_SetAnimInstanceClass",NewName="SkeletalMeshComponent.SetAnimClass")

; Chaos
+ClassRedirects=(OldName="/Script/GeometryCollectionCore.GeometryCollection",NewName="/Script/GeometryCollectionEngine.GeometryCollection")
+ClassRedirects=(OldName="/Script/GeometryCollectionCore.GeometryCollectionCache",NewName="/Script/GeometryCollectionEngine.GeometryCollectionCache")

+FunctionRedirects=(OldName="Controller.OnPossess",NewName="Controller.ReceivePossess")
+FunctionRedirects=(OldName="Controller.OnUnPossess",NewName="Controller.ReceiveUnPossess")

+FunctionRedirects=(OldName="PlayerController.ClientPlayForceFeedback",NewName="PlayerController.K2_ClientPlayForceFeedback")

; 4.23
+FunctionRedirects=(OldName="SkeletalMeshComponent.SetAnimInstanceClass",NewName="SkeletalMeshComponent.SetAnimClass")
+FunctionRedirects=(OldName="SkeletalMeshComponent.K2_SetAnimInstanceClass",NewName="SkeletalMeshComponent.SetAnimClass")
+FunctionRedirects=(OldName="EditorUtilityWidget.OnDefaultActionClicked",NewName="EditorUtilityWidget.Run")
+FunctionRedirects=(OldName="SkeletalMeshComponent.GetSubInstanceByName",NewName="KismetSystemLibrary.GetSubInstanceByTag")
+StructRedirects=(OldName="/Script/AnimGraphRuntime.AnimNode_Root",NewName="/Script/Engine.AnimNode_Root")
+FunctionRedirects=(OldName="Widget.SetRenderAngle", NewName="Widget.SetRenderTransformAngle")

+ClassRedirects=(OldName="/Script/CoreUObject.MulticastDelegateProperty",NewName="/Script/CoreUObject.MulticastInlineDelegateProperty")
+ClassRedirects=(OldName="EditorAutomationActor",NewName="EditorUtilityActor")
+ClassRedirects=(OldName="EditorAutomationActorComponent",NewName="EditorUtilityActorComponent")
+ClassRedirects=(OldName="EditorAutomationObject",NewName="EditorUtilityObject")
+ClassRedirects=(OldName="LandscapeBlueprintCustomBrush",NewName="/Script/LandscapeEditorUtilities.LandscapeBlueprintBrush")

+PropertyRedirects=(OldName="LandscapeLayerBrush.BPCustomBrush",NewName="LandscapeLayerBrush.BlueprintBrush")
+PropertyRedirects=(OldName="StructVariableDescription.bDontEditoOnInstance",NewName="bDontEditOnInstance")
+PropertyRedirects=(OldName="KismetMathLibrary.DegAtan2.A",NewName="Y")
+PropertyRedirects=(OldName="KismetMathLibrary.DegAtan2.B",NewName="X")
+PropertyRedirects=(OldName="KismetMathLibrary.Atan2.A",NewName="Y")
+PropertyRedirects=(OldName="KismetMathLibrary.Atan2.B",NewName="X")

; These aren't fully "clean" redirects, but the previous setup was broken and the necessary fix cannot be redirected to cleanly in all cases
; - If calling these on "self" in a BP where the self implements IUserListEntry, the redirect works cleanly
; - If calling these on an external object (rare), the redirect wi//UE4/Release-4.26/Engine/Plugins/VirtualProduction/VCamCore/Contentll point users to the new function to use, but the nodes will need manual fixup
+FunctionRedirects=(OldName="NativeUserListEntry.IsListItemSelected", NewName="UserListEntryLibrary.IsListItemSelected")
+FunctionRedirects=(OldName="NativeUserListEntry.IsListItemExpanded", NewName="UserListEntryLibrary.IsListItemExpanded")
+FunctionRedirects=(OldName="NativeUserListEntry.GetOwningListView", NewName="UserListEntryLibrary.GetOwningListView")
+FunctionRedirects=(OldName="UserObjectListEntry.GetListItemObject", NewName="UserObjectListEntryLibrary.GetListItemObject")

; 4.24
+PropertyRedirects=(OldName="NavDataConfig.NavigationDataClassName", NewName="NavDataConfig.NavDataClass")
+FunctionRedirects=(OldName="Actor.GetComponentsByClass", NewName="Actor.K2_GetComponentsByClass")

; Rename ClothingSystemRuntime to ClothingSystemRuntimeNv, base functionality moved to ClothingSystemRuntimeInterface.
+PackageRedirects=(OldName="/Script/ClothingSystemRuntime",NewName="/Script/ClothingSystemRuntimeNv")
+ClassRedirects=(OldName="/Script/ClothingSystemRuntime.ClothingSimulationFactoryNv",NewName="/Script/ClothingSystemRuntimeNv.ClothingSimulationFactoryNv")
+ClassRedirects=(OldName="/Script/ClothingSystemRuntime.ClothingSimulationInteractorNv",NewName="/Script/ClothingSystemRuntimeNv.ClothingSimulationInteractorNv")
+FunctionRedirects=(OldName="ClothingSystemRuntime.ClothingSimulationInteractorNv.SetAnimDriveSpringStiffness",NewName="ClothingSystemRuntimeNv.ClothingSimulationInteractorNv.SetAnimDriveSpringStiffness")
+FunctionRedirects=(OldName="ClothingSystemRuntime.ClothingSimulationInteractorNv.SetAnimDriveDamperStiffness",NewName="ClothingSystemRuntimeNv.ClothingSimulationInteractorNv.SetAnimDriveDamperStiffness")
+FunctionRedirects=(OldName="ClothingSystemRuntime.ClothingSimulationInteractorNv.EnableGravityOverride",NewName="ClothingSystemRuntimeNv.ClothingSimulationInteractorNv.EnableGravityOverride")
+FunctionRedirects=(OldName="ClothingSystemRuntime.ClothingSimulationInteractorNv.DisableGravityOverride",NewName="ClothingSystemRuntimeNv.ClothingSimulationInteractorNv.DisableGravityOverride")

; Redirect legacy ClothingSystemRuntime components to ClothingSystemRuntimeCommon
+ClassRedirects=(OldName="ClothingAsset",NewName="/Script/ClothingSystemRuntimeCommon.ClothingAssetCommon")
+StructRedirects=(OldName="ClothLODData",NewName="/Script/ClothingSystemRuntimeCommon.ClothLODDataCommon")
+StructRedirects=(OldName="ClothConfig",NewName="/Script/ClothingSystemRuntimeCommon.ClothConfig_Legacy")
+StructRedirects=(OldName="ClothParameterMask_PhysMesh",NewName="/Script/ClothingSystemRuntimeCommon.ClothParameterMask_Legacy")
+StructRedirects=(OldName="ClothConstraintSetup",NewName="/Script/ClothingSystemRuntimeCommon.ClothConstraintSetup_Legacy")
+EnumRedirects=(OldName="EClothingWindMethod",NewName="/Script/ClothingSystemRuntimeCommon.EClothingWindMethod_Legacy")

; Redirect common ClothingSystemRuntimeNv components to ClothingSystemRuntimeCommon
+ClassRedirects=(OldName="/Script/ClothingSystemRuntimeNv.ClothingAssetNv",NewName="/Script/ClothingSystemRuntimeCommon.ClothingAssetCommon")
+ClassRedirects=(OldName="/Script/ClothingSystemRuntimeNv.ClothLODDataNv",NewName="/Script/ClothingSystemRuntimeCommon.ClothLODDataCommon_Legacy")
+EnumRedirects=(OldName="MaskTarget_PhysMesh",NewName="/Script/ClothingSystemRuntimeCommon.EWeightMapTargetCommon")

; Deprecate the ClothLODDataCommon UObject class that was causing all sort of PostLoad issues
+ClassRedirects=(OldName="/Script/ClothingSystemRuntimeCommon.ClothLODDataCommon",NewName="/Script/ClothingSystemRuntimeCommon.ClothLODDataCommon_Legacy")

; Deprecate the ClothPhysicalMeshDataNv class, but keep it around as a legacy class for clothing asset backward compatibility
+ClassRedirects=(OldName="/Script/ClothingSystemRuntimeInterface.ClothPhysicalMeshDataBase",NewName="/Script/ClothingSystemRuntimeInterface.ClothPhysicalMeshDataBase_Legacy")
+ClassRedirects=(OldName="/Script/ClothingSystemRuntimeNv.ClothPhysicalMeshDataNv",NewName="/Script/ClothingSystemRuntimeNv.ClothPhysicalMeshDataNv_Legacy")

+FunctionRedirects=(OldName="SkeletalMeshComponent.GetSubInstanceByTag",NewName="SkeletalMeshComponent.GetLinkedAnimGraphInstanceByTag")
+FunctionRedirects=(OldName="SkeletalMeshComponent.GetSubInstancesByTag",NewName="SkeletalMeshComponent.GetLinkedAnimGraphInstancesByTag")
+PropertyRedirects=(OldName="SkeletalMeshComponent.GetLinkedAnimGraphInstancesByTag.OutSubInstances",NewName="OutLinkedInstances")
+FunctionRedirects=(OldName="SkeletalMeshComponent.SetSubInstanceClassByTag",NewName="SkeletalMeshComponent.LinkAnimGraphByTag")
+FunctionRedirects=(OldName="SkeletalMeshComponent.SetLayerOverlay",NewName="SkeletalMeshComponent.LinkAnimClassLayers")
+FunctionRedirects=(OldName="SkeletalMeshComponent.ClearLayerOverlay",NewName="SkeletalMeshComponent.UnlinkAnimClassLayers")
+FunctionRedirects=(OldName="SkeletalMeshComponent.GetLayerSubInstanceByGroup",NewName="SkeletalMeshComponent.GetLinkedAnimLayerInstanceByGroup")
+FunctionRedirects=(OldName="SkeletalMeshComponent.GetLayerSubInstanceByClass",NewName="SkeletalMeshComponent.GetLinkedAnimLayerInstanceByClass")
+FunctionRedirects=(OldName="AnimInstance.GetSubInstanceByTag",NewName="AnimInstance.GetLinkedAnimGraphInstanceByTag")
+FunctionRedirects=(OldName="AnimInstance.GetSubInstancesByTag",NewName="AnimInstance.GetLinkedAnimGraphInstancesByTag")
+PropertyRedirects=(OldName="AnimInstance.GetLinkedAnimGraphInstancesByTag.OutSubInstances",NewName="OutLinkedInstances")
+FunctionRedirects=(OldName="AnimInstance.SetSubInstanceClassByTag",NewName="AnimInstance.LinkAnimGraphByTag")
+FunctionRedirects=(OldName="AnimInstance.SetLayerOverlay",NewName="AnimInstance.LinkAnimClassLayers")
+FunctionRedirects=(OldName="AnimInstance.ClearLayerOverlay",NewName="AnimInstance.UnlinkAnimClassLayers")
+FunctionRedirects=(OldName="AnimInstance.GetLayerSubInstanceByGroup",NewName="AnimInstance.GetLinkedAnimLayerInstanceByGroup")
+FunctionRedirects=(OldName="AnimInstance.GetLayerSubInstanceByClass",NewName="AnimInstance.GetLinkedAnimLayerInstanceByClass")
+StructRedirects=(OldName="AnimNode_SubInstance",NewName="/Script/Engine.AnimNode_LinkedAnimGraph")
+StructRedirects=(OldName="AnimNode_SubInput",NewName="/Script/Engine.AnimNode_LinkedInputPose")
+StructRedirects=(OldName="AnimNode_Layer",NewName="/Script/Engine.AnimNode_LinkedAnimLayer")
+ClassRedirects=(OldName="/Script/AnimGraph.AnimGraphNode_SubInstanceBase",NewName="/Script/AnimGraph.AnimGraphNode_LinkedAnimGraphBase")
+ClassRedirects=(OldName="/Script/AnimGraph.AnimGraphNode_SubInstance",NewName="/Script/AnimGraph.AnimGraphNode_LinkedAnimGraph")
+ClassRedirects=(OldName="/Script/AnimGraph.AnimGraphNode_SubInput",NewName="/Script/AnimGraph.AnimGraphNode_LinkedInputPose")
+ClassRedirects=(OldName="/Script/AnimGraph.AnimGraphNode_Layer",NewName="/Script/AnimGraph.AnimGraphNode_LinkedAnimLayer")
+PropertyRedirects=(OldName="PersonaPreviewSceneDescription.SubInstanceTag",NewName="LinkedAnimGraphTag")
+EnumRedirects=(OldName="/Script/Engine.EPreviewAnimationBlueprintApplicationMethod",NewName="/Script/Engine.EPreviewAnimationBlueprintApplicationMethod",ValueChanges=(("OverlayLayer", "LinkedLayers"), ("SubInstance", "LinkedAnimGraph"))
+PropertyRedirects=(OldName="AnimClassData.SubInstanceNodeProperties",NewName="LinkedAnimGraphNodeProperties")
+PropertyRedirects=(OldName="AnimClassData.LayerNodeProperties",NewName="LinkedAnimLayerNodeProperties")
+ClassRedirects=(OldName="/Script/MeshEditingToolset.BaseBrushTool",NewName="/Script/InteractiveToolsFramework.BaseBrushTool")
+ClassRedirects=(OldName="/Script/MeshEditingToolset.BrushBaseProperties",NewName="/Script/InteractiveToolsFramework.BrushBaseProperties")

+ClassRedirects=(OldName="PixelStreamingInputComponent",NewName="PixelStreamerInputComponent")
+PropertyRedirects=(OldName="PixelStreamerInputComponent.OnPixelStreamingInputEvent",NewName="PixelStreamerInputComponent.OnInputEvent")

; 4.25
+EnumRedirects=(OldName="ECurveBlendOption",ValueChanges=(("ECurveBlendOption::MaxWeight", "ECurveBlendOption::Override"))
+ClassRedirects=(OldName="/Script/OnlineBlueprintSupport.K2Node_LatentOnlineCall", NewName="/Script/BlueprintGraph.K2Node_AsyncAction")
+ClassRedirects=(OldName="/Script/Kismet.K2Node_AsyncAction", NewName="/Script/BlueprintGraph.K2Node_AsyncAction")

;4.26
+ClassRedirects=(OldName="MovieSceneSpawnTrack",NewName="/Script/MovieScene.MovieSceneSpawnTrack")
+ClassRedirects=(OldName="MovieSceneSpawnSection",NewName="/Script/MovieScene.MovieSceneSpawnSection")
+ClassRedirects=(OldName="MovieSceneBoolSection",NewName="/Script/MovieScene.MovieSceneBoolSection")
+ClassRedirects=(OldName="PhysicalMaterialPropertyBase",NewName="/Script/PhysicsCore.PhysicalMaterialPropertyBase")
+ClassRedirects=(OldName="PhysicalMaterial",NewName="/Script/PhysicsCore.PhysicalMaterial")
+ClassRedirects=(OldName="ChaosPhysicalMaterial",NewName="/Script/PhysicsCore.ChaosPhysicalMaterial")
+ClassRedirects=(OldName="SubmixEffectReverbFastPreset",NewName="/Script/AudioMixer.SubmixEffectReverbPreset")
+EnumRedirects=(OldName="EPhysicalSurface",NewName="/Script/PhysicsCore.EPhysicalSurface")
+EnumRedirects=(OldName="ECollisionTraceFlag",NewName="/Script/PhysicsCore.ECollisionTraceFlag")
+EnumRedirects=(OldName="EPhysicsType",NewName="/Script/PhysicsCore.EPhysicsType",ValueChanges=(("PhysType_Fixed", "PhysType_Kinematic"), ("PhysType_Unfixed", "PhysType_Simulated")))
+EnumRedirects=(OldName="EBodyCollisionResponse",NewName="/Script/PhysicsCore.EBodyCollisionResponse")
+EnumRedirects=(OldName="EFrictionCombineMode",NewName="/Script/PhysicsCore.EFrictionCombineMode")
+EnumRedirects=(OldName="ELinearConstraintMotion",NewName="/Script/PhysicsCore.ELinearConstraintMotion")
+EnumRedirects=(OldName="ERadialImpulseFalloff",NewName="/Script/PhysicsCore.ERadialImpulseFalloff")
+EnumRedirects=(OldName="ESleepFamily",NewName="/Script/PhysicsCore.ESleepFamily")
+EnumRedirects=(OldName="EAngularConstraintMotion",NewName="/Script/PhysicsCore.EAngularConstraintMotion")
+EnumRedirects=(OldName="EConstraintFrame",NewName="/Script/PhysicsCore.EConstraintFrame")
+StructRedirects=(OldName="/Script/AssetRegistry.ARFilter",NewName="/Script/CoreUObject.ARFilter")
+StructRedirects=(OldName="/Script/AssetRegistry.AssetBundleData",NewName="/Script/CoreUObject.AssetBundleData")
+StructRedirects=(OldName="/Script/AssetRegistry.AssetBundleEntry",NewName="/Script/CoreUObject.AssetBundleEntry")
+StructRedirects=(OldName="/Script/AssetRegistry.AssetData",NewName="/Script/CoreUObject.AssetData")
+StructRedirects=(OldName="SubmixEffectReverbFastSettings",NewName="/Script/AudioMixer.SubmixEffectReverbSettings")

+FunctionRedirects=(OldName="KismetInputLibrary.Key_IsFloatAxis",NewName="KismetInputLibrary.Key_IsAxis1D")
+FunctionRedirects=(OldName="KismetMathLibrary.Conv_VectorToQuaterion",NewName="KismetMathLibrary.Conv_VectorToQuaternion")
+FunctionRedirects=(OldName="KismetMathLibrary.Conv_Vector4ToQuaterion",NewName="KismetMathLibrary.Conv_Vector4ToQuaternion")

; HololensAR refactoring
+FunctionRedirects=(OldName="PinComponentToARPin",NewName="/Script/AugmentedReality.ARBlueprintLibrary.PinComponentToARPin")
+FunctionRedirects=(OldName="IsWMRAnchorStoreReady",NewName="/Script/AugmentedReality.ARBlueprintLibrary.IsARPinLocalStoreReady")
+FunctionRedirects=(OldName="RemoveAllARPinsFromWMRAnchorStore",NewName="/Script/AugmentedReality.ARBlueprintLibrary.RemoveAllARPinsFromLocalStore")
+FunctionRedirects=(OldName="AzureSpatialAnchorsLibrary.CreateCloudAnchor",NewName="AzureSpatialAnchorsLibrary.ConstructCloudAnchor")

+PackageRedirects=(OldName="/Script/FieldSystemCore",NewName="/Script/Chaos")
+PackageRedirects=(OldName="/Script/GeometryCollectionSimulationCore",NewName="/Script/Chaos")

; VirtualCamera deprecation and cleanup
+ClassRedirects=(OldName="/VirtualCamera/VirtualCameraGameMode.VirtualCameraGameMode",NewName="/VirtualCamera/Deprecated/Deprecated_VirtualCameraGameMode.Deprecated_VirtualCameraGameMode")
+ClassRedirects=(OldName="/VirtualCamera/VirtualCameraPawn.VirtualCameraPawn",NewName="/VirtualCamera/Deprecated/Deprecated_VirtualCameraPawn.Deprecated_VirtualCameraPawn")
+ClassRedirects=(OldName="/VirtualCamera/VirtualCameraPlayerController.VirtualCameraPlayerController",NewName="/VirtualCamera/Deprecated/Deprecated_VirtualCameraPlayerController.Deprecated_VirtualCameraPlayerController")
+ClassRedirects=(OldName="/VirtualCamera/Widgets/VirtualCameraJoysticks.VirtualCameraJoysticks",NewName="/VirtualCamera/Deprecated/Widgets/Deprecated_VirtualCameraJoysticks.Deprecated_VirtualCameraJoysticks")

+PackageRedirects=(OldName="/VirtualCamera/Widgets/SequenceButton",NewName="/VirtualCamera/Deprecated/Widgets/SequenceButton")
+PackageRedirects=(OldName="/VirtualCamera/Widgets/SequenceMenu",NewName="/VirtualCamera/Deprecated/Widgets/SequenceMenu")
+PackageRedirects=(OldName="/VirtualCamera/Widgets/SequenceMenu",NewName="/VirtualCamera/Deprecated/Widgets/SequenceMenu")

+PackageRedirects=(OldName="/VirtualCamera/Textures/VPLeftJoystick",NewName="/VirtualCamera/Deprecated/Textures/VPLeftJoystick")
+PackageRedirects=(OldName="/VirtualCamera/Textures/VPLeftJoystickBG",NewName="/VirtualCamera/Deprecated/Textures/VPLeftJoystickBG")
+PackageRedirects=(OldName="/VirtualCamera/Textures/VPRightJoystick",NewName="/VirtualCamera/Deprecated/Textures/VPRightJoystick")
+PackageRedirects=(OldName="/VirtualCamera/Textures/VPRightJoystickBG",NewName="/VirtualCamera/Deprecated/Textures/VPRightJoystickBG")

+PackageRedirects=(OldName="/VCamCore/Assets/VCam_EmptyVisibleUMG",NewName="/VirtualCamera/VCamCore/Assets/VCam_EmptyVisibleUMG")

+PackageRedirects=(OldName="/Game/VirtualCamera/Blueprints/Modifiers/SinModifier", NewName="/VirtualCamera/VCamCore/Blueprints/Modifiers/SinModifier")
+PackageRedirects=(OldName="/Game/VirtualCamera/Blueprints/Modifiers/FollowModifier", NewName="/VirtualCamera/VCamCore/Blueprints/Modifiers/FollowModifier")
+PackageRedirects=(OldName="/Game/VirtualCamera/Blueprints/Modifiers/GamepadModifier", NewName="/VirtualCamera/VCamCore/Blueprints/Modifiers/GamepadModifier")

+PackageRedirects=(OldName="/Game/VirtualCamera/Blueprints/TestAJA", NewName="/VirtualCamera/VCamCore/Blueprints/TestAJA")
+PackageRedirects=(OldName="/Game/VirtualCamera/Assets/TestComposureMaterial_AOverB", NewName="/VirtualCamera/VCamCore/Assets/TestComposureMaterial_AOverB")
+PackageRedirects=(OldName="/Game/VirtualCamera/Assets/TestComposureRenderTargetOutput", NewName="/VirtualCamera/VCamCore/Assets/TestComposureRenderTargetOutput")
+PackageRedirects=(OldName="/Game/VirtualCamera/Assets/VCamSample_AOverBComposureMaterial", NewName="/VirtualCamera/VCamCore/Assets/VCamSample_AOverBComposureMaterial")
+PackageRedirects=(OldName="/Game/VirtualCamera/Assets/VCamSample_ComposureRenderTargetOutput", NewName="/VirtualCamera/VCamCore/Assets/VCamSample_ComposureRenderTargetOutput")

;4.27
+ClassRedirects=(OldName="CameraShake",NewName="/Script/Engine.MatineeCamerashake")
+EnumRedirects=(OldName="ECameraAnimPlaySpace",NewName="/Script/Engine.ECameraShakePlaySpace")
+FunctionRedirects=(OldName="PlayerController.ClientPlayCameraShake",NewName="ClientStartCameraShake")
+FunctionRedirects=(OldName="PlayerController.ClientPlayCameraShakeFromSource",NewName="ClientStartCameraShakeFromSource")
+FunctionRedirects=(OldName="PlayerCameraManager.PlayCameraShake",NewName="StartMatineeCameraShake")
+FunctionRedirects=(OldName="PlayerCameraManager.PlayCameraShakeFromSource",NewName="StartMatineeCameraShakeFromSource")
+FunctionRedirects=(OldName="CameraShakeSourceComponent.Play",NewName="Start")
+FunctionRedirects=(OldName="CameraShakeSourceComponent.PlayCameraShake",NewName="StartCameraShake")
+PropertyRedirects=(OldName="CameraShakeSourceComponent.bAutoPlay",NewName="bAutoStart")

[CoreUObject.Metadata]
; Note: UnrealHeaderTool should be rerun after making changes to MetadataRedirects to catch any keys specified in class headers
+MetadataRedirects=(OldKey="K2Protected", NewKey="BlueprintProtected")
+MetadataRedirects=(OldKey="K2UnsafeForConstructionScripts", NewKey="UnsafeDuringActorConstruction")
+MetadataRedirects=(OldKey="KismetType", NewKey="BlueprintType")
+MetadataRedirects=(OldKey="KismetInternalUseOnly", NewKey="BlueprintInternalUseOnly")
+MetadataRedirects=(OldKey="KismetSpawnableComponent", NewKey="BlueprintSpawnableComponent")
+MetadataRedirects=(OldKey="K2ExposeToSpawn", NewKey="ExposeOnSpawn")
+MetadataRedirects=(OldKey="K2Category", NewKey="Category")
+MetadataRedirects=(OldKey="KismetDeprecated", NewKey="DeprecatedFunction")
+MetadataRedirects=(OldKey="K2CompactNode", NewKey="CompactNodeTitle")
+MetadataRedirects=(OldKey="MenuCategory", NewKey="Category")
+MetadataRedirects=(OldKey="ArrayPointerParm", NewKey="TargetArrayParm")
+MetadataRedirects=(OldKey="FriendlyName", NewKey="DisplayName")

[EnumRemap]
; Entries can be added to this section of DefaultEngine.ini to remap metadata for engine enums to game-specific display values
; TEXTUREGROUP_Project01.DisplayName=ProjectSpecificName

[PlatformInterface]
CloudStorageInterfaceClassName=
InGameAdManagerClassName=

[/Script/Engine.UserInterfaceSettings]
UIScaleRule=ShortestSide
UIScaleCurve=(EditorCurveData=(Keys=((Time=480,Value=0.444),(Time=720,Value=0.666),(Time=1080,Value=1.0),(Time=8640,Value=8.0))),ExternalCurve=None)
bLoadWidgetsOnDedicatedServer=True
bAllowHighDPIInGameMode=True

[/Script/Engine.GameEngine]
MaxDeltaTime=0
ServerFlushLogInterval=30

[Engine.StartupPackages]
+Package=/Engine/EngineMaterials/BlinkingCaret
+Package=/Engine/EngineMaterials/DefaultBokeh
+Package=/Engine/EngineMaterials/DefaultBloomKernel
+Package=/Engine/EngineMaterials/DefaultDeferredDecalMaterial
;+Package=/Engine/EngineMaterials/DefaultPostProcessMaterial
+Package=/Engine/EngineMaterials/DefaultDiffuse
+Package=/Engine/EngineMaterials/DefaultLightFunctionMaterial
+Package=/Engine/EngineMaterials/WorldGridMaterial
+Package=/Engine/EngineMaterials/DefaultMaterial
+Package=/Engine/EngineMaterials/DefaultNormal
+Package=/Engine/EngineMaterials/DefaultPhysicalMaterial
+Package=/Engine/EngineMaterials/DefaultWhiteGrid
+Package=/Engine/EngineMaterials/EditorBrushMaterial
+Package=/Engine/EngineMaterials/EmissiveMeshMaterial
+Package=/Engine/EngineMaterials/Good64x64TilingNoiseHighFreq
+Package=/Engine/EngineMaterials/Grid
+Package=/Engine/EngineMaterials/Grid_N
+Package=/Engine/EngineMaterials/LandscapeHolePhysicalMaterial
+Package=/Engine/EngineMaterials/MiniFont
+Package=/Engine/EngineMaterials/PaperDiffuse
+Package=/Engine/EngineMaterials/PaperNormal
+Package=/Engine/EngineMaterials/PhysMat_Rubber
+Package=/Engine/EngineMaterials/PreintegratedSkinBRDF
+Package=/Engine/EngineMaterials/RemoveSurfaceMaterial
+Package=/Engine/EngineMaterials/WeightMapPlaceholderTexture

; Console platforms will remove EngineDebugMaterials from their StartupPackages
+Package=/Engine/EngineDebugMaterials/BoneWeightMaterial
+Package=/Engine/EngineDebugMaterials/DebugMeshMaterial
+Package=/Engine/EngineDebugMaterials/GeomMaterial
+Package=/Engine/EngineDebugMaterials/HeatmapGradient
+Package=/Engine/EngineDebugMaterials/LevelColorationLitMaterial
+Package=/Engine/EngineDebugMaterials/LevelColorationUnlitMaterial
+Package=/Engine/EngineDebugMaterials/MAT_LevelColorationLitLightmapUV
+Package=/Engine/EngineDebugMaterials/ShadedLevelColorationLitMaterial
+Package=/Engine/EngineDebugMaterials/ShadedLevelColorationUnlitMateri
+Package=/Engine/EngineDebugMaterials/TangentColorMap
+Package=/Engine/EngineDebugMaterials/VertexColorMaterial
+Package=/Engine/EngineDebugMaterials/VertexColorViewMode_AlphaAsColor
+Package=/Engine/EngineDebugMaterials/VertexColorViewMode_BlueOnly
+Package=/Engine/EngineDebugMaterials/VertexColorViewMode_ColorOnly
+Package=/Engine/EngineDebugMaterials/VertexColorViewMode_GreenOnly
+Package=/Engine/EngineDebugMaterials/VertexColorViewMode_RedOnly
+Package=/Engine/EngineDebugMaterials/WireframeMaterial

+Package=/Engine/EngineSounds/WhiteNoise

+Package=/Engine/EngineFonts/SmallFont
+Package=/Engine/EngineFonts/TinyFont
+Package=/Engine/EngineFonts/Roboto
+Package=/Engine/EngineFonts/RobotoTiny

; only needed for TextRender feature (3d Text in world)
+Package=/Engine/EngineMaterials/DefaultTextMaterialTranslucent
+Package=/Engine/EngineFonts/RobotoDistanceField

[Core.Log]
; This section can be used in DefaultEngine.ini to override the display level for different log categories, using lines like the following:
; LogTemp=warning

[Core.System]
+Paths=../../../Engine/Content
+Paths=%GAMEDIR%Content
CutdownPaths=%GAMEDIR%CutdownPackages
ZeroEngineVersionWarning=True
UseStrictEngineVersioning=True
CanStripEditorOnlyExportsAndImports=True
CanSkipEditorReferencedPackagesWhenCooking=False
CanUseUnversionedPropertySerialization=False
TestUnversionedPropertySerializationWhenCooking=False
DetailedCallstacksInNonMonolithicBuilds=True
UseSeperateBulkDataFiles=False
GameThreadHeartBeatStartSuspended=False
GameThreadHeartBeatHitchDuration=-1.0
GameThreadHeartBeatStackWalk=false
AllowBulkDataInIoStore=True
LegacyBulkDataOffsets=False
HangDuration=0.0
AssetLogShowsDiskPath=True
AssetLogShowsAbsolutePath=True

[/Script/Engine.StreamingSettings]
s.MinBulkDataSizeForAsyncLoading=131072
s.AsyncLoadingThreadEnabled=False
s.EventDrivenLoaderEnabled=True
s.WarnIfTimeLimitExceeded=False
s.TimeLimitExceededMultiplier=1.5
s.TimeLimitExceededMinTime=0.005
s.UseBackgroundLevelStreaming=True
s.PriorityAsyncLoadingExtraTime=15.0
s.LevelStreamingActorsUpdateTimeLimit = 5.0
s.PriorityLevelStreamingActorsUpdateExtraTime=5.0
s.LevelStreamingComponentsRegistrationGranularity = 10
s.UnregisterComponentsTimeLimit = 1.0
s.LevelStreamingComponentsUnregistrationGranularity = 5
s.MaxPackageSummarySize=16384
s.FlushStreamingOnExit=True
+FixedBootOrder=/Script/Engine/Default__SoundBase
+FixedBootOrder=/Script/Engine/Default__MaterialInterface
+FixedBootOrder=/Script/Engine/Default__DeviceProfileManager

[/Script/Engine.GarbageCollectionSettings]
gc.MaxObjectsNotConsideredByGC=1
gc.SizeOfPermanentObjectPool=0
gc.FlushStreamingOnGC=0
gc.NumRetriesBeforeForcingGC=10
gc.AllowParallelGC=True
; pick a fractional number to keep phase shifting and avoid collisions
gc.TimeBetweenPurgingPendingKillObjects=61.1
gc.MaxObjectsInEditor=25165824
gc.IncrementalBeginDestroyEnabled=True
gc.CreateGCClusters=True
gc.MinGCClusterSize=5
gc.ActorClusteringEnabled=False
gc.BlueprintClusteringEnabled=False
gc.UseDisregardForGCOnDedicatedServers=False
gc.MultithreadedDestructionEnabled=True

[Internationalization]
; These are the paths for localizing the core engine itself, the localized game content settings are in *Game.ini
+LocalizationPaths=../../../Engine/Content/Localization/Engine
+CultureDisplayNameSubstitutes="Taiwan;Chinese Taipei"
+CultureDisplayNameSubstitutes="ja;\u53f0\u6e7e;\u30c1\u30e3\u30a4\u30cb\u30fc\u30ba\u30bf\u30a4\u30da\u30a4"
+CultureDisplayNameSubstitutes="\u53f0\u6e7e;\u4e2d\u534e\u53f0\u5317"
+CultureDisplayNameSubstitutes="\u53f0\u7063;\u4e2d\u83ef\u53f0\u5317"
+CultureDisplayNameSubstitutes="\ub300\ub9cc;\uc911\ud654 \ud0c0\uc774\ubca0\uc774"

[Audio]
UnfocusedVolumeMultiplier=0.0
UseAudioThread=true
EnableAudioMixer=false
DeferStartupPrecache=false

[AudioChannelAzimuthMap]
FrontLeft=330
FrontRight=30
FrontCenter=0
BackLeft=210
BackRight=150
FrontLeftOfCenter=15
FrontRightOfCenter=345
BackCenter=180
SideLeft=250
SideRight=110

; These are the default channel orderings to use when using pro-audio interfaces that don't report channel order
[AudioDefaultChannelOrder]
FrontLeft=0
FrontRight=1
FrontCenter=2
LowFrequency=3
SideLeft=4
SideRight=5
BackLeft=6
BackRight=7

[/Script/Engine.AudioSettings]
DefaultSoundClassName=/Engine/EngineSounds/Master.Master
DefaultMediaSoundClassName=/Engine/EngineSounds/Master.Master
DefaultSoundSubmixName=/Engine/EngineSounds/MasterSubmix.MasterSubmix
MasterSubmix=/Engine/EngineSounds/Submixes/MasterSubmixDefault.MasterSubmixDefault
ReverbSubmix=/Engine/EngineSounds/Submixes/MasterReverbSubmixDefault.MasterReverbSubmixDefault
EQSubmix=/Engine/EngineSounds/Submixes/MasterEQSubmixDefault.MasterEQSubmixDefault
AmbisonicSubmix=/Engine/EngineSounds/Submixes/MasterAmbisonicSubmixDefault.MasterAmbisonicSubmixDefault
LowPassFilterResonance=0.9
MaximumConcurrentStreams=2
DialogueFilenameFormat="{DialogueGuid}_{ContextId}"

[/Script/Engine.SoundGroups]
+SoundGroupProfiles=(SoundGroup=SOUNDGROUP_Default, bAlwaysDecompressOnLoad=false, DecompressedDuration=5)
+SoundGroupProfiles=(SoundGroup=SOUNDGROUP_Effects, bAlwaysDecompressOnLoad=false, DecompressedDuration=5)
+SoundGroupProfiles=(SoundGroup=SOUNDGROUP_UI, bAlwaysDecompressOnLoad=false, DecompressedDuration=5)
+SoundGroupProfiles=(SoundGroup=SOUNDGROUP_Music, bAlwaysDecompressOnLoad=false, DecompressedDuration=0)
+SoundGroupProfiles=(SoundGroup=SOUNDGROUP_Voice, bAlwaysDecompressOnLoad=false, DecompressedDuration=0)

[/Script/Engine.Player]
ConfiguredInternetSpeed=100000
ConfiguredLanSpeed=100000

[/Script/Engine.NetDriver]
+ChannelDefinitions=(ChannelName=Control, ClassName=/Script/Engine.ControlChannel, StaticChannelIndex=0, bTickOnCreate=true, bServerOpen=false, bClientOpen=true, bInitialServer=false, bInitialClient=true)
+ChannelDefinitions=(ChannelName=Voice, ClassName=/Script/Engine.VoiceChannel, StaticChannelIndex=1, bTickOnCreate=true, bServerOpen=true, bClientOpen=true, bInitialServer=true, bInitialClient=true)
+ChannelDefinitions=(ChannelName=Actor, ClassName=/Script/Engine.ActorChannel, StaticChannelIndex=-1, bTickOnCreate=false, bServerOpen=true, bClientOpen=false, bInitialServer=false, bInitialClient=false)

[/Script/OnlineSubsystemUtils.IpNetDriver]
AllowPeerConnections=False
AllowPeerVoice=False
ConnectionTimeout=60.0
InitialConnectTimeout=60.0
RecentlyDisconnectedTrackingTime=120
TimeoutMultiplierForUnoptimizedBuilds=1
KeepAliveTime=0.2
MaxClientRate=100000
MaxInternetClientRate=100000
RelevantTimeout=5.0
SpawnPrioritySeconds=1.0
ServerTravelPause=4.0
NetServerMaxTickRate=30
MaxNetTickRate=120
NetConnectionClassName="/Script/OnlineSubsystemUtils.IpConnection"
MaxPortCountToTry=512
ResolutionConnectionTimeout=20.0

[DDoSDetection]
bDDoSDetection=false
bDDoSAnalytics=false
DDoSLogSpamLimit=64
HitchTimeQuotaMS=500
HitchFrameTolerance=3
+DetectionSeverity=Burst
+DetectionSeverity=PersistentBurst
+DetectionSeverity=DDoS
+DetectionSeverity=ExpensiveDDoS
+DetectionSeverity=DebilitatingDDoS

; Tune just above the servers worst expected single frame packet count - DDoS detection is effectively off, during this stage
[DDoSDetection.Burst]
EscalateQuotaPacketsPerSec=400
EscalateQuotaDisconnPacketsPerSec=3200
EscalateQuotaBadPacketsPerSec=100

; If a bad enough Burst occurs, automatically escalate to this - enabling time quotas has a cost
[DDoSDetection.PersistentBurst]
EscalateQuotaPacketsPerSec=800
EscalateQuotaDisconnPacketsPerSec=3200
EscalateQuotaBadPacketsPerSec=200
EscalateTimeQuotaMSPerFrame=8
CooloffTime=60

; Trigger limitation of new connections - tune previous sections escalation, to an unrealistic time/packet quota that would indicate a DDoS
[DDoSDetection.DDoS]
EscalateTimeQuotaMSPerFrame=50
PacketLimitPerFrame=100
PacketTimeLimitMSPerFrame=16
CooloffTime=60

; Ignore new connections - tune previous sections escalation, to a time quota that is costly enough to the server, to warrant this
[DDoSDetection.ExpensiveDDoS]
EscalateTimeQuotaMSPerFrame=66
PacketLimitPerFrame=0
CooloffTime=10

; Ignore new connections, and limit existing connections - tune previous sections escalation, to a time quota that is so costly that this can't be avoided
[DDoSDetection.DebilitatingDDoS]
PacketLimitPerFrame=0
NetConnPacketTimeLimitMSPerFrame=100
CooloffTime=10

[/Script/Engine.DemoNetDriver]
NetConnectionClassName="/Script/Engine.DemoNetConnection"
DemoSpectatorClass=Engine.PlayerController
SpawnPrioritySeconds=60.0
!ChannelDefinitions=CLEAR_ARRAY
+ChannelDefinitions=(ChannelName=Control, ClassName=/Script/Engine.ControlChannel, StaticChannelIndex=0, bTickOnCreate=true, bServerOpen=false, bClientOpen=true, bInitialServer=false, bInitialClient=true)
+ChannelDefinitions=(ChannelName=Actor, ClassName=/Script/Engine.ActorChannel, StaticChannelIndex=-1, bTickOnCreate=false, bServerOpen=true, bClientOpen=false, bInitialServer=false, bInitialClient=false)

[TextureStreaming]
NeverStreamOutRenderAssets=False
MinTextureResidentMipCount=7
PoolSize=160
MemoryMargin=5
MinFudgeFactor=1
LoadMapTimeLimit=20.0
LightmapStreamingFactor=0.2
ShadowmapStreamingFactor=0.2
MaxLightmapRadius=10000.0
AllowStreamingLightmaps=True
UseDynamicStreaming=True
BoostPlayerTextures=3.0

[/Script/UnrealEd.EditorEngine]
LocalPlayerClassName=/Script/Engine.LocalPlayer
GameCommandLine=-log
FOVAngle=90.000000
GodMode=True
UseAxisIndicator=True
MatineeCurveDetail=0.1
HeightMapExportClassName="TerrainHeightMapExporterTextT3D"
bGroupingActive=true
bCustomCameraAlignEmitter=true
CustomCameraAlignEmitterDistance=100.0
bDrawSocketsInGMode=false
bSmoothFrameRate=false
SmoothedFrameRateRange=(LowerBound=(Type="ERangeBoundTypes::Inclusive",Value=5),UpperBound=(Type="ERangeBoundTypes::Inclusive",Value=120))
UseOldStyleMICEditorGroups=true
InEditorGameURLOptions=

[/Script/UnrealEd.UnrealEdEngine]
AutoSaveIndex=0
+TemplateMapInfos=(ThumbnailTexture=Texture2D'/Engine/Maps/Templates/Thumbnails/Default.Default',Map="/Engine/Maps/Templates/Template_Default")
+TemplateMapInfos=(ThumbnailTexture=Texture2D'/Engine/Maps/Templates/Thumbnails/TimeOfDay.TimeOfDay',Map="/Engine/Maps/Templates/TimeOfDay_Default")
+TemplateMapInfos=(ThumbnailTexture=Texture2D'/Engine/Maps/Templates/Thumbnails/VR-Basic.VR-Basic',Map="/Engine/Maps/Templates/VR-Basic")

+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Cross
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Cross_Mat
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/PhAT_BoneSelectedMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/PhAT_ElemSelectedMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/PhAT_JointLimitMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/PhAT_NoCollisionMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/PhAT_UnselectedMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/TargetIcon
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Tick
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Tick_Mat
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetGridVertexColorMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetGridVertexColorMaterial_Ma
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetMaterial_Current
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetMaterial_X
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetMaterial_Y
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetMaterial_Z
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/WidgetVertexColorMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/LevelGridMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/TilingAAGrid
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/TilingAALineBoxFiltered
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/TilingAALineIntegral
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/ParticleSystems/PSysThumbnail_NoImage
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/ParticleSystems/PSysThumbnail_OOD
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Thumbnails/FloorPlaneMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMaterials/Thumbnails/SkySphereMaterial

+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMeshes/EditorCube
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMeshes/EditorCylinder
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMeshes/EditorPlane
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMeshes/EditorSkySphere
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorMeshes/EditorSphere

+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/Bad
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/Bkgnd
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/BkgndHi
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/BSPVertex
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/MatInstActSprite
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/SceneManager
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/SmallFont
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Actor
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_DecalActorIcon
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_TextRenderActorIcon
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Emitter
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_ExpoHeightFog
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_KBSJoint
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_KHinge
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_KPrismatic
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_LevelSequence
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_NavP
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Note
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Player
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_RadForce
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_ReflActorIcon
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Terrain
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Thruster
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_Trigger
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/S_VectorFieldVol
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/AI/S_NavLink
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightDirectional
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightDirectionalMove
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightError
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightPoint
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightPointMove
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightSpot
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/LightIcons/S_LightSpotMove
+PackagesToBeFullyLoadedAtStartup=/Engine/EditorResources/Spline/T_Loft_Spline

+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/BlinkingCaret
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultBokeh
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultBloomKernel
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultDeferredDecalMaterial
;+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultPostProcessMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultDiffuse
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultLightFunctionMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/WorldGridMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultNormal
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultPhysicalMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/DefaultWhiteGrid
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/EditorBrushMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/Good64x64TilingNoiseHighFreq
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/Grid
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/Grid_N
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/HighResScreenshot
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/HighResScreenshotMask
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/HighResScreenshotCaptureRegion
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/LandscapeHolePhysicalMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/MiniFont
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/PaperDiffuse
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/PaperNormal
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/PhysMat_Rubber
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/PreintegratedSkinBRDF
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/RemoveSurfaceMaterial
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineMaterials/WeightMapPlaceholderTexture

+PackagesToBeFullyLoadedAtStartup=/Engine/EngineFonts/SmallFont
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineFonts/TinyFont
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineFonts/Roboto
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineFonts/RobotoTiny

+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/Black
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/DefaultTexture
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/DefaultTextureCube
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/M_StreamingPause
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/WhiteSquareTexture
+PackagesToBeFullyLoadedAtStartup=/Engine/EngineResources/GradientTexture0

[DevOptions.Shaders]
; See FShaderCompilingManager for documentation on what these do
bAllowCompilingThroughWorkers=True
bAllowAsynchronousShaderCompiling=True
; Make sure we don't starve loading threads
NumUnusedShaderCompilingThreads=3
; Make sure the game has enough cores available to maintain reasonable performance
NumUnusedShaderCompilingThreadsDuringGame=4
; Batching multiple jobs to reduce file overhead, but not so many that latency of blocking compiles is hurt
MaxShaderJobBatchSize=10
bPromptToRetryFailedShaderCompiles=True
bLogJobCompletionTimes=False
; Only using 10ms of game thread time per frame to process async shader maps
ProcessGameThreadTargetTime=.01
; For regular machines, wait this many seconds before exiting an unused worker (float value)
WorkerTimeToLive=20
; For build machines, wait this many seconds before exiting an unused worker (float value)
BuildWorkerTimeToLive=1200
; Set process priority for ShaderCompileWorker (0 is normal)
WorkerProcessPriority=-1

; These values are for build machines only currently to reduce the number of SCWs spawned to reduce memory pressure
bUseVirtualCores = False
; CookerMemoryUsedInGB = 49
; MemoryToLeaveForTheOSInGB = 3
; MemoryUsedPerSCWProcessInGB = 0.4
; MinSCWsToSpawnBeforeWarning = 8

; Use SCW memory pressure calculations regardless of whether cooking is done on a build machine
; Note: CookerMemoryUsedInGB, MemoryToLeaveForTheOSInGB, MemoryUsedPerSCWProcessInGB must all be set to enable
bForceUseSCWMemoryPressureLimits = False

[LogFiles]
PurgeLogsDays=5
MaxLogFilesOnDisk=10
LogTimes=True

[Kismet]
AllowDerivedBlueprints=true
CompileDisplaysTextBackend=false
CompileDisplaysBinaryBackend=false
CompileDisplaysAnimBlueprintBackend=false
bTurnOffEditorConstructionScript=false
bLogPrintStringSource=true
PrintStringDuration=2.0
bUseLocalGraphVariables=false
bPersistentUberGraphFrame=true
bReinstanceOnlyWhenNecessary=true
bChangeDefaultValueWithoutReinstancing=true
bDisplaysLayout=false
bOptimizeExecutionFlowStack=true
bOptimizeAdjacentStates=true
bEnableInheritableComponents=true
bDeferDependencyLoads=true
bForceDisableCookedDependencyDeferring=false
bExecutionAfterReturn=false
bCanSuppressAccessViolation=false
bLoadNativeConvertedBPClassInEditor=false
bIgnoreCompileOnLoadErrorsOnBuildMachine=false

[/Script/Engine.Blueprint]
bRecompileOnLoad=true

[/Script/Engine.LevelScriptBlueprint]
bRecompileOnLoad=true

[/Script/Engine.AnimBlueprint]
bRecompileOnLoad=true

[CustomStats]
+LD=Streaming fudge factor
+LD=FrameTime
+LD=Terrain Smooth Time
+LD=Terrain Render Time
+LD=Terrain Triangles
+LD=Static Mesh Tris
+LD=Skel Mesh Tris
+LD=Skel Verts CPU Skin
+LD=Skel Verts GPU Skin
+LD=30+ FPS
+LD=Total CPU rendering time
+LD=Total GPU rendering time
+LD=Occluded primitives
+LD=Projected shadows
+LD=Visible static mesh elements
+LD=Visible dynamic primitives
+LD=Texture Pool Size
+LD=Physical Memory Used
+LD=Virtual Memory Used
+LD=Audio Memory Used
+LD=Texture Memory Used
+LD=360 Texture Memory Used
+LD=Animation Memory
+LD=Navigation Memory
+LD=Vertex Lighting Memory
+LD=StaticMesh Vertex Memory
+LD=StaticMesh Index Memory
+LD=SkeletalMesh Vertex Memory
+LD=SkeletalMesh Index Memory
+MEMLEAN=Virtual Memory Used
+MEMLEAN=Audio Memory Used
+MEMLEAN=Animation Memory
+MEMLEAN=Vertex Lighting Memory
+MEMLEAN=StaticMesh Vertex Memory
+MEMLEAN=StaticMesh Index Memory
+MEMLEAN=SkeletalMesh Vertex Memory
+MEMLEAN=SkeletalMesh Index Memory
+MEMLEAN=VertexShader Memory
+MEMLEAN=PixelShader Memory
+MEMLEAN=Navigation Memory
+GameThread=Async Loading Time
+GameThread=Audio Update Time
+GameThread=FrameTime
+GameThread=HUD Time
+GameThread=Input Time
+GameThread=Kismet Time
+GameThread=Move Actor Time
+GameThread=RHI Game Tick
+GameThread=RedrawViewports
+GameThread=Script time
+GameThread=Tick Time
+GameThread=Update Components Time
+GameThread=World Tick Time
+GameThread=Async Work Wait
+GameThread=PerFrameCapture
+GameThread=DynamicLightEnvComp Tick
+Mobile=ES2 Draw Calls
+Mobile=ES2 Draw Calls (UP)
+Mobile=ES2 Triangles Drawn
+Mobile=ES2 Triangles Drawn (UP)
+Mobile=ES2 Program Count
+Mobile=ES2 Program Count (PP)
+Mobile=ES2 Program Changes
+Mobile=ES2 Uniform Updates (Bytes)
+Mobile=ES2 Base Texture Binds
+Mobile=ES2 Detail Texture Binds
+Mobile=ES2 Lightmap Texture Binds
+Mobile=ES2 Environment Texture Binds
+Mobile=ES2 Bump Offset Texture Binds
+Mobile=Frustum Culled primitives
+Mobile=Statically occluded primitives
+SplitScreen=Processed primitives
+SplitScreen=Mesh draw calls
+SplitScreen=Mesh Particles
+SplitScreen=Particle Draw Calls

[MemReportCommands]
; These commands are run when memreport is executed, and output to a profile file
+Cmd="Mem FromReport"
+Cmd="LogCountedInstances"
+Cmd="obj list -alphasort"
+Cmd="rhi.DumpMemory"
+Cmd="LogOutStatLevels"
+Cmd="ListSpawnedActors"

[MemReportFullCommands]
; Additional commands to run with memreport -full
+Cmd="DumpParticleMem"
+Cmd="ConfigMem"
+Cmd="r.DumpRenderTargetPoolMemory"
+Cmd="ListTextures"
+Cmd="ListSounds -alphasort"
+Cmd="ListParticleSystems -alphasort"
+Cmd="obj list class=SoundWave -alphasort"
+Cmd="obj list class=SkeletalMesh -alphasort"
+Cmd="obj list class=StaticMesh -alphasort"
+Cmd="obj list class=Level -alphasort"
+Cmd="obj list class=StaticMeshComponent -alphasort"

[MemoryPools]
FLightPrimitiveInteractionInitialBlockSize=512

[ProcessLimits]
VirtualMemoryLimitInKB=0

[SystemSettings]
; Most console variables can be set in this section of DefaultEngine.ini to override their default values
; NOTE THAT ANY ITEMS IN THIS SECTION WILL AFFECT ALL PLATFORMS!!!

; the following 4 lines verify the console variable system behavior with the ECVF_Cheat flag
con.DebugEarlyDefault = True
con.DebugEarlyCheat = True
con.DebugLateDefault = True
con.DebugLateCheat = True

r.DetectAndWarnOfBadDrivers = 0

[SystemSettingsEditor]
; System settings overrides for the editor.  Ideally the editor should use the same settings as the game.

; Do not vsync in the editor. Throws off gpu profiling.
r.VSync=0

; Parallel rendering has not been tested in the editor so just disable it.
r.RHICmdBypass=0

[SystemSettingsSplitScreen2]
; System settings overrides for split screen
; Use medium detail mode in splitscreen, this allows LD's to mark meshes as High detail and they won't render in SS
; Removed for now because this value is getting applied all the time, even in non-splitscreen. Splitscreen generally needs
; work anyway, so this won't cause any issues.
;r.DetailMode=1

[OnlineSubsystem]
bHasVoiceEnabled=true
; Internal
VoiceNotificationDelta=0.33
; Steam
;VoiceNotificationDelta=0.2
MaxLocalTalkers=1
MaxRemoteTalkers=16
PollingIntervalInMs=50
bUseBuildIdOverride=false
BuildIdOverride=0

[OnlineSubsystemSteam]
bEnabled=false
SteamDevAppId=0
GameServerQueryPort=27015
bInitServerOnClient=false
bRelaunchInSteam=false
GameVersion=*******
bVACEnabled=1
bAllowP2PPacketRelay=true
P2PConnectionTimeout=90

[/Script/OnlineSubsystemSteam.SteamNetDriver]
NetConnectionClassName="/Script/OnlineSubsystemSteam.SteamNetConnection"

[/Script/SteamSockets.SteamSocketsNetDriver]
NetConnectionClassName="/Script/SteamSockets.SteamSocketsNetConnection"
ConnectionTimeout=80.0
InitialConnectTimeout=120.0
NetServerMaxTickRate=30
MaxNetTickRate=120
KeepAliveTime=0.2
MaxClientRate=100000
MaxInternetClientRate=100000
RelevantTimeout=5.0
SpawnPrioritySeconds=1.0
ServerTravelPause=4.0

[OnlineSubsystemAmazon]
bEnabled=false

[OnlineSubsystemGoogle]
bEnabled=false

[OnlineSubsystemGoogle.OnlineIdentityGoogle]
LoginRedirectUrl="http://127.0.0.1"
+LoginDomains=".google.com"
RedirectPort=9001

[OnlineSubsystemFacebook]
bEnabled=false
APIVer="v2.12"

[OnlineSubsystemFacebook.OnlineIdentityFacebook]
LoginUrl="https://www.facebook.com/`ver/dialog/oauth"
LoginRedirectUrl="https://www.facebook.com/connect/login_success.html"
MeURL="https://graph.facebook.com/`ver/me?access_token=`token"
+LoginDomains=".facebook.com"
bUsePopup=false
ProfileFields=locale
+ProfileFields=link
+ProfileFields=gender

[OnlineSubsystemFacebook.OnlineSharingFacebook]
PermissionsURL="https://graph.facebook.com/`ver/me/permissions?access_token=`token"

[OnlineSubsystemFacebook.OnlineFriendsFacebook]
FriendsUrl="https://graph.facebook.com/`ver/me/friends?fields=`fields&access_token=`token"
FriendsFields=locale
+FriendsFields=link
+FriendsFields=gender

[OnlineSubsystemTwitch]
bEnabled=false
ClientId=""

[OnlineSubsystemTwitch.OnlineIdentityTwitch]
LoginUrl="https://api.twitch.tv/kraken/oauth2/authorize"
bForceVerify=true
LoginRedirectUrl=""
+LoginDomains=".twitch.tv"
TokenValidateUrl="https://api.twitch.tv/kraken"
+ScopeFields=user_read
TokenRevokeUrl="https://api.twitch.tv/kraken/oauth2/revoke"

[OnlineSubsystemSamsung]
bEnabled=true

[OnlineSubsystemSamsung.OnlinePurchaseSamsung]
QueryReceiptsResumeFailDelaySeconds=2.0
CheckoutResumeFailDelaySeconds=2.0
bIncludeSamsungLocText=true

[OnlineSubsystemApple]
bEnabled=false

[OnlineSubsystemNull]
bEnabled=true
Achievement_0_Id=null-ach-0
Achievement_0_bIsHidden=false
Achievement_0_Title="Achievement 0"
Achievement_0_LockedDesc="Achieve achievement 0"
Achievement_0_UnlockedDesc="Achievement 0 achieved"

Achievement_1_Id=null-ach-1
Achievement_1_bIsHidden=false
Achievement_1_Title="Achievement 1"
Achievement_1_LockedDesc="Achieve achievement 1"
Achievement_1_UnlockedDesc="Achievement 1 achieved"

Achievement_2_Id=null-ach-2
Achievement_2_bIsHidden=false
Achievement_2_Title="Achievement 2"
Achievement_2_LockedDesc="Achieve achievement 2"
Achievement_2_UnlockedDesc="Achievement 2 achieved"

Achievement_3_Id=null-ach-3
Achievement_3_bIsHidden=false
Achievement_3_Title="Achievement 3"
Achievement_3_LockedDesc="Achieve achievement 3"
Achievement_3_UnlockedDesc="Achievement 3 achieved"

Achievement_4_Id=null-ach-4
Achievement_4_bIsHidden=false
Achievement_4_Title="Achievement 4"
Achievement_4_LockedDesc="Achieve achievement 4"
Achievement_4_UnlockedDesc="Achievement 4 achieved"

Achievement_5_Id=null-ach-5
Achievement_5_bIsHidden=false
Achievement_5_Title="Achievement 5"
Achievement_5_LockedDesc="Achieve achievement 5"
Achievement_5_UnlockedDesc="Achievement 5 achieved"

Achievement_6_Id=null-ach-6
Achievement_6_bIsHidden=false
Achievement_6_Title="Achievement 6"
Achievement_6_LockedDesc="Achieve achievement 6"
Achievement_6_UnlockedDesc="Achievement 6 achieved"

Achievement_7_Id=null-ach-7
Achievement_7_bIsHidden=false
Achievement_7_Title="Achievement 7"
Achievement_7_LockedDesc="Achieve achievement 7"
Achievement_7_UnlockedDesc="Achievement 7 achieved"

Achievement_8_Id=null-ach-8
Achievement_8_bIsHidden=false
Achievement_8_Title="Achievement 8"
Achievement_8_LockedDesc="Achieve achievement 8"
Achievement_8_UnlockedDesc="Achievement 8 achieved"

Achievement_9_Id=null-ach-9
Achievement_9_bIsHidden=false
Achievement_9_Title="Achievement 9"
Achievement_9_LockedDesc="Achieve achievement 9"
Achievement_9_UnlockedDesc="Achievement 9 achieved"

[/Script/OnlineSubsystemUtils.OnlineBeacon]
BeaconConnectionInitialTimeout=5.0
BeaconConnectionTimeout=45.0

[/Script/OnlineSubsystemUtils.OnlineBeaconHost]
ListenPort=15000

[/Script/OnlineSubsystemUtils.PartyBeaconHost]
bLogoutOnSessionTimeout=true
SessionTimeoutSecs=10
TravelSessionTimeoutSecs=45

[/Script/OnlineSubsystemUtils.SpectatorBeaconHost]
bLogoutOnSessionTimeout=true
SessionTimeoutSecs=10
TravelSessionTimeoutSecs=45

[/Script/Lobby.LobbyBeaconClient]
BeaconConnectionInitialTimeout=90.0
BeaconConnectionTimeout=45.0

[StaticMeshLODSettings]
LevelArchitecture=(NumLODs=4,MaxNumStreamedLODs=0,bSupportLODStreaming=0,LightMapResolution=32,LODPercentTriangles=50,PixelError=12,SilhouetteImportance=4,Name=LOCTEXT("LevelArchitectureLOD","Level Architecture"))
SmallProp=(NumLODs=4,MaxNumStreamedLODs=0,bSupportLODStreaming=0,LODPercentTriangles=50,PixelError=10,Name=LOCTEXT("SmallPropLOD","Small Prop"))
LargeProp=(NumLODs=4,MaxNumStreamedLODs=0,bSupportLODStreaming=0,LODPercentTriangles=50,PixelError=10,Name=LOCTEXT("LargePropLOD","Large Prop"))
Deco=(NumLODs=4,MaxNumStreamedLODs=0,bSupportLODStreaming=0,LODPercentTriangles=50,PixelError=10,Name=LOCTEXT("DecoLOD","Deco"))
Vista=(NumLODs=1,MaxNumStreamedLODs=0,bSupportLODStreaming=0,Name=LOCTEXT("VistaLOD","Vista"))
Foliage=(NumLODs=1,MaxNumStreamedLODs=0,bSupportLODStreaming=0,Name=LOCTEXT("FoliageLOD","Foliage"))
HighDetail=(NumLODs=6,MaxNumStreamedLODs=0,bSupportLODStreaming=0,LODPercentTriangles=50,PixelError=6,Name=LOCTEXT("HighDetailLOD","High Detail"))

[TextureTracking]
;TextureName=T_GD_Traffic_Crosswalk_01

; Config for RuntimeAssetCache asset groups.
; Each group can be managed separately (e.g. advertisement cache, character image cache etc.)
; For each group a name must be specified. Size is optional and defaults to 5MB. Size in
; ini file is stored as number of bytes. E.g.:
; [RuntimeAssetCache]
; +BucketConfigs=(Name="CharacterImage", Size=3000000)
; +BucketConfigs=(Name="Advertisement", Size=4000000)
[RuntimeAssetCache]
+BucketConfigs=(Name="DefaultBucket", Size=10000000)
PathToRAC=RuntimeAssetCache

; Derived Data backend graphs
; Each of the below backend graph sections contains a set of nodes used to create derived data backed graph.
; DerivedDataBackendGraph is the default graph for source builds, and InstalledDerivedDataBackendGraph is the default for installed builds.
; Others can be specified othe command line using:
;     -DDC=GraphSectionName     (for example: -DDC=VerifyDerivedDataBackendGraph)
; Each graph should start with 'Root' node. Names of all the other nodes are not predefined.
; Supported node types are: KeyLength, AsyncPut, Hierarchical, Boot, Filesystem, ReadPak, WritePak, Verify
; The order nodes are define in is not relevant
; Filesystem backends can be disabled by setting the corresponding environment variable to 'None'. E.g UE-SharedDataCachePath=None


[DerivedDataBackendGraph_Fill_Seattle]
MinimumDaysToKeepFile=7
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=Boot, Inner=Pak, Inner=EnginePak, Inner=Local, Inner=Seattle)
Boot=(Type=Boot, Filename="%GAMEDIR%DerivedDataCache/Boot.ddc", MaxCacheSize=512)
Local=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, PurgeTransient=true, DeleteUnused=true, UnusedFileAge=34, FoldersToClean=-1, Path=%ENGINEDIR%DerivedDataCache)
Seattle=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, DeleteUnused=true, UnusedFileAge=23, FoldersToClean=10, MaxFileChecksPerSec=1, Path=?EpicSeaDDC, EnvPathOverride=UE-SharedDataCachePath_Seattle)
Pak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
EnginePak=(Type=ReadPak, Filename=%ENGINEDIR%DerivedDataCache/DDC.ddp)

[NoShared]
MinimumDaysToKeepFile=7
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=Boot, Inner=Pak, Inner=Local)
Boot=(Type=Boot, Filename="%GAMEDIR%DerivedDataCache/Boot.ddc", MaxCacheSize=512)
Local=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, PurgeTransient=true, DeleteUnused=true, UnusedFileAge=34, FoldersToClean=-1, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache)
Pak=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")

[CreatePak]
MinimumDaysToKeepFile=7
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=Boot, Inner=PakWrite, Inner=PakRead, Inner=Local, Inner=Shared)
Boot=(Type=Boot, Filename="%GAMEDIR%DerivedDataCache/Boot.ddc", MaxCacheSize=512)
Local=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, PurgeTransient=true, DeleteUnused=true, UnusedFileAge=34, FoldersToClean=-1, Path=%ENGINEDIR%DerivedDataCache)
Shared=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, DeleteUnused=false, UnusedFileAge=23, FoldersToClean=-1, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath)
AltShared=(Type=FileSystem, ReadOnly=true, Clean=false, Flush=false, DeleteUnused=false, UnusedFileAge=23, FoldersToClean=-1, Path=?EpicDDC2, EnvPathOverride=UE-SharedDataCachePath2)
PakRead=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")
PakWrite=(Type=WritePak, Filename="%GAMEDIR%DerivedDataCache/DDC.ddp")

[CreateInstalledProjectPak]
MinimumDaysToKeepFile=7
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=Boot, Inner=EnginePak, Inner=PakWrite, Inner=PakRead, Inner=Local, Inner=Shared)
Boot=(Type=Boot, Filename="%GAMEDIR%DerivedDataCache/Boot.ddc", MaxCacheSize=512)
Local=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, PurgeTransient=true, DeleteUnused=true, UnusedFileAge=34, FoldersToClean=-1, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath)
Shared=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, DeleteUnused=false, UnusedFileAge=23, FoldersToClean=-1, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath, CommandLineOverride=SharedDataCachePath)
AltShared=(Type=FileSystem, ReadOnly=true, Clean=false, Flush=false, DeleteUnused=false, UnusedFileAge=23, FoldersToClean=-1, Path=?EpicDDC2, EnvPathOverride=UE-SharedDataCachePath2)
EnginePak=(Type=ReadPak, Filename=../../../Engine/DerivedDataCache/Compressed.ddp, Compressed=true)
PakRead=(Type=ReadPak, Filename="%GAMEDIR%DerivedDataCache/Compressed.ddp", Compressed=true)
PakWrite=(Type=WritePak, Filename="%GAMEDIR%DerivedDataCache/Compressed.ddp", Compressed=true)

[CreateInstalledEnginePak]
MinimumDaysToKeepFile=7
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=Boot, Inner=PakWrite, Inner=Local, Inner=Shared)
Boot=(Type=Boot, Filename="%GAMEDIR%DerivedDataCache/Boot.ddc", MaxCacheSize=512)
Local=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, PurgeTransient=true, DeleteUnused=true, UnusedFileAge=34, FoldersToClean=-1, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath)
Shared=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, DeleteUnused=false, UnusedFileAge=23, FoldersToClean=-1, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath)
AltShared=(Type=FileSystem, ReadOnly=true, Clean=false, Flush=false, DeleteUnused=false, UnusedFileAge=23, FoldersToClean=-1, Path=?EpicDDC2, EnvPathOverride=UE-SharedDataCachePath2)
PakWrite=(Type=WritePak, Filename=%ENGINEDIR%DerivedDataCache/Compressed.ddp, Compressed=true)

[CreateInstalledEnterprisePak]
MinimumDaysToKeepFile=7
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=Boot, Inner=PakWrite, Inner=Local, Inner=Shared)
Boot=(Type=Boot, Filename="%GAMEDIR%DerivedDataCache/Boot.ddc", MaxCacheSize=512)
Local=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, PurgeTransient=true, DeleteUnused=true, UnusedFileAge=34, FoldersToClean=-1, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath)
Shared=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, DeleteUnused=false, UnusedFileAge=23, FoldersToClean=-1, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath)
AltShared=(Type=FileSystem, ReadOnly=true, Clean=false, Flush=false, DeleteUnused=false, UnusedFileAge=23, FoldersToClean=-1, Path=?EpicDDC2, EnvPathOverride=UE-SharedDataCachePath2)
PakWrite=(Type=WritePak, Filename=../../../Enterprise/DerivedDataCache/Compressed.ddp, Compressed=true)

[CreateProjectCache]
MinimumDaysToKeepFile=7
Root=(Type=KeyLength, Length=120, Inner=AsyncPut)
AsyncPut=(Type=AsyncPut, Inner=Hierarchy)
Hierarchy=(Type=Hierarchical, Inner=Local, Inner=Project, Inner=Shared)
Local=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, PurgeTransient=true, DeleteUnused=true, UnusedFileAge=34, FoldersToClean=-1, Path=%ENGINEDIR%DerivedDataCache, EnvPathOverride=UE-LocalDataCachePath)
Project=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=true, PurgeTransient=true, DeleteUnused=true, UnusedFileAge=34, FoldersToClean=-1, Path=%GAMEDIR%ProjectDerivedData)
Shared=(Type=FileSystem, ReadOnly=false, Clean=false, Flush=false, DeleteUnused=false, UnusedFileAge=23, FoldersToClean=-1, Path=?EpicDDC, EnvPathOverride=UE-SharedDataCachePath)
AltShared=(Type=FileSystem, ReadOnly=true, Clean=false, Flush=false, DeleteUnused=false, UnusedFileAge=23, FoldersToClean=-1, Path=?EpicDDC2, EnvPathOverride=UE-SharedDataCachePath2)

[VirtualTextureChunkDDCCache]
UnusedFileAge=34
MaxFileChecksPerSec=-1 ;no limit
Path=%GAMEDIR%DerivedDataCache/VT

[DDCCleanup]
TimeToWaitAfterInit=120
TimeBetweenDeleteingDirectories=5
TimeBetweenDeletingFiles=2

[/Script/Engine.LocalPlayer]
AspectRatioAxisConstraint=AspectRatio_MaintainXFOV

[ContentComparisonReferenceTypes]
+Class=AnimSet
+Class=SkeletalMesh
+Class=SoundCue
+Class=StaticMesh
+Class=ParticleSystem
+Class=Texture2D

[AssetRegistry]
; Default Path Filters not scanned by the asset registry when searching for all assets
; These are long package paths
;+BlacklistPackagePathScanFilters="/Game/__ExternalActors__"
; Default sub content paths that do not get scanned on each mount by the asset registry when searching for all assets
+BlacklistContentSubPathScanFilters="__ExternalActors__"
; Fill out this list with asset registry tags that are not needed in cooked builds and will be stripped during cook.
; You can use a * wildcard to refer to either all classes or all tags for the class.
; If your game does not need any asset registry tags, simply add (Class=*,Tag=*). Be warned that this this may make some engine systems fail to work since they might need ParentClass or GeneratedClass on Blueprints.
+CookedTagsBlacklist=(Class=Blueprint,Tag=FiB)
+CookedTagsBlacklist=(Class=Blueprint,Tag=FiBData)
+CookedTagsBlacklist=(Class=*,Tag=AssetImportData)

; Alternatively, you can use a whitelist which will exclude all tags except those mentioned in this list. Set bUseAssetRegistryTagsWhitelistInsteadOfBlacklist to true to use this list.
bUseAssetRegistryTagsWhitelistInsteadOfBlacklist=false
+CookedTagsWhitelist=(Class=Blueprint,Tag=ParentClass)
+CookedTagsWhitelist=(Class=Blueprint,Tag=GeneratedClass)
+CookedTagsWhitelist=(Class=Blueprint,Tag=GameplayCueName)
+CookedTagsWhitelist=(Class=*,Tag=AssetBundleData)
+CookedTagsWhitelist=(Class=*,Tag=PrimaryAssetType)
+CookedTagsWhitelist=(Class=*,Tag=PrimaryAssetName)
+CookedTagsWhitelist=(Class=World,Tag=Tests)
+CookedTagsWhitelist=(Class=World,Tag=TestNames)

; These are use with USE_COMPACT_ASSET_REGISTRY to further deduplicate things with FName
+CookedTagsAsFName=PrimaryAssetType
+CookedTagsAsFName=PrimaryAssetName
+CookedTagsAsPathName=GeneratedClass
+CookedTagsAsPathName=ParentClass
+CookedTagsAsLocText=DisplayName

; These are the options for what parts of the asset registry are cooked and read at runtime. These can be overridden per platform in the appropriate PlatformEngine.ini file
bSerializeAssetRegistry=true
bSerializeDependencies=false
bSerializeSearchableNameDependencies=false
bSerializeManageDependencies=false
bSerializePackageData=false
bFilterAssetDataWithNoTags=false
bFilterDependenciesWithNoTags=false
bFilterSearchableNames=true

[AutomationTesting]
ImportTestPath=../../Content/EditorAutomation/
ImportTestPackagePath=/Engine/Content/EditorAutomation
bForceSmokeTests=false

[AutomationTesting.FbxImport]
FbxImportTestPath=../../Content/FbxEditorAutomation/
FbxImportTestPackagePath=/Engine/FbxEditorAutomationOut

[AutomationTesting.Blueprint]
TestAllBlueprints=false
+InstanceTestMaps=../../../Engine/Content/Maps/Automation/BlueprintInstanceTest.umap
+ReparentTest.ChildrenPackagePaths=/Game/ReparentingTestAssets/Children
+ReparentTest.ParentsPackagePaths=/Game/ReparentingTestAssets/Parents

[/Script/Engine.AutomationTestSettings]
+EditorTestModules=StaticMeshEditor
+EditorTestModules=LandscapeEditor
+EditorTestModules=GameProjectGeneration
+EditorTestModules=Cascade
+TestLevelFolders=TestMaps
MaterialEditorPromotionTest=(DefaultMaterialAsset=(FilePath="../../Content/EditorMeshes/ColorCalibrator/M_ColorGrid.uasset"),DefaultDiffuseTexture=(FilePath="../../Content/EngineMaterials/DefaultDiffuse.uasset"),DefaultNormalTexture=(FilePath="../../Content/EngineMaterials/DefaultNormal.uasset"))
ParticleEditorPromotionTest=(DefaultParticleAsset=(FilePath="../../Content/Tutorial/SubEditors/TutorialAssets/TutorialParticleSystem.uasset"))

[AutomationTesting.StaticMeshEditorTest]
+EditorViewButtons=Wireframe
+EditorViewButtons=Verts
+EditorViewButtons=Grid
+EditorViewButtons=Bounds
+EditorViewButtons=Collision
+EditorViewButtons=Show Pivot
+EditorViewButtons=Normals
+EditorViewButtons=Tangents
+EditorViewButtons=Binormals
+EditorViewButtons=UV
EditorViewButtonsObject=EditorCylinder

[/Script/NavigationSystem.NavigationSystemV1]
bAutoCreateNavigationData=true
bAddPlayersToGenerationSeeds=true

[/Script/NavigationSystem.NavigationData]
RuntimeGeneration=Static

[/Script/NavigationSystem.RecastNavMesh]
; runtime params
TileSetUpdateInterval=1.0
MaxTileGridWidth=256
MaxTileGridHeight=256
DefaultDrawDistance=5000.0
; navmesh generation parameters
TileSizeUU=1000.f
CellSize=19.f
CellHeight=10.f
AgentRadius=34.f
AgentHeight=144.f
AgentMaxHeight=160.f
AgentMaxStepHeight=35.f
AgentMaxSlope=44.f
MinRegionArea=0.f
; default should be aproximately 20*CellSize
MergeRegionSize=400.f
bUseBetterOffsetsFromCorners=true

[/Script/NavigationSystem.NavArea_Null]
DrawColor=(R=38,G=38,B=38,A=64)

[/Script/NavigationSystem.NavArea_Default]
DrawColor=(R=140,G=255,B=0,A=164)

[Trace.ChannelPresets]
Default=cpu,frame,log,bookmark
Rendering=gpu,cpu,frame,log,bookmark

[RemoteConfiguration]
Enabled=false
ConfigPathPrefix=\\epicgames.net\root\Home
ConfigPathSuffix=UE4Cloud
Timeout=1.0f
+IniToLoad=EditorPerProjectUserSettings
+IniToLoad=EditorKeyBindings

[Engine.ErrorHandling]
bPromptForRemoteDebugging=false
bPromptForRemoteDebugOnEnsure=false

[Niagara]
EnableNiagara=false

[/Script/Engine.Actor]
; option used when UpdateOverlapsMethodDuringLevelStreaming is set to UseConfigDefault. Options are: AlwaysUpdate, OnlyUpdateMovable, NeverUpdate.
DefaultUpdateOverlapsMethodDuringLevelStreaming=OnlyUpdateMovable

[/Script/Engine.TriggerVolume]
DefaultUpdateOverlapsMethodDuringLevelStreaming=AlwaysUpdate

[/Script/Engine.CollisionProfile]
+Profiles=(Name="NoCollision",CollisionEnabled=NoCollision,ObjectTypeName="WorldStatic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore)),HelpMessage="No collision",bCanModify=False)
+Profiles=(Name="BlockAll",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldStatic",CustomResponses=,HelpMessage="WorldStatic object that blocks all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
+Profiles=(Name="OverlapAll",CollisionEnabled=QueryOnly,ObjectTypeName="WorldStatic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Overlap),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldStatic object that overlaps all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
+Profiles=(Name="BlockAllDynamic",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldDynamic",CustomResponses=,HelpMessage="WorldDynamic object that blocks all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
+Profiles=(Name="OverlapAllDynamic",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Overlap),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldDynamic object that overlaps all actors by default. All new custom channels will use its own default response. ",bCanModify=False)
+Profiles=(Name="IgnoreOnlyPawn",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that ignores Pawn and Vehicle. All other channels will be set to default.",bCanModify=False)
+Profiles=(Name="OverlapOnlyPawn",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Pawn",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that overlaps Pawn, Camera, and Vehicle. All other channels will be set to default. ",bCanModify=False)
+Profiles=(Name="Pawn",CollisionEnabled=QueryAndPhysics,ObjectTypeName="Pawn",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Pawn object. Can be used for capsule of any playerable character or AI. ",bCanModify=False)
+Profiles=(Name="Spectator",CollisionEnabled=QueryOnly,ObjectTypeName="Pawn",CustomResponses=((Channel="WorldStatic",Response=ECR_Block),(Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Ignore),(Channel="Camera",Response=ECR_Ignore),(Channel="PhysicsBody",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Destructible",Response=ECR_Ignore)),HelpMessage="Pawn object that ignores all other actors except WorldStatic.",bCanModify=False)
+Profiles=(Name="CharacterMesh",CollisionEnabled=QueryOnly,ObjectTypeName="Pawn",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Vehicle",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Pawn object that is used for Character Mesh. All other channels will be set to default.",bCanModify=False)
+Profiles=(Name="PhysicsActor",CollisionEnabled=QueryAndPhysics,ObjectTypeName="PhysicsBody",CustomResponses=,HelpMessage="Simulating actors",bCanModify=False)
+Profiles=(Name="Destructible",CollisionEnabled=QueryAndPhysics,ObjectTypeName="Destructible",CustomResponses=,HelpMessage="Destructible actors",bCanModify=False)
+Profiles=(Name="InvisibleWall",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldStatic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="WorldStatic object that is invisible.",bCanModify=False)
+Profiles=(Name="InvisibleWallDynamic",CollisionEnabled=QueryAndPhysics,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="Visibility",Response=ECR_Ignore)),HelpMessage="WorldDynamic object that is invisible.",bCanModify=False)
+Profiles=(Name="Trigger",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Ignore),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldDynamic object that is used for trigger. All other channels will be set to default.",bCanModify=False)
+Profiles=(Name="Ragdoll",CollisionEnabled=QueryAndPhysics,ObjectTypeName="PhysicsBody",CustomResponses=((Channel="Pawn",Response=ECR_Ignore),(Channel="Visibility",Response=ECR_Ignore)),HelpMessage="Simulating Skeletal Mesh Component. All other channels will be set to default.",bCanModify=False)
+Profiles=(Name="Vehicle",CollisionEnabled=QueryAndPhysics,ObjectTypeName="Vehicle",CustomResponses=,HelpMessage="Vehicle object that blocks Vehicle, WorldStatic, and WorldDynamic. All other channels will be set to default.",bCanModify=False)
+Profiles=(Name="UI",CollisionEnabled=QueryOnly,ObjectTypeName="WorldDynamic",CustomResponses=((Channel="WorldStatic",Response=ECR_Overlap),(Channel="Pawn",Response=ECR_Overlap),(Channel="Visibility",Response=ECR_Block),(Channel="WorldDynamic",Response=ECR_Overlap),(Channel="Camera",Response=ECR_Overlap),(Channel="PhysicsBody",Response=ECR_Overlap),(Channel="Vehicle",Response=ECR_Overlap),(Channel="Destructible",Response=ECR_Overlap)),HelpMessage="WorldStatic object that overlaps all actors by default. All new custom channels will use its own default response. ",bCanModify=False)

+OldProfiles=(Name="BlockingVolume",CollisionEnabled=QueryAndPhysics,ObjectTypeName=WorldStatic,CustomResponses=((Channel=Visibility, Response=ECR_Ignore)))
+OldProfiles=(Name="InterpActor",CollisionEnabled=QueryOnly,ObjectTypeName=WorldStatic,CustomResponses=((Channel=Pawn, Response=ECR_Ignore)))
+OldProfiles=(Name="StaticMeshComponent",CollisionEnabled=QueryAndPhysics,ObjectTypeName=WorldStatic)
+OldProfiles=(Name="SkeletalMeshActor",CollisionEnabled=QueryAndPhysics,ObjectTypeName=PhysicsBody,CustomResponses=((Channel=Visibility, Response=ECR_Block)))
+OldProfiles=(Name="InvisibleActor", CollisionEnabled=QueryAndPhysics, ObjectTypeName=WorldDynamic, CustomResponses=((Channel=Visibility, Response=ECR_Ignore)))

+ProfileRedirects=(OldName="BlockingVolume",NewName="InvisibleWall")
+ProfileRedirects=(OldName="InterpActor",NewName="IgnoreOnlyPawn")
+ProfileRedirects=(OldName="StaticMeshComponent",NewName="BlockAllDynamic")
+ProfileRedirects=(OldName="SkeletalMeshActor",NewName="PhysicsActor")
+ProfileRedirects=(OldName="InvisibleActor",NewName="InvisibleWallDynamic")

+CollisionChannelRedirects=(OldName="Static",NewName="WorldStatic")
+CollisionChannelRedirects=(OldName="Dynamic",NewName="WorldDynamic")
+CollisionChannelRedirects=(OldName="VehicleMovement",NewName="Vehicle")
+CollisionChannelRedirects=(OldName="PawnMovement",NewName="Pawn")

[Engine.BufferVisualizationMaterials]
BaseColor=(Material="/Engine/BufferVisualization/BaseColor.BaseColor", Name=LOCTEXT("BaseColorMat", "Base Color"))
CustomDepth=(Material="/Engine/BufferVisualization/CustomDepth.CustomDepth", Name=LOCTEXT("BaseCustomDepthMat", "Custom Depth"))
CustomStencil=(Material="/Engine/BufferVisualization/CustomStencil.CustomStencil", Name=LOCTEXT("BaseCustomStencilMat", "Custom Stencil"))
FinalImage=(Material="/Engine/BufferVisualization/FinalImage.FinalImage", Name=LOCTEXT("BaseFinalImageMat", "Final Image"))
ShadingModel=(Material="/Engine/BufferVisualization/LightingModel.LightingModel", Name=LOCTEXT("BaseShadingModelMat", "Shading Model"))
MaterialAO=(Material="/Engine/BufferVisualization/MaterialAO.MaterialAO", Name=LOCTEXT("BaseMaterialAOMat", "Material Ambient Occlusion"))
Metallic=(Material="/Engine/BufferVisualization/Metallic.Metallic", Name=LOCTEXT("BaseMetallicMat", "Metallic"))
Opacity=(Material="/Engine/BufferVisualization/Opacity.Opacity", Name=LOCTEXT("BaseOpacityMat", "Opacity"))
Roughness=(Material="/Engine/BufferVisualization/Roughness.Roughness", Name=LOCTEXT("BaseRoughnessMat", "Roughness"))
Anisotropy=(Material="/Engine/BufferVisualization/Anisotropy.Anisotropy", Name=LOCTEXT("BaseAnisotropyMat", "Anisotropy"))
SceneColor=(Material="/Engine/BufferVisualization/SceneColor.SceneColor", Name=LOCTEXT("BaseSceneColorMat", "Scene Color"))
SceneDepth=(Material="/Engine/BufferVisualization/SceneDepth.SceneDepth", Name=LOCTEXT("BaseSceneDepthMat", "Scene Depth"))
SeparateTranslucencyRGB=(Material="/Engine/BufferVisualization/SeparateTranslucencyRGB.SeparateTranslucencyRGB", Name=LOCTEXT("BaseSeparateTranslucencyRGBMat", "Separate Translucency RGB"))
SeparateTranslucencyA=(Material="/Engine/BufferVisualization/SeparateTranslucencyA.SeparateTranslucencyA", Name=LOCTEXT("BaseSeparateTranslucencyAMat", "Separate Translucency Alpha"))
Specular=(Material="/Engine/BufferVisualization/Specular.Specular", Name=LOCTEXT("BaseSpecularMat", "Specular"))
SubsurfaceColor=(Material="/Engine/BufferVisualization/SubsurfaceColor.SubsurfaceColor", Name=LOCTEXT("BaseSubsurfaceColorMat", "Subsurface Color"))
WorldNormal=(Material="/Engine/BufferVisualization/WorldNormal.WorldNormal", Name=LOCTEXT("BaseWorldNormalMat", "World Normal"))
WorldTangent=(Material="/Engine/BufferVisualization/WorldTangent.WorldTangent", Name=LOCTEXT("BaseWorldTangentMat", "World Tangent"))
AmbientOcclusion=(Material="/Engine/BufferVisualization/AmbientOcclusion.AmbientOcclusion", Name=LOCTEXT("BaseAmbientOcclusionMat", "Ambient Occlusion"))
CustomDepthWorldUnits=(Material="/Engine/BufferVisualization/CustomDepthWorldUnits.CustomDepthWorldUnits", Name=LOCTEXT("BaseCustomDepthWorldUnitsMat", "Custom Depth World Units"))
SceneDepthWorldUnits=(Material="/Engine/BufferVisualization/SceneDepthWorldUnits.SceneDepthWorldUnits", Name=LOCTEXT("BaseSceneDepthWorldUnitsMat", "Scene Depth World Units"))
Velocity=(Material="/Engine/BufferVisualization/Velocity.Velocity", Name=LOCTEXT("Velocity", "Velocity"))
PreTonemapHDRColor=(Material="/Engine/BufferVisualization/PreTonemapHDRColor.PreTonemapHDRColor", Name=LOCTEXT("PreTonemapHDRColor", "Pre Tonemap HDR Color"))
PostTonemapHDRColor=(Material="/Engine/BufferVisualization/PostTonemapHDRColor.PostTonemapHDRColor", Name=LOCTEXT("PostTonemapHDRColor", "Post Tonemap HDR Color"))

[DeviceProfileManager]
DeviceProfileSelectionModule="ExampleDeviceProfileSelector"

[SlateRenderer]
TextureAtlasSize=1024
GrayscaleFontAtlasSize=1024
ColorFontAtlasSize=512
; This is the number of preallocated vertices to create for use in Slate/UMG UI. A smaller number would indicate more resizes of the vertex buffer which could cause hitches.  A larger number increases memory
; Use stat slatememory to see the cost of these buffers
NumPreallocatedVertices=50000

[MobileSlateUI]
bTouchFallbackToMouse=true

[Pak]
+ExtensionsToNotUsePluginCompression=uplugin
+ExtensionsToNotUsePluginCompression=upluginmanifest
+ExtensionsToNotUsePluginCompression=uproject
+ExtensionsToNotUsePluginCompression=ini
+ExtensionsToNotUsePluginCompression=icu
+ExtensionsToNotUsePluginCompression=res
+DirectoryIndexKeepFiles="*/Config/Tags/*"
+DirectoryIndexKeepFiles="*/Content/Localization/*"
+DirectoryIndexKeepFiles="*/Content/Internationalization/*"
+DirectoryIndexKeepFiles="*/Content/Movies/*"
+DirectoryIndexKeepFiles="*/Plugins/*.uplugin*"
+DirectoryIndexKeepFiles="*/*.ini*"
+DirectoryIndexKeepEmptyDirectories=*/Plugins/*
+IndexValidationIgnore="*/Saved/Logs/*"

[/Script/GameplayDebugger.GameplayDebuggingReplicator]
MaxEQSQueries=5
DebugComponentClassName="/Script/GameplayDebugger.GameplayDebuggingComponent"
DebugComponentHUDClassName="/Script/GameplayDebugger.GameplayDebuggingHUDComponent"
DebugComponentControllerClassName = "/Script/GameplayDebugger.GameplayDebuggingControllerComponent"

[/Script/GameplayDebugger.GameplayDebuggingHUDComponent]
MenuStartX=10.0
MenuStartY=10.0
DebugInfoStartX=20.0
DebugInfoStartY=60.0

; These are the defaults for iOS settings, and they need to be in the .ini since UBT reads the .ini settings, without instantiating the class
; Note: These should be in IOSEngine, but are placed here because the Settings editor UI cannot write to the Game IOSEngine, meaning settings in Engine/IOSEngine are non-overridable
[/Script/IOSRuntimeSettings.IOSRuntimeSettings]
bEnableGameCenterSupport=False
bSupportsPortraitOrientation=False
bSupportsITunesFileSharing=False
bSupportsUpsideDownOrientation=False
bSupportsLandscapeLeftOrientation=True
bSupportsLandscapeRightOrientation=True
PreferredLandscapeOrientation=LandscapeLeft
bSupportsMetal=True
bCookPVRTCTextures=True
bCookASTCTextures=False
bSupportsMetalMRT=False
bShipForBitcode=True
bGameSupportsMultipleActiveControllers=False
bAllowRemoteRotation=True
bUseRemoteAsVirtualJoystick=True
bUseAbsoluteDpadValues=False
bAllowControllers=True
bBuildAsFramework=False
bGenerateFrameworkWrapperProject=True
bGeneratedSYMFile=False
bDisableHTTPS=false
bUseRSync=True
BundleDisplayName=[PROJECT_NAME]
BundleName=[PROJECT_NAME]
BundleIdentifier=com.YourCompany.[PROJECT_NAME]
VersionInfo=1.0
FrameRateLock=PUFRL_30
bEnableDynamicMaxFPS=False
MinimumiOSVersion=IOS_12
bSupportsIPad=True
bSupportsIPhone=True
AdditionalPlistData=
RemoteServerName=
RSyncUsername=
SSHPrivateKeyOverridePath=
bEnableRemoteNotificationsSupport=False
bEnableCloudKitSupport=False
bRunAsCurrentUser=False
IOSCloudKitSyncStrategy=None
bGenerateCrashReportSymbols=false
bAutomaticSigning=false
UseFastIntrinsics=False
ForceFloats=False
EnableMathOptimisations=True
MaxShaderLanguageVersion=3
bDisableMotionData=False
bEnableAdvertisingIdentifier=True

; These are the defaults for Android settings, and they need to be in the .ini since UBT reads the .ini settings, without instantiating the class
[/Script/AndroidRuntimeSettings.AndroidRuntimeSettings]
SDKAPILevelOverride=
NDKAPILevelOverride=
bEnableGooglePlaySupport=false
bSupportAdMob=true
bBuildForArmV7=true
bBuildForArm64=false
bBuildForX86=false
bBuildForX8664=false
bBuildForES31=true
bSupportsVulkan=false
bSupportsVulkanSM5=false
bDetectVulkanByDefault=true
bSplitIntoSeparateApks=false
bPackageDataInsideApk=false
bUseExternalFilesDir=false
bPublicLogFiles=true
bCreateAllPlatformsInstall=false
Orientation=SensorLandscape
InstallLocation=InternalOnly
DepthBufferPreference=Default
PackageName=com.YourCompany.[PROJECT]
StoreVersion=1
StoreVersionOffsetArmV7=0
StoreVersionOffsetArm64=0
StoreVersionOffsetX86=0
StoreVersionOffsetX8664=0
VersionDisplayName=1.0
MinSDKVersion=19
TargetSDKVersion=28
bEnableGradle=true
bEnableLint=false
bShowLaunchImage=true
bValidateTextureFormats=true
bMultiTargetFormat_ETC2=true
bMultiTargetFormat_DXT=true
bMultiTargetFormat_ASTC=true
TextureFormatPriority_ETC2=0.2
TextureFormatPriority_DXT=0.6
TextureFormatPriority_ASTC=0.9
bEnableNewKeyboard=True
bAndroidVoiceEnabled=false
bBuildWithHiddenSymbolVisibility=false
bSaveSymbols=false
bAllowControllers=True
bAllowIMU=True
bUseDisplayCutout=False
bEnableSnapshots=False
bRestoreNotificationsOnReboot=False
bEnableBundle=False
bEnableUniversalAPK=True
bBundleABISplit=True
bBundleLanguageSplit=True
bBundleDensitySplit=True
bFullScreen=True
bForceLDLinker=False
bForceSmallOBBFiles=False
bAllowLargeOBBFiles=False
bAllowPatchOBBFile=False
bAllowOverflowOBBFiles=False
bExtractNativeLibs=true

[/Script/AndroidPlatformEditor.AndroidSDKSettings]
SDKAPILevel=latest
NDKAPILevel=android-21

[/Script/MagicLeap.MagicLeapSettings]
bEnableZI=False
bUseVulkanForZI=True
bUseMLAudioForZI=True

[/Script/LuminRuntimeSettings.LuminRuntimeSettings]
PackageName=com.YourCompany.[PROJECT]
ApplicationDisplayName=
bIsScreensApp=false
FrameTimingHint=FPS_60
bProtectedContent=False
bManualCallToAppReady=False
bUseMobileRendering=true
Certificate=(FilePath="")
IconModelPath=(Path="Build/Lumin/Resources/Model")
IconPortalPath=(Path="Build/Lumin/Resources/Portal")
bUseVulkan=true
VersionCode=1
MinimumAPILevel=7
ControllerTrackingType=Tracked
ControllerTrackingMode=CoordinateFrameUID
+AppPrivileges=LowLatencyLightwear
+AppPrivileges=Internet
+AppPrivileges=LocalAreaNetwork
+AppPrivileges=GesturesSubscribe
+AppPrivileges=GesturesConfig
+AppPrivileges=CameraCapture
+AppPrivileges=WorldReconstruction
+AppPrivileges=PcfRead
+AppPrivileges=AudioCaptureMic
+AppPrivileges=IdentityRead
+AppPrivileges=VoiceInput
+AppPrivileges=ControllerPose
bRemoveDebugInfo=true
VulkanValidationLayerLibs=(Path="")

[/Script/UnrealEd.CookerSettings]
DefaultPVRTCQuality=1
DefaultASTCQualityBySpeed=1
DefaultASTCQualityBySize=3
+ClassesExcludedOnDedicatedServer=WidgetBlueprint
+ClassesExcludedOnDedicatedServer=GroupActor
+ClassesExcludedOnDedicatedServer=MetaData
+ClassesExcludedOnDedicatedServer=ObjectRedirector
+ClassesExcludedOnDedicatedServer=NavMeshRenderingComponent
+ClassesExcludedOnDedicatedServer=ReflectionCaptureComponent
+ClassesExcludedOnDedicatedServer=TextRenderComponent
+ClassesExcludedOnDedicatedServer=Font
+ClassesExcludedOnDedicatedServer=InterpCurveEdSetup
+ClassesExcludedOnDedicatedServer=MaterialExpression
+ClassesExcludedOnDedicatedServer=MatineeActorCameraAnim
+ClassesExcludedOnDedicatedServer=NiagaraEmitter
+ClassesExcludedOnDedicatedServer=NiagaraScript
+ClassesExcludedOnDedicatedServer=ParticleEmitter
+ClassesExcludedOnDedicatedServer=ParticleLODLevel
+ClassesExcludedOnDedicatedServer=ParticleModule
+ClassesExcludedOnDedicatedServer=SubUVAnimation
+ClassesExcludedOnDedicatedServer=SoundNode
+ClassesExcludedOnDedicatedServer=GameplayEffectUIData
+ClassesExcludedOnDedicatedClient=WidgetBlueprint
+ClassesExcludedOnDedicatedClient=GroupActor
+ClassesExcludedOnDedicatedClient=MetaData
+ClassesExcludedOnDedicatedClient=ObjectRedirector
+ClassesExcludedOnDedicatedClient=InterpCurveEdSetup
+ClassesExcludedOnDedicatedClient=MatineeActorCameraAnim

+VersionedIntRValues=r.AllowStaticLighting
+VersionedIntRValues=r.GBuffer
+VersionedIntRValues=r.BasePassOutputsVelocity
+VersionedIntRValues=r.SelectiveBasePassOutputs
+VersionedIntRValues=r.DBuffer
+VersionedIntRValues=r.Shaders.KeepDebugInfo
+VersionedIntRValues=r.Shaders.Optimize
+VersionedIntRValues=r.CompileShadersForDevelopment
+VersionedIntRValues=r.MobileHDR
+VersionedIntRValues=r.UsePreExposure

[/Script/UnrealEd.ChunkDependencyInfo]
; Entries can be added to this section of DefaultEngine.ini to set dependencies for package chunks, used by cooking and packaging
; +DependencyArray=(ChunkID=10,ParentChunkID=1)

[/Script/Engine.PhysicsSettings]
DefaultGravityZ=-980.0
bEnable2DPhysics=false

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
+TargetedRHIs=PCD3D_SM5
MinimumOSVersion=MSOS_Vista
bEnableRayTracing=true
bTarget32Bit=false

[/Script/LinuxTargetPlatform.LinuxTargetSettings]
+TargetedRHIs=SF_VULKAN_SM5

[/Script/MacTargetPlatform.MacTargetSettings]
MaxShaderLanguageVersion=4
+TargetedRHIs=SF_METAL_SM5
UseFastIntrinsics=False
ForceFloats=False
EnableMathOptimisations=True

[HMDPluginPriority]
; Since SteamVR also works with the Oculus Rift and Windows Mixed Reality, give priority to the native Oculus and Windows Mixed Reality plugins before trying SteamVR
WindowsMixedRealityHMD=40
OpenXRHMD=30
OculusHMD=20
SteamVR=10

[/Script/Engine.AISystemBase]
AISystemModuleName=AIModule
AISystemClassName=/Script/AIModule.AISystem

[/Script/AIModule.AISystem]
PerceptionSystemClassName=/Script/AIModule.AIPerceptionSystem

[AutomationController.History]
bTrackHistory=false
NumberOfHistoryItemsTracked=5

[VisualLogger]
FrameCacheLenght=1.0f ;in seconds, to batch log data between file serializations
UseCompression=false ;works only with binary files

[GameplayDebuggerSettings]
OverHead=True
Basic=True
BehaviorTree=False
EQS=False
EnableEQSOnHUD=true
Perception=False
GameView1=False
GameView2=False
GameView3=False
GameView4=False
GameView5=False
NameForGameView1="GameView1"
NameForGameView2="GameView2"
NameForGameView3="GameView3"
NameForGameView4="GameView4"
NameForGameView5="GameView5"

[Browser]
bForceMessageLoop=true

[PacketSimulationProfile.Off]
; Preset that disables packet simulation
PktLoss=0
PktIncomingLoss=0
PktLagMin=0
PktLagMax=0
PktIncomingLagMin=0
PktIncomingLagMax=0

[PacketSimulationProfile.Average]
; Simulates network conditions with a ping of 60 to 120
PktLoss=1
PktIncomingLoss=1
PktLagMin=30
PktLagMax=60
PktIncomingLagMin=30
PktIncomingLagMax=60

[PacketSimulationProfile.Bad]
; Simulates network conditions with high packet loss and a ping of 200 to 400
PktLoss=5
PktIncomingLoss=5
PktLagMin=100
PktLagMax=200
PktIncomingLagMin=100
PktIncomingLagMax=200

[/Script/Engine.NetworkSettings]
+NetworkEmulationProfiles=(ProfileName="Average",ToolTip="Simulates average internet conditions")
+NetworkEmulationProfiles=(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")

[PacketHandlerComponents]
; Options can be set in this section of DefaultEngine.ini to enable different types of network packet encruption plugins
; EncryptionComponent=AESHandlerComponent

[/Script/GameplayDebugger.GameplayDebuggingControllerComponent]
CategoryZeroBind=(Key=NumPadZero,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryOneBind=(Key=NumPadOne,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryTwoBind=(Key=NumPadTwo,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryThreeBind=(Key=NumPadThree,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryFourBind=(Key=NumPadFour,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryFiveBind=(Key=NumPadFive,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategorySixBind=(Key=NumPadSix,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategorySevenBind=(Key=NumPadSeven,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryEightBind=(Key=NumPadEight,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CategoryNineBind=(Key=NumPadNine,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
CycleDetailsViewBind=(Key=Add,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
DebugCameraBind=(Key=Tab,bShift=False,bCtrl=False,bAlt=False,bCmd=False)
OnScreenDebugMessagesBind=(Key=Tab,bShift=False,bCtrl=True,bAlt=False,bCmd=False)
GameHUDBind=(Key=Tilde,bShift=False,bCtrl=True,bAlt=False,bCmd=False)

[/Script/Engine.SkeletalMeshLODSettings]
+LODGroups=(ScreenSize=(Default=1.0,PerPlatform=()),ReductionSettings=(NumOfTrianglesPercentage=.5))
+LODGroups=(ScreenSize=(Default=.3,PerPlatform=()),ReductionSettings=(NumOfTrianglesPercentage=.25))
+LODGroups=(ScreenSize=(Default=.15,PerPlatform=()),ReductionSettings=(NumOfTrianglesPercentage=.125))
+LODGroups=(ScreenSize=(Default=.1,PerPlatform=()),ReductionSettings=(NumOfTrianglesPercentage=.06))

[/Script/Engine.PlayerCameraManager]
ServerUpdateCameraTimeout=2.0

[/Script/CinematicCamera.CineCameraComponent]
+FilmbackPresets=(Name="16:9 Film",FilmbackSettings=(SensorWidth=24.00,SensorHeight=13.5))
+FilmbackPresets=(Name="16:9 Digital Film",FilmbackSettings=(SensorWidth=23.76,SensorHeight=13.365))
+FilmbackPresets=(Name="16:9 DSLR",FilmbackSettings=(SensorWidth=36,SensorHeight=20.25))
+FilmbackPresets=(Name="Super 8mm",FilmbackSettings=(SensorWidth=5.79,SensorHeight=4.01))
+FilmbackPresets=(Name="Super 16mm",FilmbackSettings=(SensorWidth=12.52,SensorHeight=7.58))
+FilmbackPresets=(Name="Super 35mm",FilmbackSettings=(SensorWidth=24.89,SensorHeight=18.66))
+FilmbackPresets=(Name="35mm Academy",FilmbackSettings=(SensorWidth=21.946,SensorHeight=16.002))
+FilmbackPresets=(Name="35mm Full Aperture",FilmbackSettings=(SensorWidth=24.892,SensorHeight=18.9121))
+FilmbackPresets=(Name="35mm VistaVision",FilmbackSettings=(SensorWidth=37.719,SensorHeight=25.146))
+FilmbackPresets=(Name="IMAX 70mm",FilmbackSettings=(SensorWidth=70.41,SensorHeight=56.63))
+FilmbackPresets=(Name="APS-C (Canon)",FilmbackSettings=(SensorWidth=22.2,SensorHeight=14.8))
+FilmbackPresets=(Name="Full Frame DSLR",FilmbackSettings=(SensorWidth=36,SensorHeight=24))
+FilmbackPresets=(Name="Micro Four Thirds",FilmbackSettings=(SensorWidth=17.3,SensorHeight=13))
DefaultFilmbackPresetName="16:9 DSLR" ; Deprecated, here for backwards compatibility
DefaultFilmbackPreset="16:9 Digital Film"
+LensPresets=(Name="12mm Prime f/2.8",LensSettings=(MinFocalLength=12,MaxFocalLength=12,MinFStop=2.8,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="30mm Prime f/1.4",LensSettings=(MinFocalLength=30,MaxFocalLength=30,MinFStop=1.4,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="50mm Prime f/1.8",LensSettings=(MinFocalLength=50,MaxFocalLength=50,MinFStop=1.8,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="85mm Prime f/1.8",LensSettings=(MinFocalLength=85,MaxFocalLength=85,MinFStop=1.8,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="105mm Prime f/2",LensSettings=(MinFocalLength=105,MaxFocalLength=105,MinFStop=2,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="200mm Prime f/2",LensSettings=(MinFocalLength=200,MaxFocalLength=200,MinFStop=2,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="24-70mm Zoom f/2.8",LensSettings=(MinFocalLength=24,MaxFocalLength=70,MinFStop=2.8,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="70-200mm Zoom f/2.8",LensSettings=(MinFocalLength=70,MaxFocalLength=200,MinFStop=2.8,MaxFStop=22,DiaphragmBladeCount=7))
+LensPresets=(Name="Universal Zoom",LensSettings=(MinFocalLength=4,MaxFocalLength=1000,MinFStop=1.2,MaxFStop=22,DiaphragmBladeCount=7))
DefaultLensPresetName="Universal Zoom"
DefaultLensFocalLength=35
DefaultLensFStop=2.8

[/Script/TcpMessaging.TcpMessagingSettings]
EnableTransport=True
ListenEndpoint=
!ConnectToEndpoints=CLEAR_ARRAY
ConnectionRetryDelay=2

[CrashReportClient]
bHideLogFilesOption=false
bIsAllowedToCloseWithoutSending=true
CrashConfigPurgeDays=2

[SteamVR.Settings]
HMDWornMovementThreshold = 50.0

[/Script/Engine.AnimationSettings]
bStripAnimationDataOnDedicatedServer=False
+BoneCustomAttributesNames=(Name="TCHour",Meaning="Timecode Hour")
+BoneCustomAttributesNames=(Name="TCMinute",Meaning="Timecode Minute")
+BoneCustomAttributesNames=(Name="TCSecond",Meaning="Timecode Second")
+BoneCustomAttributesNames=(Name="TCFrame",Meaning="Timecode Frame")
+BoneCustomAttributesNames=(Name="TCSubframe",Meaning="Timecode Subframe")
+BoneCustomAttributesNames=(Name="Takename",Meaning="Takename")

[Animation.DefaultObjectSettings]
BoneCompressionSettings="/Engine/Animation/DefaultAnimBoneCompressionSettings"
AnimationRecorderBoneCompressionSettings="/Engine/Animation/DefaultRecorderBoneCompression"
CurveCompressionSettings="/Engine/Animation/DefaultAnimCurveCompressionSettings"

[/Script/Engine.MeshSimplificationSettings]
r.MeshReductionModule="QuadricMeshReduction"

[/Script/ClassViewer.ClassViewerProjectSettings]
+InternalOnlyPaths=(Path="/Engine/VREditor")
+InternalOnlyPaths=(Path="/Engine/Sequencer")
+InternalOnlyPaths=(Path="/Engine/NotForLicensees")
+InternalOnlyClasses=/Script/VREditor.VREditorBaseUserWidget
+InternalOnlyClasses=/Script/LevelSequence.LevelSequenceBurnIn

[/Script/ClassViewer.StructViewerProjectSettings]
+InternalOnlyPaths=(Path="/Engine/VREditor")
+InternalOnlyPaths=(Path="/Engine/Sequencer")
+InternalOnlyPaths=(Path="/Engine/NotForLicensees")

[/Script/LevelSequence.LevelSequence]
DefaultCompletionMode=RestoreState

[/Script/TemplateSequence.TemplateSequence]
DefaultCompletionMode=RestoreState

[PlatformCrypto]
PlatformRequiresDataCrypto=False
PakSigningRequired=True
PakEncryptionRequired=True

[/Script/AppleARKit.AppleARKitSettings]
bEnableLiveLinkForFaceTracking=true
LiveLinkPublishingPort=11111
; by default arkit will be added to UIRequiredDeviceCapabilities and devices must support
; arkit. If false games must check that arkit is supported before enabling arkit features
bRequireDeviceSupportsARKit=true

[/Script/Engine.RendererSettings]
r.GPUCrashDebugging=false

[Messaging]
bAllowDelayedMessaging=false

[/Script/ChaosSolverEngine.ChaosSolverSettings]
DefaultChaosSolverActorClass=/Script/ChaosSolverEngine.ChaosSolverActor

[/Script/Engine.VirtualTexturePoolConfig]
; Configure VT physical memory pools. Usefull commands to set these
; "stat virtualtexturing" show dynamic use of the VT system including the cache loads
; "r.VT.ListPhysicalPool" list in-depth details on the allocated physical pools
; The upper allocation limit for a pool is caluclated by matching a size and format from the list below or falling back to the default
DefaultSizeInMegabyte=64
+Pools=(SizeInMegabyte=64, MinTileSize=0, MaxTileSize=9999, Formats=(PF_DXT1, PF_DXT5))
+Pools=(SizeInMegabyte=512, MinTileSize=0, MaxTileSize=9999, Formats=(PF_A32B32G32R32F))

[InstalledPlatforms]
HasInstalledPlatformInfo=true
+InstalledPlatformConfigurations=(PlatformName="Win64", Configuration="Development", PlatformType="Editor", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Win64", Configuration="DebugGame", PlatformType="Editor", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Win64", Configuration="DebugGame", PlatformType="Game", RequiredFile="Engine\Binaries\Win64\UE4Game.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Win64", Configuration="Development", PlatformType="Game", RequiredFile="Engine\Binaries\Win64\UE4Game.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Win64", Configuration="Shipping", PlatformType="Game", RequiredFile="Engine\Binaries\Win64\UE4Game-Win64-Shipping.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Android", Configuration="DebugGame", PlatformType="Game", Architecture="armv7", RequiredFile="Engine\Binaries\Android\UE4Game.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Android", Configuration="DebugGame", PlatformType="Game", Architecture="arm64", RequiredFile="Engine\Binaries\Android\UE4Game.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Android", Configuration="Development", PlatformType="Game", Architecture="armv7", RequiredFile="Engine\Binaries\Android\UE4Game.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Android", Configuration="Development", PlatformType="Game", Architecture="arm64", RequiredFile="Engine\Binaries\Android\UE4Game.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Android", Configuration="Shipping", PlatformType="Game", Architecture="armv7", RequiredFile="Engine\Binaries\Android\UE4Game-Android-Shipping.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Android", Configuration="Shipping", PlatformType="Game", Architecture="arm64", RequiredFile="Engine\Binaries\Android\UE4Game-Android-Shipping.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Linux", Configuration="DebugGame", PlatformType="Game", Architecture="x86_64-unknown-linux-gnu", RequiredFile="Engine\Binaries\Linux\UE4Game.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Linux", Configuration="Development", PlatformType="Game", Architecture="x86_64-unknown-linux-gnu", RequiredFile="Engine\Binaries\Linux\UE4Game.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="Linux", Configuration="Shipping", PlatformType="Game", Architecture="x86_64-unknown-linux-gnu", RequiredFile="Engine\Binaries\Linux\UE4Game-Linux-Shipping.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="IOS", Configuration="DebugGame", PlatformType="Game", RequiredFile="Engine\Binaries\IOS\UE4Game.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="IOS", Configuration="Development", PlatformType="Game", RequiredFile="Engine\Binaries\IOS\UE4Game.target", ProjectType="Any", bCanBeDisplayed=False)
+InstalledPlatformConfigurations=(PlatformName="IOS", Configuration="Shipping", PlatformType="Game", RequiredFile="Engine\Binaries\IOS\UE4Game-IOS-Shipping.target", ProjectType="Any", bCanBeDisplayed=False)