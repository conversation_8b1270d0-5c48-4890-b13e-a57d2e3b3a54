<?xml version='1.0' ?>
<BuildGraph xmlns="http://www.epicgames.com/BuildGraph" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.epicgames.com/BuildGraph ../../Engine/Build/Graph/Schema.xsd" >

	<!-- Helper option to make installed build for host platform only -->
	<Option Name="HostPlatformOnly" Restrict="true|false" DefaultValue="false" Description="A helper option to make an installed build for your host platform only, so that you don't have to disable each platform individually"/>

	<!-- Helper option to make an installed build with the host platform editor only -->
	<Option Name="HostPlatformEditorOnly" Restrict="true|false" DefaultValue="false" Description="A helper option to make an installed build for your host platform only, so that you don't have to disable each platform individually"/>
	
	<!-- Option to enable all target platforms by default, as we ship in the launcher. Note that this will require cross-compiling IOS on Windows. -->
	<Option Name="AllPlatforms" Restrict="true|false" DefaultValue="false" Description="Include all target platforms by default"/>

	<Option Name="CompileDatasmithPlugins" Restrict="true|false" DefaultValue="false" Description="If Datasmith plugins should be compiled on a separate node."/>

	<!-- Get default values for each platform being enabled -->
	<Property Name="DefaultWithWin64" Value="true"/>
	<Property Name="DefaultWithWin64" Value="false" If="$(HostPlatformEditorOnly) Or ($(HostPlatformOnly) And '$(HostPlatform)' != 'Win64')"/>

	<Property Name="DefaultWithMac" Value="true"/>
	<Property Name="DefaultWithMac" Value="false" If="$(HostPlatformEditorOnly) Or ($(HostPlatformOnly) And '$(HostPlatform)' != 'Mac')"/>

	<Property Name="DefaultWithLinux" Value="true"/>
	<Property Name="DefaultWithLinux" Value="false" If="$(HostPlatformEditorOnly) Or ($(HostPlatformOnly) And '$(HostPlatform)' != 'Linux')"/>

	<Property Name="DefaultWithLinuxAArch64" Value="true"/>
	<Property Name="DefaultWithLinuxAArch64" Value="false" If="$(HostPlatformEditorOnly) Or ($(HostPlatformOnly) And '$(HostPlatform)' != 'Linux')"/>

	<Property Name="DefaultWithPlatform" Value="true"/>
	<Property Name="DefaultWithPlatform" Value="false" If="$(HostPlatformEditorOnly) Or $(HostPlatformOnly)"/>

	<Property Name="DefaultWithIOS" Value="$(DefaultWithPlatform)"/>
	<Property Name="DefaultWithIOS" Value="false" If="'$(HostPlatform)' != 'Mac' And !$(AllPlatforms)"/>

	<Property Name="DefaultWithWin32" Value="$(DefaultWithPlatform)"/>
	<Property Name="DefaultWithWin32" Value="false" If="'$(HostPlatform)' != 'Win64' And !$(AllPlatforms)"/>

	<Property Name="DefaultWithHoloLens" Value="$(DefaultWithPlatform)"/>
	<Property Name="DefaultWithHoloLens" Value="false" If="'$(HostPlatform)' != 'Win64' And !$(AllPlatforms)"/>

	<!-- Setup default target platforms for Installed build if they haven't been specified on the commandline -->
	<Option Name="WithWin64" Restrict="true|false" DefaultValue="$(DefaultWithWin64)" Description="Include the Win64 target platform"/>
	<Option Name="WithWin32" Restrict="true|false" DefaultValue="$(DefaultWithWin32)" Description="Include the Win32 target platform"/>
	<Option Name="WithMac" Restrict="true|false" DefaultValue="$(DefaultWithMac)" Description="Include the Mac target platform"/>
	<Option Name="WithAndroid" Restrict="true|false" DefaultValue="$(DefaultWithPlatform)" Description="Include the Android target platform"/>
	<Option Name="WithIOS" Restrict="true|false" DefaultValue="$(DefaultWithIOS)" Description="Include the iOS target platform"/>
	<Option Name="WithTVOS" Restrict="true|false" DefaultValue="$(DefaultWithIOS)" Description="Include the tvOS target platform"/>
	<Option Name="WithLinux" Restrict="true|false" DefaultValue="$(DefaultWithLinux)" Description="Include the Linux target platform"/>
	<Option Name="WithLinuxAArch64" Restrict="true|false" DefaultValue="$(DefaultWithLinuxAArch64)" Description="Include the Linux AArch64 target platform"/>
	<Option Name="WithLumin" Restrict="true|false" DefaultValue="$(DefaultWithPlatform)" Description="Include the Lumin target platform on Windows"/>
	<Option Name="WithLuminMac" Restrict="true|false" DefaultValue="$(DefaultWithPlatform)" Description="Include the Lumin target platform on Mac"/>
	<Option Name="WithHoloLens" Restrict="true|false" DefaultValue="$(DefaultWithHoloLens)" Description="Include the HoloLens target platform"/>

	<!-- Set up target types -->
	<Option Name="WithClient" Restrict="true|false" DefaultValue="false" Description="Include precompiled client targets"/>
	<Option Name="WithServer" Restrict="true|false" DefaultValue="false" Description="Include precompiled server targets"/>
	
	<!-- Whether to create a prebuilt DDC -->
	<Option Name="WithDDC" Restrict="true|false" DefaultValue="false" Description="Build a standalone derived-data cache for the engine content and templates" />

	<!-- Whether to build DDC for the host platform only -->
	<Option Name="HostPlatformDDCOnly" Restrict="true|false" DefaultValue="true" Description="Whether to include DDC for the host platform only"/>

	<!-- Whether to sign any executables produced -->
	<Option Name="SignExecutables" Restrict="true|false" DefaultValue="false" Description="Sign the executables produced where signing is available"/>

	<!-- The analytics type -->
	<Option Name="AnalyticsTypeOverride" DefaultValue="" Description="Identifier for analytic events to send"/>

	<!-- Whether to embed source server info (ie. P4 paths) into PDB files -->
	<Option Name="EmbedSrcSrvInfo" Restrict="true|false" DefaultValue="false" Description="Whether to add Source indexing to Windows game apps so they can be added to a symbol server"/>
	
	<!-- Whether to embed storage symbol file to server -->
	<Option Name="EmbedSymStore" Restrict="true|false" DefaultValue="false" Description="Whether to add symbol files(e.g: *.pdb,*.exe,*.dll) to server"/>
	
	<!-- Which game configurations to include for packaged applications -->
	<Option Name="GameConfigurations" DefaultValue="DebugGame;Development;Shipping" Description="Which game configurations to include for packaged applications"/>

	<!-- Generate full debug info for binary editor and packaged application builds -->
	<Option Name="WithFullDebugInfo" Restrict="true|false" DefaultValue="true" Description="Generate full debug info for binary editor and packaged application builds"/>

	<!-- Whether to use VS2019 -->
	<Option Name="VS2019" Restrict="true|false" DefaultValue="false" Description="Use Visual Studio 2019 to build Windows targets. By default, Visual Studio 2017 is used for maximum compatibility"/>
	
	<!-- Whether to embed zip engine output -->
	<Option Name="EmbedZipOutPut" Restrict="true|false" DefaultValue="false" Description="Whether to embed zip engine output"/>
	
		
	<!-- Whether to remove symbol file -->
	<Option Name="EmbedRemoveSymbol" Restrict="true|false" DefaultValue="false" Description="Whether to embed Remove Symbol"/>
	
	<!-- The Output Zip Path -->
	<Option Name="EngineOutPutZipPath" DefaultValue="$(RootDir)/LocalBuilds/Engine/Bin/XVerseXStudio.zip" Description="EngineOutPutZipPath"/>
	
	<!-- The local output directory -->
	<Option Name="LocalInstalledDirOverride" DefaultValue="" Description="LocalInstalledDirOverride"/>
	
	<Property Name="LocalInstalledDir" Value="$(RootDir)/LocalBuilds/Engine/Windows"/>
	<Property Name="LocalInstalledDir" Value="$(LocalInstalledDirOverride)" If="'$(LocalInstalledDirOverride)' != ''"/>
	
	<Property Name="LocalInstalledDirMac" Value="$(RootDir)/LocalBuilds/Engine/Mac"/>
	<Property Name="LocalInstalledDirLinux" Value="$(RootDir)/LocalBuilds/Engine/Linux"/>

	<!-- Directory for storing build products like the Compressed DDC, Stripped and Signed files -->
	<Property Name="SavedOutput" Value="$(RootDir)/Engine/Saved"/>

	<!-- Directory for storing a stripped version of UAT for installed builds -->
	<Property Name="CsToolsDir" Value="$(SavedOutput)/CsTools"/>

	<!-- Directory for toolchains -->
	<Property Name="ToolchainDir" Value="$(RootDir)/Engine/Extras/ThirdPartyNotUE/SDKs"/>

	<!-- Architectures that we build for Android. NOTE: This should match the architectures specified in BuildPluginCommand. -->
	<Property Name="AndroidArchitectures" Value="armv7+arm64"/>
	<Property Name="LuminArchitectures" Value="armv7+arm64"/>
	
	<!-- Hololens Architectures -->
	<Property Name="HoloLensArchitectures" Value="x64+arm64+multi"/>

	<Property Name="AndroidGPUArchitectures" Value="es2"/>
	<Property Name="LuminGPUArchitectures" Value="es2"/>
	
	<!-- Compile flags for Windows Game binaries -->
	<Property Name="TargetDebugInfoArg" Value="-nodebuginfo"/>
	<Property Name="TargetDebugInfoArg" Value="" If="$(WithFullDebugInfo)"/>

	<!-- Compile flags for Windows targets -->
	<Property Name="VSCompilerArg" Value="-2017"/>
	<Property Name="VSCompilerArg" Value="-2019" If="$(VS2019)"/>

	<!-- Names of the client and server targets to build -->
	<Property Name="OptionalClientTarget" Value=""/>
	<Property Name="OptionalClientTarget" Value="UE4Client" If="$(WithClient)"/>

	<Property Name="OptionalServerTarget" Value=""/>
	<Property Name="OptionalServerTarget" Value="UE4Server" If="$(WithServer)"/>

	<Property Name="ExtensionPlatformDirs" Value=""/>
	<Macro Name="AddExtensionPlatform" Arguments="PlatformDir">
		<Property Name="Found" Value="false"/>
		<ForEach Name="Entry" Values="$(ExtensionPlatformDirs)">
			<Property Name="Found" Value="true" If="'$(Entry)' == '$(PlatformDir)'"/>
		</ForEach>
		<Do If="!$(Found)">
			<Property Name="ExtensionPlatformDirs" Value="$(ExtensionPlatformDirs);" If="'$(ExtensionPlatformDirs)' != ''"/>
			<Property Name="ExtensionPlatformDirs" Value="$(ExtensionPlatformDirs)$(PlatformDir)"/>
		</Do>
	</Macro>

	<Property Name="ExtensionIncludes" Value=""/>
	<Property Name="ExtensionPlatforms" Value=""/>
	<ForEach Name="Platform" Values="$(DataDrivenPlatforms)">
		<Property Name="PlatformBuildScript" Value="Platforms/$(Platform)/Build/InstalledEngineBuild.xml"/>
		<Do If="Exists('Engine/$(PlatformBuildScript)')">
			<Property Name="ExtensionIncludes" Value="$(ExtensionIncludes);" If="'$(ExtensionIncludes)' != ''"/>
			<Property Name="ExtensionIncludes" Value="$(ExtensionIncludes)../$(PlatformBuildScript)"/>
			<Property Name="ExtensionPlatforms" Value="$(ExtensionPlatforms);" If="'$(ExtensionPlatforms)' != ''"/>
			<Property Name="ExtensionPlatforms" Value="$(ExtensionPlatforms)$(Platform)"/>
		</Do>
	</ForEach>
	<Include Script="$(ExtensionIncludes)"/>

	<!-- Include the script that lists files and patterns used when creating an installed build -->
	<Include Script="InstalledEngineFilters.xml"/>

	<Option Name="CrashReporterAPIURL" DefaultValue="" Description="The URL to use to talk to the CrashReporterClient API."/>
	<Option Name="CrashReporterAPIKey" DefaultValue="" Description="The API key to use to talk to the CrashReporterClient API."/>
	<Property Name="CrashReporterCompileArgs" Value=""/>
	<Property Name="CrashReporterCompileArgs" Value="-define:CRC_TELEMETRY_URL=\&quot;$(CrashReporterAPIURL)\&quot; -define:CRC_TELEMETRY_KEY_DEV=\&quot;$(CrashReporterAPIKey)\&quot; -define:CRC_TELEMETRY_KEY_RELEASE=\&quot;$(CrashReporterAPIKey)\&quot; -OverrideBuildEnvironment" If="'$(CrashReporterAPIURL)' != '' And '$(CrashReporterAPIKey)' != ''" />

	<!-- Base editor and tools for compile on Windows -->
	<Agent Name="Editor Win64" Type="Win64_Licensee">
		<Node Name="Update Version Files">
			<Log Message="$(RootDir)"/>
			<SetVersion Change="$(Change)" Branch="$(EscapedBranch)" If="$(IsBuildMachine)"/>
		</Node>
		<Node Name="Compile UnrealHeaderTool Win64" Requires="Update Version Files">
			<Compile Target="UnrealHeaderTool" Platform="Win64" Configuration="Development" Arguments="-precompile -allmodules"/>
		</Node>
		<Node Name="Compile UE4Editor Win64" Requires="Compile UnrealHeaderTool Win64" Produces="#UE4Editor Win64;#UE4Editor Win64 Unstripped;#UE4Editor Win64 Stripped;#UE4Editor Win64 Unsigned;#UE4Editor Win64 Signed;#UE4Editor Win64 WithExceptions">
			<Compile Target="UE4Editor" Platform="Win64" Configuration="DebugGame" Tag="#UE4Editor Win64" Arguments="-precompile -allmodules $(CrashReporterCompileArgs)"/>
			<Compile Target="UE4Editor" Platform="Win64" Configuration="Development" Tag="#UE4Editor Win64" Arguments="-precompile -allmodules $(CrashReporterCompileArgs)"/>

			<Do If="$(EmbedSrcSrvInfo)">
				<!-- Embed source info into the PDB files. Should be done from this machine to ensure that paths are correct. -->
				<Log Message="Embedding source file information into PDB files..."/>
				<Tag Files="Engine/Source/...;Engine/Plugins/..." Filter="*.c;*.h;*.cpp;*.hpp;*.inl" Except="Engine/Source/ThirdParty/..." With="#SourceFiles"/>
				<SrcSrv BinaryFiles="#UE4Editor Win64" SourceFiles="#SourceFiles" Branch="$(Branch)" Change="$(Change)"/>
			</Do>
			


			<Property Name="UE4EditorExceptions" Value="$(Win64StripExceptions)"/>
			<Property Name="UE4EditorExceptions" Value="$(UE4EditorExceptions);$(PluginsExceptions)" If="$(CompileDatasmithPlugins)"/>
			<Tag Files="#UE4Editor Win64" Filter="$(Win64StripFilter)" Except="$(Win64StripExceptions)" With="#UE4Editor Win64 Unstripped"/>

			<Switch>
				<Case If="'$(WithFullDebugInfo)' == true">
					<!-- Copy files for local debug builds -->
					<Copy Files="#UE4Editor Win64 Unstripped" From="$(RootDir)" To="$(SavedOutput)"/>
				</Case>
				<Default>
					<!-- Otherwise, strip the files for distribution -->
					<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Win64" Files="#UE4Editor Win64 Unstripped" Tag="#UE4Editor Win64 Stripped"/>
				</Default>
			</Switch>

			<Do If="'$(SignExecutables)' == true">
				<Tag Files="#UE4Editor Win64" Filter="$(WindowsSignFilter)" Except="$(PluginsExceptions)" With="#UE4Editor Win64 Unsigned"/>
				<Copy Files="#UE4Editor Win64 Unsigned" From="$(RootDir)" To="$(SavedOutput)" Tag="#Editor_ToSign_Win64"/>
				<Sign Files="#Editor_ToSign_Win64" Tag="#UE4Editor Win64 Signed"/>
			</Do>
			<Tag Files="#UE4Editor Win64" Except="$(PluginsExceptions)" With="#UE4Editor Win64 WithExceptions"/>

		</Node>
	</Agent>
	<Aggregate Name="Win64 Editor" Label="Editors/Win64" Requires="Compile UE4Editor Win64" Include="Editor Win64"/>

	<!-- Base editor and tools for compile on Mac -->
	<Agent Name="Editor Mac" Type="Mac_Licensee">
		<Node Name="Compile UnrealHeaderTool Mac" Requires="Update Version Files">
			<Compile Target="UnrealHeaderTool" Platform="Mac" Configuration="Development" Arguments="-precompile -allmodules"/>
		</Node>
		<Node Name="Compile UE4Editor Mac" Requires="Compile UnrealHeaderTool Mac" Produces="#UE4Editor Mac;#UE4Editor Mac Unstripped;#UE4Editor Mac Stripped;#UE4Editor Mac Unsigned;#UE4Editor Mac Signed;#UE4Editor Mac WithExceptions">
			<Compile Target="UE4Editor" Platform="Mac" Configuration="DebugGame" Tag="#UE4Editor Mac" Arguments="-precompile -allmodules $(CrashReporterCompileArgs)"/>
			<Compile Target="UE4Editor" Platform="Mac" Configuration="Development" Tag="#UE4Editor Mac" Arguments="-precompile -allmodules $(CrashReporterCompileArgs)"/>

			<Property Name="UE4EditorExceptions" Value=""/>
			<Property Name="UE4EditorExceptions" Value="$(PluginsExceptions)" If="$(CompileDatasmithPlugins)"/>
			<Tag Files="#UE4Editor Mac" Filter="$(MacStripFilter)" Except="$(UE4EditorExceptions)" With="#UE4Editor Mac Unstripped"/>

			<Switch>
				<Case If="'$(WithFullDebugInfo)' == true">
					<!-- Copy files for local debug builds -->
					<Copy Files="#UE4Editor Mac Unstripped" From="$(RootDir)" To="$(SavedOutput)"/>
				</Case>
				<Default>
					<!-- Otherwise, strip the files for distribution -->
					<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Mac" Files="#UE4Editor Mac Unstripped" Tag="#UE4Editor Mac Stripped"/>
				</Default>
			</Switch>

			<Do If="'$(SignExecutables)' == true">
				<Tag Files="#UE4Editor Mac" Filter="$(MacSignFilter)" Except="$(PluginsExceptions)" With="#UE4Editor Mac Unsigned"/>
				<Copy Files="#UE4Editor Mac Unsigned" From="$(RootDir)" To="$(SavedOutput)" Tag="#Editor_ToSign_Mac"/>
				<Sign Files="#Editor_ToSign_Mac" Tag="#UE4Editor Mac Signed"/>
			</Do>

			<Tag Files="#UE4Editor Mac" Except="$(PluginsExceptions)" With="#UE4Editor Mac WithExceptions"/>
		</Node>
	</Agent>
	<Aggregate Name="Mac Editor" Label="Editors/Mac" Requires="Compile UE4Editor Mac" Include="Editor Mac"/>

	<!-- Base editor and tools for compile on Linux -->
	<Agent Name="Editor Linux" Type="Win64_Licensee">
		<Node Name="Compile UnrealHeaderTool Linux" Requires="Update Version Files">
			<Compile Target="UnrealHeaderTool" Platform="Linux" Configuration="Development" Arguments="-precompile -allmodules"/>
		</Node>
		<Node Name="Compile UE4Editor Linux" Requires="Compile UnrealHeaderTool $(HostPlatform)" Produces="#UE4Editor Linux;#UE4Editor Linux Unstripped;#UE4Editor Linux Stripped">
			<Compile Target="UE4Editor" Platform="Linux" Configuration="DebugGame" Tag="#UE4Editor Linux" Arguments="-precompile -allmodules"/>
			<Compile Target="UE4Editor" Platform="Linux" Configuration="Development" Tag="#UE4Editor Linux" Arguments="-precompile -allmodules"/>

			<Tag Files="#UE4Editor Linux" Filter="$(LinuxStripFilter)" With="#UE4Editor Linux Unstripped"/>

			<Switch>
				<Case If="'$(WithFullDebugInfo)' == true">
					<!-- Copy files for local debug builds -->
					<Copy Files="#UE4Editor Linux Unstripped" From="$(RootDir)" To="$(SavedOutput)" Tag="#UE4Editor Linux Stripped"/>
				</Case>
				<Default>
					<!-- Otherwise, strip the files for distribution -->
					<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Linux" Files="#UE4Editor Linux Unstripped" Tag="#UE4Editor Linux Stripped"/>
				</Default>
			</Switch>
		</Node>
	</Agent>

	<!-- Target Platforms normally built from a Win64 machine -->
	<Agent Name="Target Win64" Type="Win64_Licensee">
		<Node Name="Compile UE4Game Win64" Requires="Compile UnrealHeaderTool Win64" Produces="#UE4Game Win64;#UE4Game Win64 Unstripped;#UE4Game Win64 Stripped;#UE4Game Win64 Unsigned;#UE4Game Win64 Signed">
			<ForEach Name="Target" Values="UE4Game;$(OptionalClientTarget);$(OptionalServerTarget)">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="$(Target)" Platform="Win64" Configuration="$(Configuration)" Tag="#UE4Game Win64" Arguments="-precompile -allmodules -nolink $(VSCompilerArg) $(TargetDebugInfoArg)"/>
					<Compile Target="$(Target)" Platform="Win64" Configuration="$(Configuration)" Tag="#UE4Game Win64" Arguments="-precompile $(VSCompilerArg) $(TargetDebugInfoArg)" Clean="false"/>
				</ForEach>
			</ForEach>

			<Do If="$(EmbedSrcSrvInfo)">
				<!-- Embed source info into the PDB files. Should be done from this machine to ensure that paths are correct. -->
				<Log Message="Embedding source file information into PDB files..."/>
				<Tag Files="Engine/Source/...;Engine/Plugins/..." Filter="*.c;*.h;*.cpp;*.hpp;*.inl" Except="Engine/Source/ThirdParty/..." With="#SourceFiles"/>
				<SrcSrv BinaryFiles="#UE4Game Win64" SourceFiles="#SourceFiles" Branch="$(Branch)" Change="$(Change)"/>
			</Do>

			<Tag Files="#UE4Game Win64" Filter="$(Win64StripFilter)" Except="$(Win64StripExceptions)" With="#UE4Game Win64 Unstripped"/>

			<Switch>
				<Case If="'$(WithFullDebugInfo)' == true">
					<!-- Copy files for local debug builds -->
					<Copy Files="#UE4Game Win64 Unstripped" From="$(RootDir)" To="$(SavedOutput)"/>
				</Case>
				<Default>
					<!-- Otherwise, strip the files for distribution -->
					<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Win64" Files="#UE4Game Win64 Unstripped" Tag="#UE4Game Win64 Stripped"/>
				</Default>
			</Switch>

			<Do If="'$(SignExecutables)' == true">
				<Tag Files="#UE4Game Win64" Filter="$(WindowsSignFilter)" With="#UE4Game Win64 Unsigned"/>
				<Copy Files="#UE4Game Win64 Unsigned" From="$(RootDir)" To="$(SavedOutput)" Tag="#Game_ToSign_Win64"/>
				<Sign Files="#Game_ToSign_Win64" Tag="#UE4Game Win64 Signed"/>
			</Do>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_Win64" Label="Platforms/Win64" Requires="Compile UE4Game Win64"/>

	<Agent Name="Target Win32" Type="Win64_Licensee">
		<Node Name="Compile UE4Game Win32" Requires="Compile UnrealHeaderTool Win64" Produces="#UE4Game Win32;#UE4Game Win32 Unstripped;#UE4Game Win32 Stripped;#UE4Game Win32 Unsigned;#UE4Game Win32 Signed">
			<ForEach Name="Target" Values="UE4Game;$(OptionalClientTarget)">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="$(Target)" Platform="Win32" Configuration="$(Configuration)" Tag="#UE4Game Win32" Arguments="-precompile -allmodules -nolink $(VSCompilerArg) $(TargetDebugInfoArg)"/>
					<Compile Target="$(Target)" Platform="Win32" Configuration="$(Configuration)" Tag="#UE4Game Win32" Arguments="-precompile $(VSCompilerArg) $(TargetDebugInfoArg)" Clean="false"/>
				</ForEach>
			</ForEach>

			<Do If="$(EmbedSrcSrvInfo)">
				<!-- Embed source info into the PDB files. Should be done from this machine to ensure that paths are correct. -->
				<Log Message="Embedding source file information into PDB files..."/>
				<Tag Files="Engine/Source/...;Engine/Plugins/..." Filter="*.c;*.h;*.cpp;*.hpp;*.inl" Except="Engine/Source/ThirdParty/..." With="#SourceFiles"/>
				<SrcSrv BinaryFiles="#UE4Game Win32" SourceFiles="#SourceFiles" Branch="$(Branch)" Change="$(Change)"/>
			</Do>

			<Tag Files="#UE4Game Win32" Filter="$(Win32StripFilter)" With="#UE4Game Win32 Unstripped"/>

			<Switch>
				<Case If="'$(WithFullDebugInfo)' == true">
					<!-- Copy files for local debug builds -->
					<Copy Files="#UE4Game Win32 Unstripped" From="$(RootDir)" To="$(SavedOutput)"/>
				</Case>
				<Default>
					<!-- Otherwise, strip the files for distribution -->
					<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Win32" Files="#UE4Game Win32 Unstripped" Tag="#UE4Game Win32 Stripped"/>
				</Default>
			</Switch>


			<Do If="'$(SignExecutables)' == true">
				<Tag Files="#UE4Game Win32" Filter="$(WindowsSignFilter)" With="#UE4Game Win32 Unsigned"/>
				<Copy Files="#UE4Game Win32 Unsigned" From="$(RootDir)" To="$(SavedOutput)" Tag="#Game_ToSign_Win32"/>
				<Sign Files="#Game_ToSign_Win32" Tag="#UE4Game Win32 Signed"/>
			</Do>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_Win32" Label="Platforms/Win32" Requires="Compile UE4Game Win32"/>

	<Agent Name="Target Linux" Type="Win64_Licensee">
		<Node Name="Compile UE4Game Linux" Requires="Compile UnrealHeaderTool $(HostPlatform)" Produces="#UE4Game Linux;#UE4Game Linux Unstripped;#UE4Game Linux Stripped">
			<ForEach Name="Target" Values="UE4Game;$(OptionalClientTarget);$(OptionalServerTarget)">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="$(Target)" Platform="Linux" Configuration="$(Configuration)" Tag="#UE4Game Linux" Arguments="-precompile -allmodules -nolink $(TargetDebugInfoArg)"/>
					<Compile Target="$(Target)" Platform="Linux" Configuration="$(Configuration)" Tag="#UE4Game Linux" Arguments="-precompile $(TargetDebugInfoArg)" Clean="false"/>
				</ForEach>
			</ForEach>

			<Tag Files="#UE4Game Linux" Filter="$(LinuxStripFilter)" With="#UE4Game Linux Unstripped"/>
			<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Linux" Files="#UE4Game Linux Unstripped" Tag="#UE4Game Linux Stripped"/>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_Linux" Requires="Compile UE4Game Linux"/>
	
	<Agent Name="Target LinuxAArch64" Type="Win64_Licensee">
		<Node Name="Compile UE4Game LinuxAArch64" Requires="Compile UnrealHeaderTool $(HostPlatform)" Produces="#UE4Game LinuxAArch64;#UE4Game LinuxAArch64 Unstripped;#UE4Game LinuxAArch64 Stripped">
			<ForEach Name="Target" Values="UE4Game;$(OptionalClientTarget);$(OptionalServerTarget)">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="$(Target)" Platform="LinuxAArch64" Configuration="$(Configuration)" Tag="#UE4Game LinuxAArch64" Arguments="-precompile -allmodules $(TargetDebugInfoArg)"/>
					<Compile Target="$(Target)" Platform="LinuxAArch64" Configuration="$(Configuration)" Tag="#UE4Game LinuxAArch64" Arguments="-precompile $(TargetDebugInfoArg)" Clean="false"/>
				</ForEach>
			</ForEach>

			<Tag Files="#UE4Game LinuxAArch64" Filter="$(LinuxStripFilter)" With="#UE4Game LinuxAArch64 Unstripped"/>
			<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="LinuxAArch64" Files="#UE4Game LinuxAArch64 Unstripped" Tag="#UE4Game LinuxAArch64 Stripped"/>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_LinuxAArch64" Label="Platforms/Linux (AArch64)" Requires="Compile UE4Game LinuxAArch64"/>

	<Agent Name="Target Android" Type="Win64_Licensee">
		<Node Name="Compile UE4Game Android" Requires="Compile UnrealHeaderTool $(HostPlatform)" Produces="#UE4Game Android;#UE4Game Android Unstripped;#UE4Game Android Stripped">
			<ForEach Name="Target" Values="UE4Game;$(OptionalClientTarget)">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="$(Target)" Platform="Android" Configuration="$(Configuration)" Tag="#UE4Game Android" Arguments="-precompile -allmodules -nolink -architectures=$(AndroidArchitectures) $(TargetDebugInfoArg)"/>
					<Compile Target="$(Target)" Platform="Android" Configuration="$(Configuration)" Tag="#UE4Game Android" Arguments="-precompile -architectures=$(AndroidArchitectures) $(TargetDebugInfoArg)" Clean="false"/>
				</ForEach>
			</ForEach>

			<Tag Files="#UE4Game Android" Filter="$(AndroidStripFilter)" With="#UE4Game Android Unstripped"/>
			<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Android" Files="#UE4Game Android Unstripped" Tag="#UE4Game Android Stripped"/>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_Android" Label="Platforms/Android" Requires="Compile UE4Game Android"/>
	
	<ForEach Name="Platform" Values="$(ExtensionPlatforms)">
		<Expand Name="Agent_Target_$(Platform)_Win64_Licensee"/>
	</ForEach>

	<Agent Name="Target HoloLens" Type="HoloLens">
		<Node Name="Compile UE4Game HoloLens" Requires="Compile UnrealHeaderTool Win64" Produces="#UE4Game HoloLens">
			<ForEach Name="Configuration" Values="$(GameConfigurations)">
				<Compile Target="UE4Game" Platform="HoloLens" Configuration="$(Configuration)" Tag="#UE4Game HoloLens" Arguments="-precompile -allmodules -nolink -architecture=arm64 $(TargetDebugInfoArg)"/>
				<Compile Target="UE4Game" Platform="HoloLens" Configuration="$(Configuration)" Tag="#UE4Game HoloLens" Arguments="-precompile -architecture=arm64 $(TargetDebugInfoArg)" Clean="false"/>
				<Compile Target="UE4Game" Platform="HoloLens" Configuration="$(Configuration)" Tag="#UE4Game HoloLens" Arguments="-precompile -allmodules -nolink -architecture=x64 $(TargetDebugInfoArg)"/>
				<Compile Target="UE4Game" Platform="HoloLens" Configuration="$(Configuration)" Tag="#UE4Game HoloLens" Arguments="-precompile -architecture=x64 $(TargetDebugInfoArg)" Clean="false"/>
			</ForEach>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_HoloLens" Label="Platforms/HoloLens" Requires="Compile UE4Game HoloLens"/>

	<Agent Name="Target Lumin" Type="Win64_Licensee">
		<Node Name="Compile UE4Game Lumin" Requires="Compile UnrealHeaderTool Win64" Produces="#UE4Game Lumin;#UE4Game Lumin Unstripped;#UE4Game Lumin Stripped">
			<ForEach Name="Target" Values="UE4Game;$(OptionalClientTarget)">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="$(Target)" Platform="Lumin" Configuration="$(Configuration)" Tag="#UE4Game Lumin" AllowXGE="false" Arguments="-precompile -allmodules -nolink -architectures=$(LuminArchitectures) -gpuarchitectures=$(LuminGPUArchitectures) $(TargetDebugInfoArg)"/>
					<Compile Target="$(Target)" Platform="Lumin" Configuration="$(Configuration)" Tag="#UE4Game Lumin" AllowXGE="false" Arguments="-precompile -architectures=$(LuminArchitectures) -gpuarchitectures=$(LuminGPUArchitectures) $(TargetDebugInfoArg)" Clean="false"/>
				</ForEach>
			</ForEach>
			
			<Tag Files="#UE4Game Lumin" Filter="$(LuminStripFilter)" With="#UE4Game Lumin Unstripped"/>
			<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Lumin" Files="#UE4Game Lumin Unstripped" Tag="#UE4Game Lumin Stripped"/>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_Lumin" Label="Platforms/Lumin" Requires="Compile UE4Game Lumin"/>

	<!-- Target Platforms normally built from a Mac machine -->
	<Agent Name="Target Mac" Type="Mac_Licensee">
		<Node Name="Compile UE4Game Mac" Requires="Compile UnrealHeaderTool Mac" Produces="#UE4Game Mac;#UE4Game Mac Unstripped;#UE4Game Mac Stripped;#UE4Game Mac Unsigned;#UE4Game Mac Signed">
			<ForEach Name="Target" Values="UE4Game;$(OptionalClientTarget);$(OptionalServerTarget)">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="$(Target)" Platform="Mac" Configuration="$(Configuration)" Tag="#UE4Game Mac" Arguments="-precompile -allmodules -nolink $(TargetDebugInfoArg)"/>
					<Compile Target="$(Target)" Platform="Mac" Configuration="$(Configuration)" Tag="#UE4Game Mac" Arguments="-precompile $(TargetDebugInfoArg)" Clean="false"/>
				</ForEach>
			</ForEach>

			<Tag Files="#UE4Game Mac" Filter="$(MacStripFilter)" With="#UE4Game Mac Unstripped"/>
			<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Mac" Files="#UE4Game Mac Unstripped" Tag="#UE4Game Mac Stripped"/>

			<Do If="'$(SignExecutables)' == true">
				<Tag Files="#UE4Game Mac" Filter="$(MacSignFilter)" With="#UE4Game Mac Unsigned"/>
				<Copy Files="#UE4Game Mac Unsigned" From="$(RootDir)" To="$(SavedOutput)" Tag="#Game_ToSign_Mac"/>
				<Sign Files="#Game_ToSign_Mac" Tag="#UE4Game Mac Signed"/>
			</Do>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_Mac" Requires="Compile UE4Game Mac"/>

	<Agent Name="Target IOS" Type="Mac_Licensee">
		<Node Name="Compile UE4Game IOS" Requires="Compile UnrealHeaderTool Mac" Produces="#UE4Game IOS;#UE4Game IOS Unstripped;#UE4Game IOS Stripped">
			<ForEach Name="Target" Values="UE4Game;$(OptionalClientTarget)">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="$(Target)" Platform="IOS" Configuration="$(Configuration)" Tag="#UE4Game IOS" Arguments="-precompile -allmodules -nolink $(TargetDebugInfoArg)"/>
					<Compile Target="$(Target)" Platform="IOS" Configuration="$(Configuration)" Tag="#UE4Game IOS" Arguments="-precompile -createstub $(TargetDebugInfoArg)" Clean="false"/>
				</ForEach>
			</ForEach>

			<Tag Files="#UE4Game IOS" Filter="$(IOSStripFilter)" With="#UE4Game IOS Unstripped"/>
			<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="IOS" Files="#UE4Game IOS Unstripped" Tag="#UE4Game IOS Stripped"/>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_IOS" Label="Platforms/IOS" Requires="Compile UE4Game IOS"/>

	<Agent Name="Target TVOS" Type="Mac_Licensee">
		<Node Name="Compile UE4Game TVOS" Requires="Compile UnrealHeaderTool Mac" Produces="#UE4Game TVOS;#UE4Game TVOS Unstripped;#UE4Game TVOS Stripped">
			<ForEach Name="Target" Values="UE4Game;$(OptionalClientTarget)">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="$(Target)" Platform="TVOS" Configuration="$(Configuration)" Tag="#UE4Game TVOS" Arguments="-precompile -allmodules -nolink $(TargetDebugInfoArg)"/>
					<Compile Target="$(Target)" Platform="TVOS" Configuration="$(Configuration)" Tag="#UE4Game TVOS" Arguments="-precompile -createstub $(TargetDebugInfoArg)" Clean="false"/>
				</ForEach>
			</ForEach>

			<Tag Files="#UE4Game TVOS" Filter="$(TVOSStripFilter)" With="#UE4Game TVOS Unstripped"/>
			<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="TVOS" Files="#UE4Game TVOS Unstripped" Tag="#UE4Game TVOS Stripped"/>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_TVOS" Label="Platforms/TVOS" Requires="Compile UE4Game TVOS"/>

	<Agent Name="Target LuminMac" Type="Mac_Licensee">
		<Node Name="Compile UE4Game LuminMac" Requires="Compile UnrealHeaderTool Mac" Produces="#UE4Game LuminMac;#UE4Game LuminMac Unstripped;#UE4Game LuminMac Stripped">
			<ForEach Name="Target" Values="UE4Game;$(OptionalClientTarget)">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="$(Target)" Platform="Lumin" Configuration="$(Configuration)" Tag="#UE4Game LuminMac" Arguments="-precompile -allmodules -nolink -architectures=$(LuminArchitectures) -gpuarchitectures=$(LuminGPUArchitectures) $(TargetDebugInfoArg)" AllowParallelExecutor="false"/>
					<Compile Target="$(Target)" Platform="Lumin" Configuration="$(Configuration)" Tag="#UE4Game LuminMac" Arguments="-precompile -architectures=$(LuminArchitectures) -gpuarchitectures=$(LuminGPUArchitectures) $(TargetDebugInfoArg)" AllowParallelExecutor="false" Clean="false"/>
				</ForEach>
			</ForEach>

			<Tag Files="#UE4Game LuminMac" Filter="$(LuminStripFilter)" With="#UE4Game LuminMac Unstripped"/>
			<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Lumin" Files="#UE4Game LuminMac Unstripped" Tag="#UE4Game LuminMac Stripped"/>
		</Node>
	</Agent>
	<Aggregate Name="TargetPlatforms_LuminMac" Label="Platforms/LuminMac" Requires="Compile UE4Game LuminMac"/>

	<Agent Name="NotForLicensee Tools Group Win64" Type="Win64">
		<Node Name="Build NotForLicensee Tools Win64" Requires="Compile UnrealHeaderTool Win64" Produces="#NotForLicensee Build Tools Win64">
			<Compile Target="DatasmithCADWorker" Configuration="Development" Platform="Win64" Tag="#NotForLicensee Build Tools Win64"/>
		</Node>
	</Agent>
	
	<Agent Name="BuildRules" Type="Win64_Licensee">
		<Node Name="Compile Rules Assemblies" Produces="#Rules Assemblies">
			<Spawn Exe="Engine/Binaries/DotNET/UnrealBuildTool.exe" Arguments="-Mode=QueryTargets"/>
			<Tag Files="Engine/Intermediate/Build/BuildRules/..." With="#Rules Assemblies"/>
		</Node>
	</Agent>

	<Agent Name="Tools Group Win64" Type="Win64_Licensee">
		<Node Name="Build Tools Win64" Requires="Compile UnrealHeaderTool Win64;#NotForLicensee Build Tools Win64" Produces="#Tools Win64 Complete">
			<Compile Target="CrashReportClient" Configuration="Shipping" Platform="Win64" Tag="#Build Tools Win64" Arguments="$(CrashReporterCompileArgs)"/>
			<Compile Target="CrashReportClientEditor" Configuration="Shipping" Platform="Win64" Tag="#Build Tools Win64" Arguments="$(CrashReporterCompileArgs)"/>
			<Tag Files="#NotForLicensee Build Tools Win64" With="#Build Tools Win64"/>
			<Compile Target="ShaderCompileWorker" Configuration="Development" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="UnrealCEFSubProcess" Configuration="Development" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="UnrealInsights" Configuration="Development" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="UnrealFrontend" Configuration="Development" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="UnrealLightmass" Configuration="Development" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="UnrealPak" Configuration="Development" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="UnrealMultiUserServer" Configuration="Development" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="UnrealRecoverySvc" Configuration="Development" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="LiveCodingConsole" Configuration="Development" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="BootstrapPackagedGame" Configuration="Shipping" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="BuildPatchTool" Configuration="Shipping" Platform="Win64" Tag="#Build Tools Win64"/>
			<Compile Target="SwitchboardListener" Configuration="Development" Platform="Win64" Tag="#Build Tools Win64"/>

			<Tag Files="Engine/Binaries/Win64/XGEControlWorker.exe" With="#Build Tools Win64"/>
			<Tag Files="$(RootDir)/Engine/Saved/UnrealBuildTool/BuildConfiguration.Schema.xsd" With="#Build Tools Win64"/>

			<Do If="$(EmbedSrcSrvInfo)">
				<!-- Embed source info into the PDB files. Should be done from this machine to ensure that paths are correct. -->
				<Log Message="Embedding source file information into PDB files..."/>
				<Tag Files="Engine/Source/...;Engine/Plugins/..." Filter="*.c;*.h;*.cpp;*.hpp;*.inl" Except="Engine/Source/ThirdParty/..." With="#SourceFiles"/>
				<SrcSrv BinaryFiles="#Build Tools Win64" SourceFiles="#SourceFiles" Branch="$(Branch)" Change="$(Change)"/>
			</Do>

			<WriteTextFile File="ToolComplete.txt" Tag="#Tools Win64 Complete"/>
		</Node>
		<!-- Compile the C# tools. Copies files to a temporary directory first, to strip out all confidential folders and avoid sharing violations with the running version of UAT. -->
		<Node Name="Build Tools CS" Requires="Update Version Files" Produces="#Build Tools CS Binaries;#SourceForDocumentation">
			<Delete Files="$(CsToolsDir)/..."/>

			<!-- Copy Source and referenced libraries to a new location with Confidential folders removed-->
			<Tag Files="$(AutomationToolPaths)" Filter="*.csproj" Except="$(ConfidentialExceptions)"  With="#UAT Projects"/>
			<CsCompile Project="#UAT Projects" Configuration="Development" Platform="AnyCPU" TagReferences="#UATReferences" EnumerateOnly="true"/>
			<Tag Files="Engine/Binaries/DotNET/...;Engine/Binaries/ThirdParty/Newtonsoft/...;Engine/Binaries/ThirdParty/VisualStudio/...;Engine/Source/Programs/...;Engine/Platforms/*/Source/Programs/...;Engine/Source/Editor/SwarmInterface/...;#UATReferences" Except="$(ConfidentialExceptions)" With="#RedistUATSource"/>
			<ForEach Name="Platform" Values="$(ExtensionPlatforms)">
				<Expand Name="$(Platform)_TagCsToolsRedistUATSource"/>
			</ForEach>
			<Copy Files="#RedistUATSource" From="$(RootDir)" To="$(CsToolsDir)"/>

			<Property Name="RedistAutomationToolPaths" Value=""/>
			<ForEach Name="AutomationToolPath" Values="$(AutomationToolPaths)">
				<Property Name="RedistAutomationToolPaths" Value="$(RedistAutomationToolPaths)$(CsToolsDir)/$(AutomationToolPath);"/>
			</ForEach>
			<Tag Files="$(RedistAutomationToolPaths)" Filter="*.csproj" With="#RedistUATProjects"/>

			<!-- Compile all the tools -->
			<CsCompile Project="#RedistUATProjects" Configuration="Development" Platform="AnyCPU" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
			<Do If="'$(HostPlatform)' == 'Win64'">
				<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/UnrealSwarm/SwarmCoordinator/SwarmCoordinator.csproj" Configuration="Development" Platform="AnyCPU" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
				<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/UnrealSwarm/Agent/Agent.csproj" Configuration="Development" Platform="AnyCPU" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
				<CsCompile Project="$(CsToolsDir)/Engine/Source/Editor/SwarmInterface/DotNET/SwarmInterface.csproj" Configuration="Development" Platform="AnyCPU" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
				<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/nDisplayLauncher/nDisplayLauncher.csproj" Configuration="Development" Platform="AnyCPU" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
				<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/nDisplayListener/nDisplayListener.csproj" Configuration="Development" Platform="AnyCPU" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
				<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/UnrealControls/UnrealControls.csproj" Configuration="Development" Platform="AnyCPU" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
				<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/IOS/iPhonePackager/iPhonePackager.csproj" Configuration="Development" Platform="AnyCPU" Arguments="/verbosity:minimal /target:Rebuild" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
				<ForEach Name="Platform" Values="$(ExtensionPlatforms)">
					<Expand Name="$(Platform)_Build_Tools_CS_Binaries"/>
				</ForEach>
			</Do>
			
			<!-- Compile IPP. Mac/Linux bundled versions of Mono do not have Winforms, so we just use the existing one. -->
			<Property Name="CompileIPP" Value="true"/>
			<Property Name="CompileIPP" Value="false" If="'$(HostPlatform)' != 'Win64'"/>
			<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/UnrealControls/UnrealControls.csproj" Configuration="Development" Platform="AnyCPU" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries" EnumerateOnly="!$(CompileIPP)"/>
			<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/IOS/iPhonePackager/iPhonePackager.csproj" Configuration="Development" Platform="AnyCPU" Arguments="/verbosity:minimal /target:Rebuild" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries" EnumerateOnly="!$(CompileIPP)"/>
			
			<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/NetworkProfiler/NetworkProfiler/NetworkProfiler.csproj" Configuration="Development" Platform="AnyCPU" Tag="#Build Tools CS Binaries" TagReferences="#Build Tools CS Binaries;#Sign Binaries" If="'$(HostPlatform)' != 'Linux'"/>
			<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/IOS/DeploymentServer/DeploymentServer.csproj" Configuration="Development" Platform="AnyCPU" Arguments="/verbosity:minimal /target:Rebuild" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
			<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/IOS/MobileDeviceInterface/MobileDeviceInterface.csproj" Configuration="Development" Platform="AnyCPU" Arguments="/verbosity:minimal /target:Rebuild" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
			<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/IOS/DeploymentInterface/DeploymentInterface.csproj" Configuration="Development" Platform="AnyCPU" Arguments="/verbosity:minimal /target:Rebuild" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>
			<CsCompile Project="$(CsToolsDir)/Engine/Source/Programs/IOS/DeploymentServerLauncher/DeploymentServerLauncher.csproj" Configuration="Development" Platform="AnyCPU" Arguments="/verbosity:minimal /target:Rebuild" Tag="#Build Tools CS Binaries;#Sign Binaries" TagReferences="#Build Tools CS Binaries"/>

			<!-- Create tag set for saved source needed for documentation -->
			<Tag Files="$(CsToolsDir)/Engine/..." With="#SourceForDocumentation" />

			<!-- Sign the binaries -->
			<Sign Files="#Sign Binaries" If="'$(SignExecutables)' == true"/>
		</Node>
		<Node Name="Build Tools Linux" Requires="Compile UnrealHeaderTool $(HostPlatform)">
			<Compile Target="CrashReportClient" Platform="Linux" Configuration="Shipping"/>
			<Compile Target="CrashReportClientEditor" Platform="Linux" Configuration="Shipping"/>
			<Compile Target="ShaderCompileWorker" Configuration="Development" Platform="Linux" Tag="#Build Tools Linux"/>
			<Compile Target="UnrealInsights" Configuration="Development" Platform="Linux" Tag="#Build Tools Linux"/>
			<Compile Target="UnrealLightmass" Configuration="Development" Platform="Linux" Tag="#Build Tools Linux"/>
			<Compile Target="UnrealPak" Configuration="Development" Platform="Linux" Tag="#Build Tools Linux"/>
			<Compile Target="UnrealMultiUserServer" Configuration="Development" Platform="Linux" Tag="#Build Tools Linux"/>
			<Compile Target="UnrealRecoverySvc" Configuration="Development" Platform="Linux" Tag="#Build Tools Linux"/>
			<Compile Target="SwitchboardListener" Configuration="Development" Platform="Linux" Tag="#Build Tools Linux"/>
		</Node>
		<Node Name="Build Tools Win32" Requires="Compile UnrealHeaderTool Win64">
			<Compile Target="CrashReportClient" Configuration="Shipping" Platform="Win32" Tag="#Build Tools Win32"/>
			<Compile Target="UnrealCEFSubProcess" Configuration="Development" Platform="Win32" Tag="#Build Tools Win32"/>
			<Compile Target="BootstrapPackagedGame" Configuration="Shipping" Platform="Win32" Tag="#Build Tools Win32"/>
		</Node>
	</Agent>

	<Agent Name="Tools Group Mac" Type="Mac_Licensee">
		<Node Name="Build Tools Mac" Requires="Compile UnrealHeaderTool Mac">
			<Compile Target="CrashReportClient" Configuration="Shipping" Platform="Mac" Tag="#Build Tools Mac" Arguments="$(CrashReporterCompileArgs)"/>
			<Compile Target="CrashReportClientEditor" Configuration="Shipping" Platform="Mac" Tag="#Build Tools Mac" Arguments="$(CrashReporterCompileArgs)"/>
			<Compile Target="ShaderCompileWorker" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="UnrealCEFSubProcess" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="UnrealInsights" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="UnrealFrontend" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="UnrealLightmass" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="UnrealPak" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="UnrealMultiUserServer" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="UnrealRecoverySvc" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="BuildPatchTool" Configuration="Shipping" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="DsymExporter" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="UE4EditorServices" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
			<Compile Target="UnrealAtoS" Configuration="Development" Platform="Mac" Tag="#Build Tools Mac"/>
		</Node>
	</Agent>

	<Agent Name="FeaturePacks Group" Type="Win64_Licensee">
		<Node Name="Make Feature Packs" Requires="Update Version Files;Build Tools $(HostPlatform)">
			<!--Delete any pre-existing feature packs-->
			<Delete Files="FeaturePacks/..."/>
			<ForEach Name="TemplateName" Values="$(ProjectsToFeaturePack)">
				<PakFile Output="FeaturePacks/$(TemplateName).upack" Files="" ResponseFile="Templates/$(TemplateName)/contents.txt"/>
			</ForEach>
			<ForEach Name="SampleName" Values="MobileStarterContent;StarterContent">
				<PakFile Output="FeaturePacks/$(SampleName).upack" Files="" ResponseFile="Samples/$(SampleName)/contents.txt"/>
			</ForEach>
		</Node>
	</Agent>

	<Property Name="DDCPlatformsWin64" Value="Windows"/>
	<Property Name="DDCPlatformsWin64" Value="$(DDCPlatformsWin64)+WindowsNoEditor" If="'$(WithWin64)' == true Or '$(WithWin32)' == true"/>
	<Do If="'$(HostPlatformDDCOnly)' == false">
		<Property Name="DDCPlatformsWin64" Value="$(DDCPlatformsWin64)+Android_ATC" If="'$(WithAndroid)' == true"/>
		<Property Name="DDCPlatformsWin64" Value="$(DDCPlatformsWin64)+IOS" If="'$(WithIOS)' == true"/>
		<Property Name="DDCPlatformsWin64" Value="$(DDCPlatformsWin64)+TVOS" If="'$(WithTVOS)' == true"/>
		<Property Name="DDCPlatformsWin64" Value="$(DDCPlatformsWin64)+LinuxNoEditor" If="'$(WithLinux)' == true"/>
		<Property Name="DDCPlatformsWin64" Value="$(DDCPlatformsWin64)+LinuxAArch64NoEditor" If="'$(WithLinuxAArch64)' == true"/>
		<Property Name="DDCPlatformsWin64" Value="$(DDCPlatformsWin64)+Lumin" If="'$(WithLumin)' == true"/>
		<Property Name="DDCPlatformsWin64" Value="$(DDCPlatformsWin64)+HoloLens" If="'$(WithHoloLens)' == true"/>
		<ForEach Name="Platform" Values="$(ExtensionPlatforms)">
			<Expand Name="$(Platform)_Append_DDCPlatformsWin64"/>
		</ForEach>
	</Do>

	<Macro Name="GenerateEngineDirList" Arguments="InList">
		<Property Name="EngineDirList" Value=""/>
		<ForEach Name="EngineDir" Values="$(InList)">
			<Property Name="EngineDirList" Value="$(EngineDirList);" If="'$(EngineDirList)' != ''"/>
			<Property Name="EngineDirList" Value="$(EngineDirList)Engine\$(EngineDir)"/>
			<ForEach Name="ExtensionDir" Values="$(ExtensionPlatformDirs)">
				<Property Name="EngineDirList" Value="$(EngineDirList);Engine\Platforms\$(ExtensionDir)\$(EngineDir)"/>
			</ForEach>
		</ForEach>
	</Macro>

	<Macro Name="TagDDCEngineFiles" Arguments="ExceptPlugins">
		<Property Name="EngineDirList" Value=""/>
		<Expand Name="GenerateEngineDirList" InList="$(InstalledDDCEngineContent)"/>
		<Property Name="DDCCopyEngineContent" Value="$(EngineDirList)"/>
		<Expand Name="GenerateEngineDirList" InList="$(InstalledDDCEngineContentExcept)"/>
		<Property Name="DDCCopyEngineContentExcept" Value="$(EngineDirList)"/>
		<Expand Name="GenerateEngineDirList" InList="$(InstalledDDCEngineConfig)"/>
		<Property Name="DDCCopyEngineConfig" Value="$(EngineDirList)"/>
		<Expand Name="GenerateEngineDirList" InList="$(InstalledDDCEngineConfigExcept)"/>
		<Property Name="DDCCopyEngineConfigExcept" Value="$(EngineDirList)"/>
		<Expand Name="GenerateEngineDirList" InList="$(InstalledDDCEnginePlugins);$(ExceptPlugins)"/>
		<Property Name="DDCCopyEnginePlugins" Value="$(EngineDirList)"/>
		<Expand Name="GenerateEngineDirList" InList="$(InstalledDDCEnginePluginsExcept)"/>
		<Property Name="DDCCopyEnginePluginsExcept" Value="$(EngineDirList)"/>
		<Expand Name="GenerateEngineDirList" InList="$(InstalledDDCEngineShaders)"/>
		<Property Name="DDCCopyEngineShaders" Value="$(EngineDirList)"/>
		<Expand Name="GenerateEngineDirList" InList="$(InstalledDDCEngineShadersExcept)"/>
		<Property Name="DDCCopyEngineShadersExcept" Value="$(EngineDirList)"/>
		<ForEach Name="Platform" Values="$(ExtensionPlatforms)">
			<Expand Name="$(Platform)_TagDDCFiles"/>
		</ForEach>
		<Tag Files="$(DDCCopyEngineContent)" Except="$(DDCCopyEngineContentExcept)" With="#ToCopy"/>
		<Tag Files="$(DDCCopyEngineConfig)" Except="$(DDCCopyEngineConfigExcept)" With="#ToCopy"/>
		<Tag Files="$(DDCCopyEnginePlugins)" Except="$(DDCCopyEnginePluginsExcept)" With="#ToCopy"/>
		<Tag Files="$(DDCCopyEngineShaders)" Except="$(DDCCopyEngineShadersExcept)" With="#ToCopy"/>
	</Macro>

	<Agent Name="DDC Group Win64" Type="Win64_Licensee">
		<Node Name="Build DDC Win64" Requires="#UE4Editor Win64;Build Tools Win64;#Build Tools CS Binaries" Produces="#CompressedDDCWin64">
			<!-- Get our temp dir -->
			<Property Name="DDCDir" Value="$(RootDir)\LocalBuilds\InstalledDDC"/>
			<Property Name="SavedDir" Value="$(SavedOutput)\Installed\Win64"/>
			<!-- Delete all the old files -->
			<Delete Files="$(DDCDir)\..."/>
			<!-- Build up a list of files needed to build DDC -->
			<CsCompile Project="Engine/Source/Programs/UnrealBuildTool/UnrealBuildTool.csproj" Configuration="Development" Platform="AnyCPU" Tag="#ToCopy" TagReferences="#ToCopy" EnumerateOnly="true"/>
			<Tag Files="#Compile UnrealHeaderTool Win64;#UE4Editor Win64;#Build Tools Win64" With="#ToCopy"/>
			<Tag Files="#ToCopy" Filter="*.target" With="#TargetReceipts"/>
			<TagReceipt Files="#TargetReceipts" RuntimeDependencies="true" With="#ToCopy"/>
			<Tag Files="Engine\Binaries\DotNET\Ionic.Zip.Reduced.dll" With="#ToCopy"/>
			<Tag Files="Engine\Binaries\DotNET\OneSky.dll" With="#ToCopy"/>
			<Tag Files="Templates\TemplateResources\..." With="#ToCopy"/>
			<Expand Name="TagDDCEngineFiles" ExceptPlugins="Plugins\...\Binaries\Mac\..."/>
			<!-- Filter out the files not needed to build DDC. Removing confidential folders can affect DDC keys, so we want to be sure that we're making DDC with a build that can use it. -->
			<Tag Files="#ToCopy" Except="$(ConfidentialExceptions);$(RootDir)/.../Source/...;$(RootDir)/.../Intermediate/..." With="#FilteredCopyList"/>
			
			<!-- Copy everything to a temporary directory -->
			<Copy From="$(RootDir)" To="$(DDCDir)" Files="#FilteredCopyList"/>
			<Copy From="$(CsToolsDir)" To="$(DDCDir)" Files="#Build Tools CS Binaries"/>

			<Command Name="BuildDerivedDataCache" Arguments="-TempDir=&quot;$(DDCDir)&quot; -FeaturePacks=&quot;$(ProjectsToBuildDDCWin64)&quot; -TargetPlatforms=$(DDCPlatformsWin64) -HostPlatform=Win64 -SavedDir=&quot;$(SavedDir)&quot;"/>

			<Tag Files="$(SavedDir)/Engine/DerivedDataCache/Compressed.ddp" With="#CompressedDDCWin64"/>
		</Node>
	</Agent>

	<Property Name="DDCPlatformsMac" Value="Mac"/>
	<Property Name="DDCPlatformsMac" Value="$(DDCPlatformsMac)+MacNoEditor" If="'$(WithMac)' == true"/>
	<Do If="'$(HostPlatformDDCOnly)' == false">
		<Property Name="DDCPlatformsMac" Value="$(DDCPlatformsMac)+Android_ATC" If="'$(WithAndroid)' == true"/>
		<Property Name="DDCPlatformsMac" Value="$(DDCPlatformsMac)+IOS" If="'$(WithIOS)' == true"/>
		<Property Name="DDCPlatformsMac" Value="$(DDCPlatformsMac)+TVOS" If="'$(WithTVOS)' == true"/>
		<Property Name="DDCPlatformsMac" Value="$(DDCPlatformsMac)+Lumin" If="'$(WithLuminMac)' == true"/>
	</Do>

	<Agent Name="DDC Group Mac" Type="Mac">
		<Node Name="Build DDC Mac" Requires="#UE4Editor Mac;Build Tools Mac;#Build Tools CS Binaries" Produces="#CompressedDDCMac">
			<!-- Get our temp dir -->
			<Property Name="DDCDir" Value="$(RootDir)/LocalBuilds/InstalledDDC"/>
			<Property Name="SavedDir" Value="$(SavedOutput)/Installed/Mac"/>
			<!-- Delete all the old files -->
			<Delete Files="$(DDCDir)/..."/>
			<!-- Build up a list of files needed to build DDC -->
			<Tag Files="#Compile UnrealHeaderTool Mac;#UE4Editor Mac;#Build Tools Mac" With="#ToCopy"/>
			<Tag Files="#ToCopy" Filter="*.target" With="#TargetReceipts"/>
			<TagReceipt Files="#TargetReceipts" RuntimeDependencies="true" With="#ToCopy"/>
			<Tag Files="Engine/Binaries/DotNET/Ionic.Zip.Reduced.dll" With="#ToCopy"/>
			<Tag Files="Engine/Binaries/DotNET/OneSky.dll" With="#ToCopy"/>
			<Tag Files="Templates/TemplateResources/..." With="#ToCopy"/>
			<Expand Name="TagDDCEngineFiles" ExceptPlugins="Plugins/.../Binaries/Win64/...;Plugins/.../Binaries/Win32/..."/>
			<!-- Filter out the files not needed to build DDC. Removing confidential folders can affect DDC keys, so we want to be sure that we're making DDC with a build that can use it. -->
			<Tag Files="#ToCopy" Except="$(ConfidentialExceptions);$(RootDir)/.../Source/...;$(RootDir)/.../Intermediate/..." With="#FilteredCopyList"/>
			
			<!-- Copy everything to a temporary directory -->
			<Copy From="$(RootDir)" To="$(DDCDir)" Files="#FilteredCopyList"/>
			<Copy From="$(CsToolsDir)" To="$(DDCDir)" Files="#Build Tools CS Binaries"/>

			<Command Name="BuildDerivedDataCache" Arguments="-TempDir=&quot;$(DDCDir)&quot; -FeaturePacks=&quot;$(ProjectsToBuildDDC)&quot; -TargetPlatforms=$(DDCPlatformsMac) -HostPlatform=Mac -SavedDir=&quot;$(SavedDir)&quot;"/>

			<Tag Files="$(SavedDir)/Engine/DerivedDataCache/Compressed.ddp" With="#CompressedDDCMac"/>
		</Node>
	</Agent>

	<Property Name="DDCPlatformsLinux" Value="Linux"/>
	<Property Name="DDCPlatformsLinux" Value="$(DDCPlatformsLinux)+LinuxNoEditor" If="'$(WithLinux)' == true"/>
	<Do If="'$(HostPlatformDDCOnly)' == false">
		<Property Name="DDCPlatformsLinux" Value="$(DDCPlatformsLinux)+LinuxAArch64NoEditor" If="'$(WithLinuxAArch64)' == true"/>
	</Do>

	<Agent Name="DDC Group Linux" Type="Linux;Win64">
		<!-- Additionally require the Win64 editor binaries if building on win64 -->
		<Property Name="DDCLinuxRequires" Value="#UE4Editor Linux;Build Tools Linux;#Build Tools CS Binaries"/>
		<Property Name="DDCLinuxRequires" Value="$(DDCLinuxRequires);#UE4Editor Win64;#Build Tools Win64" If="'$(HostPlatform)' == 'Win64'"/>
		<Node Name="Build DDC Linux" Requires="$(DDCLinuxRequires)" Produces="#CompressedDDCLinux">
			<!-- Get our temp dir -->
			<Property Name="DDCDir" Value="$(RootDir)/LocalBuilds/InstalledDDC"/>
			<Property Name="SavedDir" Value="$(SavedOutput)/Installed/Linux"/>
			<!-- Delete all the old files -->
			<Delete Files="$(DDCDir)/..."/>
			<!-- Build up a list of files needed to build DDC -->
			<CsCompile Project="Engine/Source/Programs/UnrealBuildTool/UnrealBuildTool.csproj" Configuration="Development" Platform="AnyCPU" Tag="#ToCopy" TagReferences="#ToCopy" EnumerateOnly="true"/>
			<Tag Files="#Compile UnrealHeaderTool $(HostPlatform);#UE4Editor Linux;#Build Tools Linux" With="#ToCopy"/>
			<Tag Files="#UE4Editor Win64;#Build Tools Win64" With="#ToCopy" If="'$(HostPlatform)' == 'Win64'"/>
			<Tag Files="#ToCopy" Filter="*.target" With="#TargetReceipts"/>
			<TagReceipt Files="#TargetReceipts" RuntimeDependencies="true" With="#ToCopy"/>
			<Tag Files="Engine/Content/..." Except="*.psd;*.pdn;*.fbx;*.po" With="#ToCopy"/>
			<Tag Files="Engine/Config/..." Except="*.vdf" With="#ToCopy"/>
			<Tag Files="Engine/Plugins/....uplugin;Engine/Plugins/.../Config/...;Engine/Plugins/.../Content/...;Engine/Plugins/.../Resources/...;Engine/Plugins/.../Shaders/...;Engine/Plugins/.../Templates/..." Except="Engine/Plugins/Runtime/TwitchLiveStreaming/...;Engine/Plugins/.../Binaries/Win32/...;Engine/Plugins/.../Binaries/Mac/..." With="#ToCopy"/>
			<Tag Files="Engine/Shaders/..." With="#ToCopy"/>
			<Tag Files="Engine/Binaries/DotNET/Ionic.Zip.Reduced.dll" With="#ToCopy"/>
			<Tag Files="Engine/Binaries/DotNET/OneSky.dll" With="#ToCopy"/>
			<Tag Files="Templates/TemplateResources/..." With="#ToCopy"/>
			<Expand Name="TagDDCEngineFiles" ExceptPlugins="Plugins/.../Binaries/Win32/...;Plugins/.../Binaries/Mac/..."/>
			<!-- Filter out the files not needed to build DDC. Removing confidential folders can affect DDC keys, so we want to be sure that we're making DDC with a build that can use it. -->
			<Tag Files="#ToCopy" Except="$(ConfidentialExceptions);$(RootDir)/.../Source/...;$(RootDir)/.../Intermediate/..." With="#FilteredCopyList"/>
			
			<!-- Copy everything to a temporary directory -->
			<Copy From="$(RootDir)" To="$(DDCDir)" Files="#FilteredCopyList"/>
			<Copy From="$(CsToolsDir)" To="$(DDCDir)" Files="#Build Tools CS Binaries"/>

			<Command Name="BuildDerivedDataCache" Arguments="-TempDir=&quot;$(DDCDir)&quot; -FeaturePacks=$(ProjectsToBuildDDC) -TargetPlatforms=$(DDCPlatformsLinux) -SavedDir=&quot;$(SavedDir)&quot;"/>

			<Tag Files="$(SavedDir)/Engine/DerivedDataCache/Compressed.ddp" With="#CompressedDDCLinux"/>
		</Node>
	</Agent>

	<!-- These are plugins that need to be compiled with a full workspace rather than Win64_Licensee. -->
	<Agent Name="Extra Plugins Agent Win64" Type="Win64">
		<Node Name="Compile Datasmith Plugins Win64" Requires="Compile UnrealHeaderTool Win64" Produces="#DatasmithPluginBinaries Win64">
			<Do If="$(CompileDatasmithPlugins) == 'True'">
				<Property Name="DatasmithPlugins" Value="DatasmithCADImporter;DatasmithIFCImporter;DatasmithC4DImporter;AxFImporter;StaticMeshEditorExtension;MDLImporter"/>
				<ForEach Name="Plugin" Values="$(DatasmithPlugins)">
					<Compile Target="UE4Editor" Configuration="Development" Platform="Win64" Arguments="-Plugin=&quot;$(RootDir)/Engine/Plugins/Enterprise/$(Plugin)/$(Plugin).uplugin&quot;"/>
					<Tag Files="$(RootDir)/Engine/Plugins/Enterprise/$(Plugin)/Binaries/..." With="#DatasmithPluginBinaries Win64"/>
				</ForEach>
			</Do>
		</Node>
	</Agent>

	<Agent Name="Extra Plugins Agent Mac" Type="Mac">
		<Node Name="Compile Datasmith Plugins Mac" Requires="Compile UnrealHeaderTool Mac" Produces="#DatasmithPluginBinaries Mac">
			<Do If="$(CompileDatasmithPlugins) == 'True'">
				<Property Name="DatasmithPlugins" Value="DatasmithCADImporter;DatasmithIFCImporter;DatasmithC4DImporter;AxFImporter;StaticMeshEditorExtension;MDLImporter"/>
				<ForEach Name="Plugin" Values="$(DatasmithPlugins)">
					<Compile Target="UE4Editor" Configuration="Development" Platform="Mac" Arguments="-Plugin=&quot;$(RootDir)/Engine/Plugins/Enterprise/$(Plugin)/$(Plugin).uplugin&quot;"/>
					<Tag Files="$(RootDir)/Engine/Plugins/Enterprise/$(Plugin)/Binaries/..." With="#DatasmithPluginBinaries Mac"/>
				</ForEach>
			</Do>
		</Node>
	</Agent>

	<Agent Name="Installed Build Group Win64" Type="Win64_Licensee">
		<!-- Build up a list of requirements as some can be optional -->
		<Property Name="UE4Editor" Value="#UE4Editor Win64" />
		<Property Name="UE4Editor" Value="#UE4Editor Win64 WithExceptions" If="$(CompileDatasmithPlugins)" />
		<Property Name="InstalledRequirements" Value="Compile UnrealHeaderTool Win64;$(UE4Editor);#UE4Editor Win64 Unstripped;#UE4Editor Win64 Stripped;#UE4Editor Win64 Unsigned;#UE4Editor Win64 Signed;#Build Tools Win64;#Build Tools CS Binaries;#Rules Assemblies"/>
		<!-- Optional Target Platform requirements -->
		<ForEach Name="Platform" Values="$(ExtensionPlatforms)">
			<Expand Name="$(Platform)_AddInstalledRequirements"/>
		</ForEach>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game Win64;#UE4Game Win64 Unstripped;#UE4Game Win64 Stripped;#UE4Game Win64 Unsigned;#UE4Game Win64 Signed" If="'$(WithWin64)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game Win32;#UE4Game Win32 Unstripped;#UE4Game Win32 Stripped;#UE4Game Win32 Unsigned;#UE4Game Win32 Signed;#Build Tools Win32" If="'$(WithWin32)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game Android;#UE4Game Android Unstripped;#UE4Game Android Stripped" If="'$(WithAndroid)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game IOS;#UE4Game IOS Unstripped;#UE4Game IOS Stripped;#Compile UnrealHeaderTool Mac" If="'$(WithIOS)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game TVOS;#UE4Game TVOS Unstripped;#UE4Game TVOS Stripped" If="'$(WithTVOS)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game Linux;#UE4Game Linux Unstripped;#UE4Game Linux Stripped;#Build Tools Linux" If="'$(WithLinux)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game LinuxAArch64;#UE4Game LinuxAArch64 Unstripped;#UE4Game LinuxAArch64 Stripped" If="'$(WithLinuxAArch64)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game Lumin" If="'$(WithLumin)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game HoloLens" If="'$(WithHoloLens)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#CompressedDDCWin64" If="'$(WithDDC)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#Compile Datasmith Plugins Win64;#DatasmithPluginBinaries Win64" If="$(CompileDatasmithPlugins)"/>
		<!-- Feature packs -->
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);Make Feature Packs"/>

		<!-- Filters the build products required for an installed build into groups depending on whether they need to be stripped, signed or just copied -->
		<Node Name="Make Installed Build Win64" Requires="$(InstalledRequirements)" Produces="#Installed Build Win64 Files">

			<!-- Clear any existing files from the final location -->
			<Delete Files="$(LocalInstalledDir)\..."/>

			<!-- List of platform names -->
			<Property Name="Platforms" Value=""/>
			
			<!-- Define filter and exception properties for the installed build -->
			<Property Name="CopyInstalledFilter" Value="$(CopyEditorFilterWin64)"/>
			<Property Name="CopyInstalledExceptions" Value="$(CopyEditorExceptionsWin64)"/>

			<!-- Tag files that always need to be part of an installed build -->
			<Tag Files="#Compile UnrealHeaderTool Win64" With="#Installed Win64"/>
			<Tag Files="$(UE4Editor)" Except="#UE4Editor Win64 Unstripped;#UE4Editor Win64 Unsigned" With="#Installed Win64"/>
			<Tag Files="#UE4Editor Win64 Stripped;#UE4Editor Win64 Signed" With="#Saved Output"/>
			<Tag Files="#Build Tools Win64;#Make Feature Packs;#Rules Assemblies" With="#Installed Win64"/>
			<Tag Files="#DatasmithPluginBinaries Win64" With="#Installed Win64" If="$(CompileDatasmithPlugins)"/>

			<!-- Tag enabled target platforms -->
			<Do If="'$(WithWin64)' == true">
				<Tag Files="#UE4Game Win64" Except="#UE4Game Win64 Unstripped;#UE4Game Win64 Unsigned" With="#Installed Win64"/>
				<Tag Files="#UE4Game Win64 Stripped;#UE4Game Win64 Signed" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)Win64;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyWin64Filter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyWin64Exceptions)"/>
			</Do>
			<Do If="'$(WithWin32)' == true">
				<Tag Files="#UE4Game Win32;#Build Tools Win32" Except="#UE4Game Win32 Unstripped;#UE4Game Win32 Unsigned" With="#Installed Win64"/>
				<Tag Files="#UE4Game Win32 Stripped;#UE4Game Win32 Signed" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)Win32;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyWin32Filter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyWin32Exceptions)"/>
			</Do>
			<Do If="'$(WithAndroid)' == true">
				<Tag Files="#UE4Game Android" Except="#UE4Game Android Unstripped" With="#Installed Win64"/>
				<Tag Files="#UE4Game Android Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)Android;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyAndroidFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyAndroidExceptions)"/>
			</Do>
			<Do If="'$(WithIOS)' == true">
				<Tag Files="#UE4Game IOS;#Compile UnrealHeaderTool Mac" Except="#UE4Game IOS Unstripped" With="#Installed Win64"/>
				<Tag Files="#UE4Game IOS Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)IOS;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyIOSFilterWin64)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyIOSExceptions)"/>
			</Do>
			<Do If="'$(WithTVOS)' == true">
				<Tag Files="#UE4Game TVOS" Except="#UE4Game TVOS Unstripped" With="#Installed Win64"/>
				<Tag Files="#UE4Game TVOS Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)TVOS;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyTVOSFilterWin64)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyTVOSExceptions)"/>
			</Do>
			<Do If="'$(WithLinux)' == true">
				<Tag Files="#UE4Game Linux;#Build Tools Linux" Except="#UE4Game Linux Unstripped" With="#Installed Win64"/>
				<Tag Files="#UE4Game Linux Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)Linux;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyLinuxFilterWin64)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyLinuxExceptions)"/>
			</Do>
			<Do If="'$(WithLinuxAArch64)' == true">
				<Tag Files="#UE4Game LinuxAArch64" Except="#UE4Game LinuxAArch64 Unstripped" With="#Installed Win64"/>
				<Tag Files="#UE4Game LinuxAArch64 Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)LinuxAArch64;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyLinuxFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyLinuxExceptions)"/>
			</Do>
			<ForEach Name="Platform" Values="$(ExtensionPlatforms)">
				<Expand Name="$(Platform)_Tag_UE4Game"/>
			</ForEach>
			<Do If="'$(WithLumin)' == true">
				<Tag Files="#UE4Game Lumin" With="#Installed Win64"/>
				<Property Name="Platforms" Value="$(Platforms)Lumin;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyLuminFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyLuminExceptions)"/>
			</Do>
			<Do If="'$(WithHoloLens)' == true">
				<Tag Files="#UE4Game HoloLens;#UE4Game HoloLens Includes" With="#Installed Win64"/>
				<Property Name="Platforms" Value="$(Platforms)HoloLens;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyHoloLensFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyHoloLensExceptions)"/>
			</Do>
			<!-- Tag any dependencies from all previous build products -->
			<Tag Files="#Installed Win64" Filter="*.target" With="#TargetReceipts"/>
			<TagReceipt Files="#TargetReceipts" RuntimeDependencies="true" With="#Installed Win64"/>

			<!-- Tag any additional dependencies -->
			<Tag Files="#Installed Win64" Filter=".../DependencyList.txt;.../DependencyList-AllModules.txt" With="#DependencyLists"/>
			<Tag FileLists="#DependencyLists" With="#Installed Win64" />

			<!-- Confidential Exceptions - done last to ensure they never get overwritten -->
			<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);#DependencyLists;$(ConfidentialExceptions)"/>
			
			<!-- Tag additional files needed for installed build -->
			<Tag Files="$(CopyInstalledFilter)" With="#Installed Win64"/>

			<!-- Tag Win64 files that need to be stripped -->
			<Tag Files="#Compile UnrealHeaderTool Win64;#Build Tools Win64" Filter="$(Win64StripFilter)" Except="$(Win64StripExceptions);$(CopyInstalledExceptions)" With="#Strip Tools for Installed Win64"/>
			<Tag Files="#Build Tools Win32" Filter="$(Win32StripFilter)" Except="$(CopyInstalledExceptions)" With="#Strip Tools for Installed Win64" If="$(WithWin32)"/>

			<Do If="'$(SignExecutables)' == true">
				<!-- Tag files that need to be signed -->
				<Tag Files="#Compile UnrealHeaderTool Win64;#Build Tools Win64" Filter="$(WindowsSignFilter)" Except="$(CopyInstalledExceptions);$(SignExceptions)" With="#Sign Tools for Installed Win64"/>
				<Tag Files="#Build Tools Win32" Filter="$(WindowsSignFilter)" Except="$(CopyInstalledExceptions);$(SignExceptions)" With="#Sign Tools for Installed Win64" If="$(WithWin32)"/>
			</Do>

			<!-- Tag files that need to be copied directly -->
			<Tag Files="#Installed Win64" Except="$(CopyInstalledExceptions);#Strip Tools for Installed Win64;#Sign Tools for Installed Win64" With="#Copy for Installed Win64"/>
			<Copy Files="#Copy for Installed Win64" From="$(RootDir)" To="$(LocalInstalledDir)" Tag="#Installed Build Win64 Files"/>
			<Copy Files="#Build Tools CS Binaries" From="$(CsToolsDir)" To="$(LocalInstalledDir)" Tag="#Installed Build Win64 Files"/>
			
			<!-- Tag saved output that needs to be copied directly -->
			<Tag BaseDir="$(SavedOutput)" Files="#Saved Output" Except="$(CopyInstalledExceptions)" With="#Copy Saved Output"/>
			<Copy Files="#Copy Saved Output" From="$(SavedOutput)" To="$(LocalInstalledDir)" Tag="#Installed Build Win64 Files"/>

			<!-- Strip required files into their final location -->
			<Strip Files="#Strip Tools for Installed Win64" BaseDir="$(RootDir)" OutputDir="$(LocalInstalledDir)" Platform="Win64" Tag="#Installed Build Win64 Files"/>

			<Do If="'$(SignExecutables)' == true">
				<!-- Copy then Sign required files in their final location -->
				<Copy Files="#Sign Tools for Installed Win64" From="$(RootDir)" To="$(LocalInstalledDir)" Tag="#Files to Sign"/>
				<Sign Files="#Files to Sign" Tag="#Installed Build Win64 Files"/>
			</Do>

			<!-- Copy the compressed DDC -->
			<Property Name="SavedDir" Value="$(SavedOutput)\Installed\Win64"/>
			<Copy Files="#CompressedDDCWin64" From="$(SavedDir)" To="$(LocalInstalledDir)" If="'$(WithDDC)' == true" Tag="#Installed Build Win64 Files"/>

			<!-- List all of the platforms that we only allow for content projects -->
			<Property Name="ContentOnlyPlatforms" Value=""/>

			<!-- Create command line arguments for any platforms that were built with additional architectures -->
			<Property Name="ArchitectureCommands" Value=""/>
			<Property Name="ArchitectureCommands" Value="$(ArchitectureCommands) -AndroidArchitectures=$(AndroidArchitectures)" If="'$(WithAndroid)' == true"/>
			<Property Name="ArchitectureCommands" Value="$(ArchitectureCommands) -HoloLensArchitectures=$(HoloLensArchitectures)" If="'$(WithHoloLens)' == true"/>

			<!-- Finalize the build and then tag the text file that this process creates -->
			<Command Name="FinalizeInstalledBuild" Arguments="-OutputDir=&quot;$(LocalInstalledDir)&quot; -Platforms=$(Platforms) -ContentOnlyPlatforms=$(ContentOnlyPlatforms) $(ArchitectureCommands) -AnalyticsTypeOverride=$(AnalyticsTypeOverride)"/>
			<Tag Files="$(LocalInstalledDir)/Engine/Build/InstalledBuild.txt" With="#Installed Build Win64 Files"/>
			
			<!-- Sanitize all the receipts in the target directory -->
			<Tag Files="#Installed Build Win64 Files" Filter="*.target;" With="#Installed Build Win64 Receipts"/>
			<SanitizeReceipt Files="#Installed Build Win64 Receipts" EngineDir="$(LocalInstalledDir)\Engine"/>
			<Log Message="SanitizeReceipt End..."/>
			<Do If="$(EmbedSymStore)">
				<!-- Storage symbol file to server. Should be done from this machine to ensure that paths are correct. -->
				<Log Message="Embedding Storage Symbol files to server..."/>
				<Tag Files="$(LocalInstalledDir)/..." Filter="*.pdb;*.exe;*.dll" Except="$(LocalInstalledDir)/Engine/Source/...;$(LocalInstalledDir)/Engine/Intermediate/...;$(LocalInstalledDir)/Engine/Extras/..." With="#SymStoreFiles"/>
				<SymStore Platform="Win64" Files="#SymStoreFiles" StoreDir="\\CreatorSamba\XverseCreator\XCreatorSymbols\EngineSymbol" Product="XVerseStudio"/>
			</Do>
			<Do If="$(EmbedRemoveSymbol)">
				<Tag Files="$(LocalInstalledDir)/..." Filter="*.pdb" Except="" With="#DeletePDBFiles"/>
				<Log Message="Start Delete PDB Files..."/>
				<Delete Files="#DeletePDBFiles"/>
			</Do>
			<Do If="$(EmbedZipOutPut)">
				<Log Message="Start Zip Output Files..."/>
				<Zip FromDir="$(LocalInstalledDir)" ZipFile="$(EngineOutPutZipPath)"/>
			</Do>
		</Node>
	</Agent>
	<Aggregate Name="HostPlatforms_Win64" Label="Builds/Win64" Requires="Make Installed Build Win64" Exclude="Update Version Files"/>

	<Agent Name="Installed Build Group Mac" Type="Mac_Licensee">
		<!-- Build up a list of requirements as some can be optional -->
		<Property Name="UE4Editor" Value="#UE4Editor Mac" />
		<Property Name="UE4Editor" Value="#UE4Editor Mac WithExceptions" If="$(CompileDatasmithPlugins)" />
		<Property Name="FilterRequirements" Value="#Compile UnrealHeaderTool Mac;$(UE4Editor);#UE4Editor Mac Unstripped;#UE4Editor Mac Stripped;#UE4Editor Mac Unsigned;#UE4Editor Mac Signed;#Build Tools Mac;#Build Tools CS Binaries;#Rules Assemblies"/>

		<!-- Optional Target Platform requirements -->
		<Property Name="FilterRequirements" Value="$(FilterRequirements);#UE4Game Mac;#UE4Game Mac Unstripped;#UE4Game Mac Stripped;#UE4Game Mac Unsigned;#UE4Game Mac Signed" If="'$(WithMac)' == true"/>
		<Property Name="FilterRequirements" Value="$(FilterRequirements);#UE4Game Android;#UE4Game Android Unstripped;#UE4Game Android Stripped" If="'$(WithAndroid)' == true"/>
		<Property Name="FilterRequirements" Value="$(FilterRequirements);#UE4Game IOS;#UE4Game IOS Unstripped;#UE4Game IOS Stripped" If="'$(WithIOS)' == true"/>
		<Property Name="FilterRequirements" Value="$(FilterRequirements);#UE4Game TVOS;#UE4Game TVOS Unstripped;#UE4Game TVOS Stripped" If="'$(WithTVOS)' == true"/>
		<Property Name="FilterRequirements" Value="$(FilterRequirements);#UE4Game LuminMac;#UE4Game LuminMac Unstripped;#UE4Game LuminMac Stripped" If="'$(WithLuminMac)' == true"/>
		<Property Name="FilterRequirements" Value="$(FilterRequirements);#CompressedDDCMac" If="'$(WithDDC)' == true"/>
		<Property Name="FilterRequirements" Value="$(FilterRequirements);#Compile Datasmith Plugins Mac;#DatasmithPluginBinaries Mac" If="$(CompileDatasmithPlugins)"/>

		<!-- Feature packs -->
		<Property Name="FilterRequirements" Value="$(FilterRequirements);Make Feature Packs"/>

		<!-- Filters the build products required for an installed build into groups depending on whether they need to be stripped, signed or just copied-->
		<Node Name="Make Installed Build Mac" Requires="$(FilterRequirements)" Produces="#Installed Build Mac Files">
			<!-- Clear any existing files from the final location -->
			<Delete Files="$(LocalInstalledDirMac)/..."/>

			<!-- List of platform names -->
			<Property Name="Platforms" Value=""/>
			
			<!-- Define filter and exception properties for the installed build -->
			<Property Name="CopyInstalledFilter" Value="$(CopyEditorFilterMac)"/>
			<Property Name="CopyInstalledExceptions" Value="$(CopyEditorExceptionsMac)"/>

			<!-- Tag files that always need to be part of an installed build -->
			<Tag Files="#Compile UnrealHeaderTool Mac" With="#Installed Mac"/>
			<Tag Files="$(UE4Editor)" Except="#UE4Editor Mac Unstripped;#UE4Editor Mac Unsigned" With="#Installed Mac"/>
			<Tag Files="#UE4Editor Mac Stripped;#UE4Editor Mac Signed" With="#Saved Output"/>
			<Tag Files="#Build Tools Mac;#Make Feature Packs;#Rules Assemblies" With="#Installed Mac"/>
			<Tag Files="#DatasmithPluginBinaries Mac" With="#Installed Mac" If="$(CompileDatasmithPlugins)"/>

			<!-- Tag enabled target platforms -->
			<Do If="'$(WithMac)' == true">
				<Tag Files="#UE4Game Mac" Except="#UE4Game Mac Unstripped;#UE4Game Mac Unsigned" With="#Installed Mac"/>
				<Tag Files="#UE4Game Mac Stripped;#UE4Game Mac Signed" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)Mac;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyMacFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyMacExceptions)"/>
			</Do>
			<Do  If="'$(WithAndroid)' == true">
				<Tag Files="#UE4Game Android" Except="#UE4Game Android Unstripped" With="#Installed Mac"/>
				<Tag Files="#UE4Game Android Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)Android;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyAndroidFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyAndroidExceptions)"/>
			</Do>
			<Do If="'$(WithIOS)' == true">
				<Tag Files="#UE4Game IOS" Except="#UE4Game IOS Unstripped" With="#Installed Mac"/>
				<Tag Files="#UE4Game IOS Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)IOS;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyIOSFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyIOSExceptions)"/>
			</Do>
			<Do If="'$(WithTVOS)' == true">
				<Tag Files="#UE4Game TVOS" Except="#UE4Game TVOS Unstripped" With="#Installed Mac"/>
				<Tag Files="#UE4Game TVOS Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)TVOS;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyTVOSFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyTVOSExceptions)"/>
			</Do>
			<Do If="'$(WithLuminMac)' == true">
				<Tag Files="#UE4Game LuminMac" Except="#UE4Game LuminMac Unstripped" With="#Installed Mac"/>
				<Tag Files="#UE4Game LuminMac Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)Lumin;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyLuminFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyLuminExceptions)"/>
			</Do>

			<!-- Tag any dependencies from all previous build products -->
			<Tag Files="#Installed Mac" Filter="*.target" With="#TargetReceipts"/>
			<TagReceipt Files="#TargetReceipts" RuntimeDependencies="true" With="#Installed Mac"/>

			<!-- Tag any additional dependencies -->
			<Tag Files="#Installed Mac" Filter=".../DependencyList.txt;.../DependencyList-AllModules.txt" With="#DependencyLists"/>
			<Tag FileLists="#DependencyLists" With="#Installed Mac" />

			<!-- Confidential Exceptions - done last to ensure they never get overwritten -->
			<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(ConfidentialExceptions);#DependencyLists"/>
			
			<!-- Tag additional files needed for editor and platforms -->
			<Tag Files="$(CopyInstalledFilter)" With="#Installed Mac"/>

			<!-- Tag Mac files that need to be stripped -->
			<Tag Files="#Compile UnrealHeaderTool Mac;#Build Tools Mac" Filter="$(MacStripFilter)" Except="$(CopyInstalledExceptions)" With="#Strip Tools for Installed Mac"/>

			<Do If="'$(SignExecutables)' == true">
				<!-- Tag files that need to be signed -->
				<Tag Files="#Compile UnrealHeaderTool Mac;#Build Tools Mac" Filter="$(MacSignFilter)" Except="$(CopyInstalledExceptions);$(SignExceptions)" With="#Sign Tools for Installed Mac"/>
			</Do>

			<!-- Tag files that need to be copied directly -->
			<Tag Files="#Installed Mac" Except="$(CopyInstalledExceptions);#Strip Tools for Installed Mac;#Sign Tools for Installed Mac" With="#Copy for Installed Mac"/>
			<Copy Files="#Copy for Installed Mac" From="$(RootDir)" To="$(LocalInstalledDirMac)" Tag="#Installed Build Mac Files"/>
			<Copy Files="#Build Tools CS Binaries" From="$(CsToolsDir)" To="$(LocalInstalledDirMac)" Tag="#Installed Build Mac Files"/>
			
			<!-- Tag saved output that needs to be copied directly -->
			<Tag BaseDir="$(SavedOutput)" Files="#Saved Output" Except="$(CopyInstalledExceptions)" With="#Copy Saved Output"/>
			<Copy Files="#Copy Saved Output" From="$(SavedOutput)" To="$(LocalInstalledDirMac)" Tag="#Installed Build Mac Files"/>

			<!-- Strip required files into their final location -->
			<Strip Files="#Strip Tools for Installed Mac" BaseDir="$(RootDir)" OutputDir="$(LocalInstalledDirMac)" Platform="Mac" Tag="#Installed Build Mac Files"/>

			<Do If="'$(SignExecutables)' == true">
				<!-- Copy then Sign required files in their final location -->
				<Copy Files="#Sign Tools for Installed Mac" From="$(RootDir)" To="$(LocalInstalledDirMac)" Tag="#Files to Sign"/>
				<Sign Files="#Files to Sign" Tag="#Installed Build Mac Files"/>
			</Do>

			<!-- Copy the compressed DDC -->
			<Property Name="SavedDir" Value="$(SavedOutput)/Installed/Mac"/>
			<Copy Files="#CompressedDDCMac" From="$(SavedDir)" To="$(LocalInstalledDirMac)" If="'$(WithDDC)' == true" Tag="#Installed Build Mac Files"/>

			<!-- Create command line arguments for any platforms that were built with additional architectures -->
			<Property Name="ArchitectureCommands" Value=""/>
			<Property Name="ArchitectureCommands" Value="$(ArchitectureCommands) -AndroidArchitectures=$(AndroidArchitectures)" If="'$(WithAndroid)' == true"/>
			<!-- Finalize the build and then tag the text file that this process creates -->
			<Command Name="FinalizeInstalledBuild" Arguments="-OutputDir=$(LocalInstalledDirMac) -Platforms=$(Platforms) $(ArchitectureCommands) -AnalyticsTypeOverride=$(AnalyticsTypeOverride)"/>
			<Tag Files="$(LocalInstalledDirMac)/Engine/Build/InstalledBuild.txt" With="#Installed Build Mac Files"/>

			<!-- Sanitize all the receipts in the target directory -->
			<Tag Files="#Installed Build Mac Files" Filter="*.target" With="#Installed Build Mac Receipts"/>
			<SanitizeReceipt Files="#Installed Build Mac Receipts" EngineDir="$(LocalInstalledDirMac)/Engine"/>
		</Node>
	</Agent>
	<Aggregate Name="HostPlatforms_Mac" Label="Builds/Mac" Requires="Make Installed Build Mac"/>

	<Agent Name="Installed Build Group Linux" Type="Win64_Licensee">
		<!-- Build up a list of requirements as some can be optional -->
		<Property Name="InstalledRequirements" Value="#Compile UnrealHeaderTool Linux;#UE4Editor Linux;#UE4Editor Linux Unstripped;#UE4Editor Linux Stripped;#Build Tools Linux;#Build Tools CS Binaries;#Rules Assemblies"/>

		<!-- Optional Target Platform requirements -->
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game Linux;#UE4Game Linux Unstripped;#UE4Game Linux Stripped" If="'$(WithLinux)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UE4Game LinuxAArch64;#UE4Game LinuxAArch64 Unstripped;#UE4Game LinuxAArch64 Stripped" If="'$(WithLinuxAArch64)' == true"/>
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#CompressedDDCLinux" If="'$(WithDDC)' == true"/>

		<!-- Feature packs -->
		<Property Name="InstalledRequirements" Value="$(InstalledRequirements);Make Feature Packs"/>

		<!-- Filters the build products required for an installed build into groups depending on whether they need to be stripped or just copied -->
		<Node Name="Make Installed Build Linux" Requires="$(InstalledRequirements)" Produces="#Installed Build Linux Files">

			<!-- Clear any existing files from the final location -->
			<Delete Files="$(LocalInstalledDirLinux)\..."/>

			<!-- List of platform names -->
			<Property Name="Platforms" Value=""/>

			<!-- Define filter and exception properties for the installed build -->
			<Property Name="CopyInstalledFilter" Value="$(CopyEditorFilterLinux)"/>
			<Property Name="CopyInstalledExceptions" Value="$(CopyEditorExceptionsLinux)"/>

			<!-- Tag files that always need to be part of an installed build -->
			<Tag Files="#Compile UnrealHeaderTool Linux" With="#Installed Linux"/>
			<Tag Files="#UE4Editor Linux" Except="#UE4Editor Linux Unstripped" With="#Installed Linux"/>
			<Tag Files="#UE4Editor Linux Stripped" With="#Saved Output"/>
			<Tag Files="#Build Tools Linux;#Make Feature Packs;#Rules Assemblies" With="#Installed Linux"/>

			<!-- Tag enabled target platforms -->
			<Do If="'$(WithLinux)' == true">
				<Tag Files="#UE4Game Linux;#Build Tools Linux" Except="#UE4Game Linux Unstripped" With="#Installed Linux"/>
				<Tag Files="#UE4Game Linux Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)Linux;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyLinuxFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyLinuxExceptions)"/>
			</Do>
			<Do If="'$(WithLinuxAArch64)' == true">
				<Tag Files="#UE4Game LinuxAArch64" Except="#UE4Game LinuxAArch64 Unstripped" With="#Installed Linux"/>
				<Tag Files="#UE4Game LinuxAArch64 Stripped" With="#Saved Output"/>
				<Property Name="Platforms" Value="$(Platforms)LinuxAArch64;"/>
				<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyLinuxFilter)"/>
				<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyLinuxExceptions)"/>
			</Do>

			<!-- Tag any dependencies from all previous build products -->
			<Tag Files="#Installed Linux" Filter="*.target" With="#TargetReceipts"/>
			<TagReceipt Files="#TargetReceipts" RuntimeDependencies="true" With="#Installed Linux"/>

			<!-- Tag any additional dependencies -->
			<Tag Files="#Installed Linux" Filter=".../DependencyList.txt;.../DependencyList-AllModules.txt" With="#DependencyLists"/>
			<Tag FileLists="#DependencyLists" With="#Installed Linux" />

			<!-- Confidential Exceptions - done last to ensure they never get overwritten -->
			<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(ConfidentialExceptions);#DependencyLists"/>

			<!-- Tag additional files needed for installed build -->
			<Tag Files="$(CopyInstalledFilter)" With="#Installed Linux"/>

			<!-- Tag Linux files that need to be stripped -->
			<Tag Files="#Compile UnrealHeaderTool Linux;#Build Tools Linux" Filter="$(LinuxStripFilter)" Except="$(CopyInstalledExceptions)" With="#Strip Tools for Installed Linux"/>

			<!-- Tag files that need to be copied directly -->
			<Tag Files="#Installed Linux" Except="$(CopyInstalledExceptions);#Strip Tools for Installed Linux" With="#Copy for Installed Linux"/>
			<Copy Files="#Copy for Installed Linux" From="$(RootDir)" To="$(LocalInstalledDirLinux)" Tag="#Installed Build Linux Files"/>

			<!-- Tag saved output that needs to be copied directly -->
			<Tag BaseDir="$(SavedOutput)" Files="#Saved Output" Except="$(CopyInstalledExceptions)" With="#Copy Saved Output"/>
			<Copy Files="#Copy Saved Output" From="$(SavedOutput)" To="$(LocalInstalledDirLinux)" Tag="#Installed Build Linux Files"/>
			<Copy Files="#Build Tools CS Binaries" From="$(CsToolsDir)" To="$(LocalInstalledDirLinux)" Tag="#Installed Build Linux Files"/>
			
			<!-- Strip required files into their final location -->
			<Strip Files="#Strip Tools for Installed Linux" BaseDir="$(RootDir)" OutputDir="$(LocalInstalledDirLinux)" Platform="Linux" Tag="#Installed Build Linux Files"/>

			<!-- Copy the compressed DDC -->
			<Property Name="SavedDir" Value="$(SavedOutput)\Installed\Linux"/>
			<Copy Files="#CompressedDDCLinux" From="$(SavedDir)" To="$(LocalInstalledDirLinux)" If="'$(WithDDC)' == true" Tag="#Installed Build Linux Files"/>

			<!-- Copy the toolchain if it exists -->
			<Property Name="LinuxToolchainDir" Value="$(ToolchainDir)/HostLinux"/>
			<Copy Files="$(LinuxToolchainDir)/..." From="$(LinuxToolchainDir)" To="$(LocalInstalledDirLinux)/Engine/Extras/ThirdPartyNotUE/SDKs/HostLinux" If="Exists('$(LinuxToolchainDir)')" Tag="#Installed Build Linux Files"/>

			<!-- List all of the platforms that we only allow for content projects -->
			<Property Name="ContentOnlyPlatforms" Value=""/>

			<!-- Create command line arguments for any platforms that were built with additional architectures -->
			<Property Name="ArchitectureCommands" Value=""/>
			<Property Name="ArchitectureCommands" Value="$(ArchitectureCommands) -AndroidArchitectures=$(AndroidArchitectures)" If="'$(WithAndroid)' == true"/>

			<!-- Finalize the build and then tag the text file that this process creates -->
			<Command Name="FinalizeInstalledBuild" Arguments="-OutputDir=$(LocalInstalledDirLinux) -Platforms=$(Platforms) -ContentOnlyPlatforms=$(ContentOnlyPlatforms) $(ArchitectureCommands) -AnalyticsTypeOverride=$(AnalyticsTypeOverride)"/>
			<Tag Files="$(LocalInstalledDirLinux)/Engine/Build/InstalledBuild.txt" With="#Installed Build Linux Files"/>

			<!-- Sanitize all the receipts in the target directory -->
			<Tag Files="#Installed Build Linux Files" Filter="*.target" With="#Installed Build Linux Files"/>
			<SanitizeReceipt Files="#Installed Build Linux Receipts" EngineDir="$(LocalInstalledDirLinux)/Engine"/>
		</Node>
	</Agent>
	<Aggregate Name="HostPlatforms_Linux" Label="Builds/Linux" Requires="Make Installed Build Linux"/>

</BuildGraph>
