#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import os
#from datetime import datetime
import datetime
import shutil



LogTag = "LookUpExpireData"
def DeleteDirAndZip(EngineOutPutPath, f):
    filepath = os.path.join(EngineOutPutPath, "{0}.zip".format(f))
    dirpath = os.path.join(EngineOutPutPath, "{0}".format(f))
    if os.path.exists(filepath):
        print("DeleteDirAndZip Delete Zip", filepath)
        #os.remove(filepath)
    try:
        print("DeleteDirAndZip Delete Dir", dirpath)
        # shutil.rmtree(dirpath)
    except:
        print("ClearEngineOutPut Delete Dir error", dirpath)
def LookUpEngineOutPut(EngineOutPutPath):
    print("{0} LookUpExpireData OutPutPath={1}".format(LogTag, EngineOutPutPath))
    if not os.path.exists(EngineOutPutPath):
        print("Path not exists", EngineOutPutPath)
        return
    file_list = os.listdir(EngineOutPutPath)
    file_list.sort(reverse=True)
    expireTime = datetime.datetime.now() + datetime.timedelta(days=-2)
    branchMap = {}
    date_time = expireTime.strftime("%Y%m%d")
    print("{0} LookUpExpireData expireTime={1}".format(LogTag, date_time))
    engineMap={}         
    for f in file_list:    
        fpath = os.path.join(EngineOutPutPath, f)
        FileNameArr = f.split("-")
        if len(FileNameArr) > 1:
            FileDate = FileNameArr[1]
        if os.path.isdir(fpath) and f.startswith("XVerseEngine-") and FileDate < date_time:
            #print("{0} Delete EngineOutput={1}".format(LogTag, fpath))
            print("{0} LookUpExpireData FileDate={1} {2}".format(LogTag, FileDate, f))
            engineInstalledPath = os.path.join(fpath, "Engine\Version\EngineInstalled.ini")
            if not os.path.exists(engineInstalledPath):
                print("LookUpExpireData engine unavailable",engineInstalledPath)
                engineMap[f] = "no EngineInstalled"
                DeleteDirAndZip(EngineOutPutPath, f)
                continue
            lockPath = os.path.join(fpath, "Engine\Version\EngineLock.ini")
            if os.path.exists(lockPath):
                print("LookUpExpireData engine has locked unavailable",lockPath)
                engineMap[f] = "has EngineLock"
                continue
            uatPath = os.path.join(fpath, "Engine\Build\BatchFiles\RunUAT.bat")
            if not os.path.exists(uatPath):
                print("LookUpExpireData engine not exist RunUAT", f, uatPath)
                engineMap[f] = "no RunUAT"
                continue
            engineBranch = ReadEngineBranch(fpath)
            if len(engineBranch) < 1:
                print("LookUpExpireData engine not exist Branch Info", engineBranch, f, fpath)
                engineMap[f] = "no branchinfo"
                continue
            if not branchMap.get(engineBranch):
                branchMap[engineBranch] = f
                print("LookUpExpireData insert new branch",engineBranch, f, fpath)
                engineMap[f] = "new avaliable engine=" + engineBranch
            else:
                print("LookUpExpireData delete duplix branch engine", engineBranch, f, fpath)
                engineMap[f] = "dupix engine=" + engineBranch
    for engine in engineMap:
        print("Total Engine", engine, engineMap[engine])
    print("LookUpEngineOutPut End")
def ReadEngineBranch(EnginePath):
    engineVersionPath = os.path.join(EnginePath, "Engine\Version\EngineVersion.ini")
    engineVersionFile = open(engineVersionPath, "r" , encoding="utf-8")
    fileBranch = ""
    for line in engineVersionFile:
        if line.startswith("Branch"):
            #print("ReadEngineBranch", engineVersionPath, line)
            lines = line.split(":")
            if len(lines) > 1:
                fileBranch = lines[1]
                fileBranch = fileBranch.strip()
    return fileBranch
if __name__ == "__main__":
    EngineOutPutPath = "C:\\work\\xverse\\EngineOutput"
    if len(sys.argv) > 1:
        EngineOutPutPath = sys.argv[1]
    LookUpEngineOutPut(EngineOutPutPath)