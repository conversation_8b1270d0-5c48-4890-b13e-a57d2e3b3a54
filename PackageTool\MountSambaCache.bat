net use   \\**********\SkinCache "ddcache" /user:"ddcache"  /PERSISTENT:YES >nul
goto :eof
if exist \\**********\SkinCache (
    echo "Skin Samba Cache Already Mount"
) else (
    echo "Mount Skin Samba Cache"
    net use   \\**********\SkinCache "ddcache" /user:"ddcache"  /PERSISTENT:YES >nul
)

net use   \\**********\CreatorProxyTasks "ddcache" /user:"ddcache"  /PERSISTENT:YES >nul
goto :eof
if exist \\**********\CreatorProxyTasks (
    echo "Proxy Tasks Samba Cache Already Mount"
) else (
    echo "Mount Proxy Tasks Samba Cache"
    net use   \\**********\CreatorProxyTasks "ddcache" /user:"ddcache"  /PERSISTENT:YES >nul
)

net use   \\**********\CIPackage "ddcache" /user:"ddcache"  /PERSISTENT:YES >nul
goto :eof
if exist \\**********\CIPackage (
    echo "CIPackage Samba Cache Already Mount"
) else (
    echo "Mount CIPackage Tasks Samba Cache"
    net use   \\**********\CIPackage "ddcache" /user:"ddcache"  /PERSISTENT:YES >nul
)