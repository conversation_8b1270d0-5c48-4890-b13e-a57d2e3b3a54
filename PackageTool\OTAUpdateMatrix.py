import typer
from typing_extensions import Annotated
from otaUploader import OTAStorage
from uploadNotify import retryHttp, getStoreIdMap
from rich import  print


def main(
    app_id: Annotated[str, typer.Argument(help="console应用id")],
    apk_path: Annotated[str, typer.Argument(help="apk文件cos路径", rich_help_panel="OTA存储参数")],
    main_pak_path: Annotated[str, typer.Argument(help="主pak文件cos路径", rich_help_panel="OTA存储参数")],
    video_path: Annotated[str, typer.Argument(help="全景视频文件cos路径", rich_help_panel="OTA存储参数")],
    phy_scene_pak_path: Annotated[str, typer.Argument(help="物理场景pak文件cos路径", rich_help_panel="OTA存储参数")],
    version_name: Annotated[str, typer.Argument(help="版本名称", rich_help_panel="OTA版本参数")],
    version_desc: Annotated[str, typer.Argument(help="版本描述", rich_help_panel="OTA版本参数")],
    target_store: Annotated[str, typer.Argument(help="目标门店", rich_help_panel="OTA版本参数")] = "",
):
    ota = OTAStorage()
    apk_exists, _ = ota.check_object_exists(apk_path)
    main_pak_exists, _ = ota.check_object_exists(main_pak_path)
    video_exists, _ = ota.check_object_exists(video_path)
    phy_scene_pak_exists, _ = ota.check_object_exists(phy_scene_pak_path)
    err_msg = "ota版本创建失败:\n"
    if not apk_exists:
        err_msg += f"\tAPK文件{apk_path}在cos桶中不存在\n"
    if not main_pak_exists:
        err_msg += f"\tMainPak文件{main_pak_path}在cos桶中不存在\n"
    if not video_exists:
        err_msg += f"\tVideo文件{video_path}在cos桶中不存在\n"
    if not phy_scene_pak_exists:
        err_msg += f"\tPhyScenePak文件{phy_scene_pak_path}在cos桶中不存在\n"
    if err_msg != "ota版本创建失败:\n":
        print(err_msg)
        raise typer.Exit(code=1)

    store_id_map = getStoreIdMap()
    dst_store_id = store_id_map.get(target_store, "")  # 应该不可能为None
    print(f"{target_store}->{dst_store_id}")
    release_data = {
        "appId": app_id,
        "version": version_name,
        "desc": version_desc,
        "apkPath": apk_path,
        "pakPath": main_pak_path,
        "videoPath": video_path,
        "targetStoreId": dst_store_id,
        "extraFileList": [
            {
                "path": phy_scene_pak_path,
                "targetPath": "mountPak"
            }
        ]
    }
    release_ret, msg = retryHttp(url="https://lbvr-api.xverse.cn/ota/auto_create_xverse_prd_version", data=release_data, mode='post')
    if not release_ret:
        err_msg += f"\t{msg}"
        print(err_msg)
        raise typer.Exit(code=1)
    print("OTA版本创建成功")
    
if __name__ == "__main__":
    typer.run(main)