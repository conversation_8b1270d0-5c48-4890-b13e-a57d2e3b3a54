<?xml version='1.0' ?>
<BuildGraph xmlns="http://www.epicgames.com/BuildGraph" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.epicgames.com/BuildGraph ../../Engine/Build/Graph/Schema.xsd" >
	<!-- Whether to embed storage symbol file to server -->
	<Option Name="EmbedSymStore" Restrict="true|false" DefaultValue="true" Description="Whether to add symbol files(e.g: *.pdb,*.exe,*.dll) to server"/>
		<!-- Whether to remove symbol file -->
	<Option Name="EmbedRemoveSymbol" Restrict="true|false" DefaultValue="false" Description="Whether to embed Remove Symbol"/>
		<!-- The local output directory -->
	<Option Name="XProjectDir" DefaultValue="" Description="XProjectDir"/>
	
	<Option Name="XProjectName" DefaultValue="XStudio" Description="XProjectName"/>
	
	<Option Name="XPlatform" DefaultValue="" Description="Platform;e.g:Win64,Linux"/>

	<Agent Name="Installed Build Group Win64" Type="Win64_Licensee">

		<!-- Filters the build products required for an installed build into groups depending on whether they need to be stripped, signed or just copied -->
		<Node Name="Make Installed Build Win64" Produces="#Installed Build Win64 Files">
			<Log Message="Embedding Storage Symbol files to server...EmbedSymStore=$(EmbedSymStore),EmbedRemoveSymbol=$(EmbedRemoveSymbol)"/>
			<Log Message="Embedding Storage Symbol files to server...XProjectDir=$(XProjectDir),XProjectName=$(XProjectName),XPlatform=$(XPlatform)"/>
			<Do If="$(EmbedSymStore)">
				<!-- Storage symbol file to server. Should be done from this machine to ensure that paths are correct. -->
				<Log Message="Embedding Storage Symbol files to server Inging..."/>
				<Tag Files="$(XProjectDir)/..." Filter="*.pdb;*.debug;" Except="UE4Editor-*" With="#SymStoreFiles"/>
				<SymStore Platform="Win64" Files="#SymStoreFiles" StoreDir="\\CreatorSamba\XverseCreator\XCreatorSymbols\ProjectSymbol" Product="$(XProjectName)"/>
			</Do>
			<Do If="$(EmbedRemoveSymbol)">
				<Tag Files="$(XProjectDir)/..." Filter="*.pdb;*.debug" Except="" With="#DeletePDBFiles"/>
				<Log Message="Start Delete PDB Files..."/>
				<Delete Files="#DeletePDBFiles"/>
			</Do>
		</Node>
	</Agent>
	<Aggregate Name="HostPlatforms_Win64" Label="Builds/Win64" Requires="Make Installed Build Win64" />



</BuildGraph>
