{"FileVersion": 3, "Version": 1, "VersionName": "1.7.0", "FriendlyName": "KawaiiPhysics", "Description": "", "Category": "Animation", "CreatedBy": "pafuhana1213", "CreatedByURL": "https://twitter.com/pafuhana1213", "DocsURL": "https://github.com/pafuhana1213/KawaiiPhysics", "MarketplaceURL": "KawaiiPhysics : Simple fake Physics for UnrealEngine4", "SupportURL": "https://github.com/pafuhana1213/KawaiiPhysics/issues", "EnabledByDefault": true, "CanContainContent": false, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "Modules": [{"Name": "KawaiiPhysics", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}, {"Name": "KawaiiPhysicsEd", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON>efault"}]}