﻿<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20" fill="none">
<g  clip-path="url(#clip-path-EAoZTludkVqP93_2Xdcah)">
<g filter="url(#filter_LeYwxNAmLN55yB7NArvIv)">
<ellipse cx="9.374999046325684" cy="9.629626274108887" rx="9.374999046325684" ry="9.629626274108887"   fill="url(#linear_fill_LeYwxNAmLN55yB7NArvIv)" >
</ellipse>
</g>
<g filter="url(#filter_gWjPUS1u2VXB8phJnsT_R)">
<ellipse cx="14.42307710647583" cy="17.777775764465332" rx="2.163461208343506" ry="2.222222328186035"   fill="url(#linear_fill_gWjPUS1u2VXB8phJnsT_R)" >
</ellipse>
</g>
<g filter="url(#filter_746zXa5Uv9foZpeItthQS)">
<path   fill-rule="evenodd" style="mix-blend-mode:overlay" fill="url(#linear_fill_746zXa5Uv9foZpeItthQS_0)"  d="M6.64498 14.199C6.64796 14.3231 6.64498 14.4472 6.65094 14.5713C6.65987 14.765 6.74919 14.9042 6.93379 14.9738C7.11541 15.0435 7.25535 14.9799 7.38636 14.8467C7.7764 14.4442 8.16942 14.0477 8.55946 13.6452C8.82147 13.3758 8.74108 13.0217 8.38677 12.8976C8.19026 12.831 7.9997 12.7463 7.81808 12.6434C6.99632 12.1773 6.43061 11.4933 6.11798 10.5884C6.03759 10.3553 5.91552 10.2555 5.69519 10.2524C5.588 10.2433 5.49272 10.2918 5.40936 10.3735C5.00145 10.7881 4.59355 11.2058 4.18862 11.6235C4.06357 11.7506 4.01891 11.9019 4.08739 12.0744C4.15587 12.253 4.28687 12.3438 4.47147 12.3529C4.59652 12.3589 4.72455 12.3589 4.85258 12.3589C5.061 12.3619 5.06695 12.368 5.00145 12.5677C4.85258 13.0247 4.70073 13.4818 4.55484 13.9388C4.4804 14.1748 4.54888 14.3655 4.73646 14.4745C4.86151 14.5471 4.99252 14.5289 5.12055 14.4835C5.55823 14.3383 5.99591 14.1869 6.43359 14.0386C6.63903 13.969 6.63903 13.9721 6.64498 14.199ZM13.6287 3.12598C12.4586 3.10782 11.4462 3.97343 11.4165 5.32932C11.3926 6.46125 12.396 7.48422 13.5185 7.4933C14.7095 7.50238 15.6742 6.53691 15.6891 5.31721C15.7039 4.14897 14.7541 3.14414 13.6287 3.12598ZM11.1337 15.4105C11.9703 14.8536 12.8129 14.3027 13.6228 13.7065C14.3761 13.1526 14.7036 12.3718 14.6083 11.4154C14.5666 11.0068 14.5339 10.5952 14.4922 10.1836C14.4803 10.0595 14.5101 9.96266 14.6053 9.87187C15.7903 8.76415 16.6359 7.42339 17.2135 5.90104C17.6959 4.62686 17.9847 3.31333 18.0264 1.94533C18.0383 1.67294 18.0055 1.40358 17.9638 1.13724C17.9222 0.867878 17.9103 0.855772 17.6423 0.807348C17.148 0.713525 16.6508 0.731684 16.1566 0.783135C15.177 0.886038 14.2242 1.11 13.2982 1.452C11.6577 2.05429 10.2077 2.9562 9.03757 4.29091C8.96909 4.36658 8.90359 4.37868 8.80831 4.3696C8.30215 4.3121 7.79897 4.2213 7.29281 4.22735C6.41149 4.23341 5.72371 4.64502 5.24435 5.40771C4.98531 5.82537 4.72628 6.24606 4.46724 6.6607C4.2231 7.05113 3.97597 7.43247 3.74076 7.82592C3.40729 8.38281 3.72289 9.05167 4.34517 9.1001C5.0925 9.1576 5.83983 9.203 6.58716 9.24537C6.72412 9.25143 6.78069 9.29077 6.79558 9.43605C6.83726 9.92332 6.96529 10.3834 7.22135 10.804C7.71858 11.6152 8.44209 12.0631 9.36806 12.1872C9.71642 12.2356 9.70451 12.2386 9.73131 12.6048C9.77895 13.3403 9.83254 14.0727 9.90102 14.8051C9.96057 15.4437 10.6067 15.7615 11.1337 15.4105Z">
</path>
<path   fill-rule="evenodd"  fill="#FFFFFF" fill-opacity="0.4" d="M6.64498 14.199C6.64796 14.3231 6.64498 14.4472 6.65094 14.5713C6.65987 14.765 6.74919 14.9042 6.93379 14.9738C7.11541 15.0435 7.25535 14.9799 7.38636 14.8467C7.7764 14.4442 8.16942 14.0477 8.55946 13.6452C8.82147 13.3758 8.74108 13.0217 8.38677 12.8976C8.19026 12.831 7.9997 12.7463 7.81808 12.6434C6.99632 12.1773 6.43061 11.4933 6.11798 10.5884C6.03759 10.3553 5.91552 10.2555 5.69519 10.2524C5.588 10.2433 5.49272 10.2918 5.40936 10.3735C5.00145 10.7881 4.59355 11.2058 4.18862 11.6235C4.06357 11.7506 4.01891 11.9019 4.08739 12.0744C4.15587 12.253 4.28687 12.3438 4.47147 12.3529C4.59652 12.3589 4.72455 12.3589 4.85258 12.3589C5.061 12.3619 5.06695 12.368 5.00145 12.5677C4.85258 13.0247 4.70073 13.4818 4.55484 13.9388C4.4804 14.1748 4.54888 14.3655 4.73646 14.4745C4.86151 14.5471 4.99252 14.5289 5.12055 14.4835C5.55823 14.3383 5.99591 14.1869 6.43359 14.0386C6.63903 13.969 6.63903 13.9721 6.64498 14.199ZM13.6287 3.12598C12.4586 3.10782 11.4462 3.97343 11.4165 5.32932C11.3926 6.46125 12.396 7.48422 13.5185 7.4933C14.7095 7.50238 15.6742 6.53691 15.6891 5.31721C15.7039 4.14897 14.7541 3.14414 13.6287 3.12598ZM11.1337 15.4105C11.9703 14.8536 12.8129 14.3027 13.6228 13.7065C14.3761 13.1526 14.7036 12.3718 14.6083 11.4154C14.5666 11.0068 14.5339 10.5952 14.4922 10.1836C14.4803 10.0595 14.5101 9.96266 14.6053 9.87187C15.7903 8.76415 16.6359 7.42339 17.2135 5.90104C17.6959 4.62686 17.9847 3.31333 18.0264 1.94533C18.0383 1.67294 18.0055 1.40358 17.9638 1.13724C17.9222 0.867878 17.9103 0.855772 17.6423 0.807348C17.148 0.713525 16.6508 0.731684 16.1566 0.783135C15.177 0.886038 14.2242 1.11 13.2982 1.452C11.6577 2.05429 10.2077 2.9562 9.03757 4.29091C8.96909 4.36658 8.90359 4.37868 8.80831 4.3696C8.30215 4.3121 7.79897 4.2213 7.29281 4.22735C6.41149 4.23341 5.72371 4.64502 5.24435 5.40771C4.98531 5.82537 4.72628 6.24606 4.46724 6.6607C4.2231 7.05113 3.97597 7.43247 3.74076 7.82592C3.40729 8.38281 3.72289 9.05167 4.34517 9.1001C5.0925 9.1576 5.83983 9.203 6.58716 9.24537C6.72412 9.25143 6.78069 9.29077 6.79558 9.43605C6.83726 9.92332 6.96529 10.3834 7.22135 10.804C7.71858 11.6152 8.44209 12.0631 9.36806 12.1872C9.71642 12.2356 9.70451 12.2386 9.73131 12.6048C9.77895 13.3403 9.83254 14.0727 9.90102 14.8051C9.96057 15.4437 10.6067 15.7615 11.1337 15.4105Z">
</path>
<path fill-rule="evenodd"  fill="url(#linear_border_746zXa5Uv9foZpeItthQS_0)"  d="M13.6228 13.7065C12.8129 14.3027 11.9703 14.8536 11.1337 15.4105C10.6067 15.7615 9.96057 15.4437 9.90102 14.8051C9.83254 14.0727 9.77895 13.3403 9.73131 12.6048C9.70451 12.2386 9.71642 12.2356 9.36806 12.1872C8.44209 12.0631 7.71858 11.6152 7.22135 10.804C6.96529 10.3834 6.83726 9.92332 6.79558 9.43605C6.78069 9.29077 6.72412 9.25143 6.58716 9.24537C5.83983 9.203 5.0925 9.1576 4.34517 9.1001C3.72289 9.05167 3.40729 8.38281 3.74076 7.82592C3.97597 7.43247 4.2231 7.05113 4.46724 6.6607C4.72628 6.24606 4.98531 5.82537 5.24435 5.40771C5.72371 4.64502 6.41149 4.23341 7.29281 4.22735C7.66875 4.22286 8.04305 4.27179 8.41815 4.32083C8.54807 4.33781 8.67809 4.35481 8.80831 4.3696C8.90359 4.37868 8.96909 4.36658 9.03757 4.29091C10.2077 2.9562 11.6577 2.05429 13.2982 1.452C14.2242 1.11 15.177 0.886038 16.1566 0.783135C16.6508 0.731684 17.148 0.713525 17.6423 0.807348C17.9103 0.855772 17.9222 0.867878 17.9638 1.13724C18.0055 1.40358 18.0383 1.67294 18.0264 1.94533C17.9847 3.31333 17.6959 4.62686 17.2135 5.90104C16.6359 7.42339 15.7903 8.76415 14.6053 9.87187C14.5101 9.96266 14.4803 10.0595 14.4922 10.1836C14.5339 10.5952 14.5666 11.0068 14.6083 11.4154C14.7036 12.3718 14.3761 13.1526 13.6228 13.7065ZM11.3518 15.0318Q11.1346 15.176 11.0258 15.2484L11.0257 15.2484Q10.7238 15.4496 10.4279 15.3042Q10.1294 15.1575 10.0948 14.787Q10.0066 13.8432 9.92546 12.5906Q9.92457 12.5785 9.92287 12.5548Q9.9 12.2372 9.80785 12.1387Q9.71446 12.0388 9.39392 11.9942Q8.07058 11.8169 7.38764 10.7028Q7.05218 10.1517 6.98924 9.4162Q6.95343 9.0667 6.59818 9.05101Q5.25491 8.97485 4.36027 8.90601Q4.00639 8.87847 3.8605 8.57037Q3.71098 8.25458 3.90785 7.92581Q4.06931 7.65573 4.4056 7.12381Q4.55681 6.88464 4.63265 6.76337L5.15081 5.92856L5.40917 5.5113Q6.0886 4.4303 7.29514 4.42202Q7.65723 4.41769 8.39291 4.51386Q8.65448 4.54805 8.78984 4.5634Q9.03258 4.58653 9.18396 4.41925Q10.7991 2.57686 13.3657 1.63462Q14.7375 1.12795 16.1767 0.976764Q17.0255 0.888409 17.6077 0.998919Q17.7474 1.02417 17.7485 1.02535Q17.7497 1.02657 17.7715 1.16734Q17.845 1.63687 17.8318 1.9394Q17.7728 3.87365 17.0315 5.83198Q16.1464 8.16484 14.471 9.73094Q14.2714 9.92115 14.2984 10.2019L14.4146 11.4347Q14.549 12.7839 13.5074 13.5497Q12.8064 14.0658 11.3518 15.0318ZM15.2282 3.65684Q14.559 2.94629 13.6318 2.93133L13.6317 2.93133Q12.6492 2.91608 11.9607 3.57136Q11.2454 4.25218 11.2218 5.32505Q11.2023 6.25406 11.8982 6.96824Q12.5922 7.68049 13.5169 7.68797Q14.4922 7.6954 15.1826 7.00357Q15.8716 6.3132 15.8837 5.31959Q15.8959 4.36573 15.2282 3.65684ZM11.4165 5.32932C11.4462 3.97343 12.4586 3.10782 13.6287 3.12598C14.7541 3.14414 15.7039 4.14897 15.6891 5.31721C15.6742 6.53691 14.7095 7.50238 13.5185 7.4933C12.396 7.48422 11.3926 6.46125 11.4165 5.32932ZM5.69519 10.2524C5.91552 10.2555 6.03759 10.3553 6.11798 10.5884C6.43061 11.4933 6.99632 12.1773 7.81808 12.6434C7.9997 12.7463 8.19026 12.831 8.38677 12.8976C8.74108 13.0217 8.82147 13.3758 8.55946 13.6452C8.16942 14.0477 7.7764 14.4442 7.38636 14.8467C7.25535 14.9799 7.11541 15.0435 6.93379 14.9738C6.74919 14.9042 6.65987 14.765 6.65094 14.5713C6.64498 14.4472 6.64796 14.3231 6.64498 14.199L6.64497 14.1988C6.63903 13.9721 6.63895 13.9691 6.43359 14.0386C5.99591 14.1869 5.55823 14.3383 5.12055 14.4835C4.99252 14.5289 4.86151 14.5471 4.73646 14.4745C4.54888 14.3655 4.4804 14.1748 4.55484 13.9388C4.70073 13.4818 4.85258 13.0247 5.00145 12.5677C5.06695 12.368 5.061 12.3619 4.85258 12.3589C4.72455 12.3589 4.59652 12.3589 4.47147 12.3529C4.28687 12.3438 4.15587 12.253 4.08739 12.0744C4.01891 11.9019 4.06357 11.7506 4.18862 11.6235C4.59355 11.2058 5.00145 10.7881 5.40936 10.3735C5.49272 10.2918 5.588 10.2433 5.69519 10.2524ZM6.84132 14.347Q6.84084 14.2459 6.8396 14.1944L6.83959 14.1939L6.83958 14.1937Q6.83268 13.9304 6.73297 13.8535Q6.62327 13.7688 6.37086 13.8543L5.93342 14.0032L5.05549 14.3001Q4.91162 14.3511 4.83424 14.3061Q4.67271 14.2123 4.73998 13.9989L5.03687 13.0855L5.18643 12.6284Q5.2663 12.3849 5.18768 12.2757Q5.11001 12.1679 4.85258 12.1642Q4.60124 12.1642 4.48103 12.1584Q4.32516 12.1507 4.26833 12.0026Q4.21669 11.8725 4.32814 11.7592L4.92471 11.1461L5.54563 10.5125Q5.6183 10.4413 5.67876 10.4464L5.68562 10.447L5.69251 10.4471Q5.86412 10.4494 5.93398 10.6519Q6.42664 12.078 7.72212 12.8128Q8.00838 12.975 8.32242 13.0814Q8.48655 13.1388 8.5145 13.2605Q8.54269 13.3832 8.42016 13.5092L7.83344 14.1101L7.24758 14.7102Q7.12226 14.8376 7.00248 14.7917Q6.85339 14.7355 6.84539 14.562Q6.84202 14.4917 6.84132 14.347Z">
</path>
</g>
</g>
<defs>
<clipPath id="clip-path-EAoZTludkVqP93_2Xdcah">
<path d="M0 20L20 20L20 0L0 0L0 20Z" fill="white"/>
</clipPath>
<radialGradient id="linear_fill_LeYwxNAmLN55yB7NArvIv" cx="0" cy="0" r="1" gradientTransform="translate(14.050498570709228 0) rotate(-180) scale(15.063816973114013, 15.063816973114013)" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#3499F8"  />
<stop offset="1" stop-color="#0B76E0"  />
</radialGradient>
<filter id="filter_LeYwxNAmLN55yB7NArvIv" x="-3.8900000000000006" y="-2.92" width="26.52999809265137" height="27.039254455566407" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_LeYwxNAmLN55yB7NArvIv"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_LeYwxNAmLN55yB7NArvIv"/>
<feOffset dx="0" dy="0.97"/>
<feGaussianBlur stdDeviation="1.945"/>
<feComposite in2="hardAlpha_LeYwxNAmLN55yB7NArvIv" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.1450980392156863 0 0 0 0 0.5529411764705883 0 0 0 0 0.9372549019607843 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="feFloodId_LeYwxNAmLN55yB7NArvIv" result="dropShadow_1_LeYwxNAmLN55yB7NArvIv"/>
<feBlend mode="normal" in="SourceGraphic" in2="dropShadow_1_LeYwxNAmLN55yB7NArvIv" result="shape_LeYwxNAmLN55yB7NArvIv"/>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_LeYwxNAmLN55yB7NArvIv" result="shape_LeYwxNAmLN55yB7NArvIv"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_LeYwxNAmLN55yB7NArvIv"/>
<feOffset dx="0" dy="0.49"/>
<feGaussianBlur stdDeviation="0.245"/>
<feComposite in2="hardAlpha_LeYwxNAmLN55yB7NArvIv" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.32941176470588235 0 0 0 0 0.6588235294117647 0 0 0 0 0.9686274509803922 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_LeYwxNAmLN55yB7NArvIv" result="innerShadow_0_LeYwxNAmLN55yB7NArvIv" />
</filter>
<radialGradient id="linear_fill_gWjPUS1u2VXB8phJnsT_R" cx="0" cy="0" r="1" gradientTransform="translate(15.502038480300904 15.555553436279297) rotate(-180) scale(3.476266832427978, 3.476266832427978)" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#3499F8"  />
<stop offset="1" stop-color="#0B76E0"  />
</radialGradient>
<filter id="filter_gWjPUS1u2VXB8phJnsT_R" x="8.369615898132324" y="12.635553436279295" width="12.106922416687013" height="12.224442749023439" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_gWjPUS1u2VXB8phJnsT_R"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_gWjPUS1u2VXB8phJnsT_R"/>
<feOffset dx="0" dy="0.97"/>
<feGaussianBlur stdDeviation="1.945"/>
<feComposite in2="hardAlpha_gWjPUS1u2VXB8phJnsT_R" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.1450980392156863 0 0 0 0 0.5529411764705883 0 0 0 0 0.9372549019607843 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="feFloodId_gWjPUS1u2VXB8phJnsT_R" result="dropShadow_1_gWjPUS1u2VXB8phJnsT_R"/>
<feBlend mode="normal" in="SourceGraphic" in2="dropShadow_1_gWjPUS1u2VXB8phJnsT_R" result="shape_gWjPUS1u2VXB8phJnsT_R"/>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_gWjPUS1u2VXB8phJnsT_R" result="shape_gWjPUS1u2VXB8phJnsT_R"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_gWjPUS1u2VXB8phJnsT_R"/>
<feOffset dx="0" dy="0.49"/>
<feGaussianBlur stdDeviation="0.245"/>
<feComposite in2="hardAlpha_gWjPUS1u2VXB8phJnsT_R" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.32941176470588235 0 0 0 0 0.6588235294117647 0 0 0 0 0.9686274509803922 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_gWjPUS1u2VXB8phJnsT_R" result="innerShadow_0_gWjPUS1u2VXB8phJnsT_R" />
</filter>
<radialGradient id="linear_fill_746zXa5Uv9foZpeItthQS_0" cx="0" cy="0" r="1" gradientTransform="translate(10.81730842590332 15.555534362792969) rotate(0) scale(12.66665096282959, 7.407398223876953)" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#FFFFFF"  />
<stop offset="1" stop-color="#FFFFFF" stop-opacity="0.01" />
</radialGradient>
<linearGradient id="linear_border_746zXa5Uv9foZpeItthQS_0" x1="4.033990859985352" y1="1.3580732345581055" x2="17.60062599182129" y2="15.55555534362793" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#EBF1FF"  />
<stop offset="0.7297" stop-color="#9BBFFA"  />
<stop offset="1" stop-color="#97C1F7"  />
</linearGradient>
<filter id="filter_746zXa5Uv9foZpeItthQS" x="-0.48271478652954114" y="-2.2064751815795898" width="22.410997180938722" height="22.828007011413575" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_746zXa5Uv9foZpeItthQS"/>
<feGaussianBlur in="feFloodId_746zXa5Uv9foZpeItthQS" stdDeviation_bg="2" type="BackgroundImage" />
<feComposite in2="SourceAlpha" operator="in" result="background_Blur_746zXa5Uv9foZpeItthQS"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_746zXa5Uv9foZpeItthQS"/>
<feOffset dx="0" dy="0.97"/>
<feGaussianBlur stdDeviation="1.945"/>
<feComposite in2="hardAlpha_746zXa5Uv9foZpeItthQS" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.1568627450980392 0 0 0 0 0.5568627450980392 0 0 0 0 0.9372549019607843 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="feFloodId_746zXa5Uv9foZpeItthQS" result="dropShadow_1_746zXa5Uv9foZpeItthQS"/>
<feBlend mode="normal" in="SourceGraphic" in2="background_Blur_746zXa5Uv9foZpeItthQS" result="shape_746zXa5Uv9foZpeItthQS"/>
<feBlend mode="normal" in="SourceGraphic" in2="dropShadow_1_746zXa5Uv9foZpeItthQS" result="shape_746zXa5Uv9foZpeItthQS"/>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_746zXa5Uv9foZpeItthQS" result="shape_746zXa5Uv9foZpeItthQS"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_746zXa5Uv9foZpeItthQS"/>
<feOffset dx="0" dy="0.49"/>
<feGaussianBlur stdDeviation="0.485"/>
<feComposite in2="hardAlpha_746zXa5Uv9foZpeItthQS" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.8313725490196079 0 0 0 0 0.9450980392156862 0 0 0 0 0.9882352941176471 0 0 0 0.53 0"/>
<feBlend mode="normal" in2="shape_746zXa5Uv9foZpeItthQS" result="innerShadow_0_746zXa5Uv9foZpeItthQS" />
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_746zXa5Uv9foZpeItthQS"/>
<feOffset dx="0" dy="-0.49"/>
<feGaussianBlur stdDeviation="0.485"/>
<feComposite in2="hardAlpha_746zXa5Uv9foZpeItthQS" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.6588235294117647 0 0 0 0 0.8431372549019608 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="innerShadow_0_746zXa5Uv9foZpeItthQS" result="innerShadow_1_746zXa5Uv9foZpeItthQS" />
</filter>
</defs>
</svg>
