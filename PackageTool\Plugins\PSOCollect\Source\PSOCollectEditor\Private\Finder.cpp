﻿// Copyright Xverse. All Rights Reserved.


#include "Finder.h"

#include "CustomMoveComponent.h"
#include "JsonObjectConverter.h"
#include "LevelSequence.h"
#include "LevelSequenceActor.h"
#include "MaterialDomain.h"
#include "NiagaraActor.h"
#include "UnrealEd.h"

#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "PSOLightController.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Components/RectLightComponent.h"
#include "Engine/AssetManager.h"
#include "Engine/RectLight.h"
#include "Engine/SkinnedAssetCommon.h"
#include "Engine/StaticMeshActor.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Sections/MovieScenePrimitiveMaterialSection.h"

DEFINE_LOG_CATEGORY(LogPSOCollect);

float UFinder::OpacityValues[3] = {0.0, 0.5, 1.0};
float UFinder::BaseStep = 100.0f;

UStaticMesh* UFinder::StaticMeshTemplate = nullptr;
USkeletalMesh* UFinder::SkeletalMeshTemplate = nullptr;

TArray<UStaticMesh*> UFinder::CubeMeshes{};
UMaterialInterface* UFinder::WorldGridMaterial = nullptr;

void PrintFVertexDeclarationElementList(const FVertexDeclarationElementList& ElementList)
{
	UE_LOG(LogPSOCollect, Log, TEXT("============================"));
	for (int i = 0; i < ElementList.Num(); i++)
	{
		UE_LOG(LogPSOCollect, Log, TEXT("ElementList[%d]: %s"), i, *ElementList[i].ToString());
	}
	UE_LOG(LogPSOCollect, Log, TEXT("============================"));
}

void UFinder::ScanLevels()
{
	StaticMeshTemplate = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cube.Cube"));
	StaticMeshTemplate->InitResources();

	SkeletalMeshTemplate = LoadObject<USkeletalMesh>(nullptr, TEXT("/Engine/EngineMeshes/SkeletalCube.SkeletalCube"));
	SkeletalMeshTemplate->InitResources();

	WorldGridMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/EngineMaterials/WorldGridMaterial.WorldGridMaterial"));

	CubeMeshes.Empty();
	const FString PSOCubeDirectory = "/PSOCollect";
	LoadStaticMeshInDirectory(PSOCubeDirectory, CubeMeshes);

	const FString PSOMapFolderPath = FPaths::ProjectContentDir() / "PSOCollect";
	if (!IFileManager::Get().DirectoryExists(*PSOMapFolderPath))
	{
		IFileManager::Get().MakeDirectory(*PSOMapFolderPath);
	}

	const TArray<FString> DirectoryPaths
	{
		"/Game",
		"/XVerseEngine",
		"/Engine/EngineMaterials"
	};
	FPSOCollectInfo PSOCollectInfo;
	FindAllPSOAssetInDirectory(DirectoryPaths, PSOCollectInfo);

	const FString PSOMapName = "/Game/PSOCollect/PSOWorld";
	CreatePSOMap(PSOMapName, PSOCollectInfo);
}

void UFinder::FindAllPSOAssetInMap(const TArray<FString>& MapFolders, FPSOCollectInfo& PSOCollectInfo)
{
	TArray<FString> MapPaths;
	for (FString MapFolder : MapFolders)
	{
		FindAllMaps(MapFolder, MapPaths);
	}

	for (const auto& MapPath : MapPaths)
	{
		UWorld* World = UEditorLoadingAndSavingUtils::LoadMap(MapPath);

		FindAllMaterialAssetPaths(World, PSOCollectInfo);

		for (TActorIterator<AActor> It(World); It; ++It)
		{
			AActor* Actor = *It;

			if (const auto NiagaraActor = Cast<ANiagaraActor>(Actor))
			{
				const auto NiagaraComponent = NiagaraActor->GetNiagaraComponent();
				if (NiagaraComponent && NiagaraComponent->GetAsset())
				{
					FString AssetPath = NiagaraComponent->GetAsset()->GetPathName();
					PSOCollectInfo.NiagaraAssetPaths.Add(AssetPath);
				}
			}
			else if (const auto Emitter = Cast<AEmitter>(Actor))
			{
				const auto ParticleSystemComponent = Emitter->GetParticleSystemComponent();
				if (ParticleSystemComponent && ParticleSystemComponent->Template)
				{
					FString AssetPath = ParticleSystemComponent->Template->GetPathName();
					PSOCollectInfo.EmitterAssetPaths.Add(AssetPath);
				}
			}
			else if (const auto StaticMeshActor = Cast<AStaticMeshActor>(Actor))
			{
				const auto StaticMeshComponent = StaticMeshActor->GetStaticMeshComponent();
				if (StaticMeshComponent && StaticMeshComponent->GetStaticMesh())
				{
					const auto& Materials = StaticMeshComponent->GetStaticMesh()->GetStaticMaterials();
					for (auto& Material : Materials)
					{
						const auto& MaterialInterface = Material.MaterialInterface;
						if (MaterialInterface != nullptr)
						{
							AddStaticMeshMaterialAssetPath(PSOCollectInfo.StaticMeshMaterialAssetPaths, MaterialInterface.Get());
						}
						else
						{
							UE_LOG(LogPSOCollect, Log, TEXT("MaterialInterface == nullptr, Actor is :%s "), *Actor->GetPathName());
						}
					}
				}
			}
			else if (const auto SkeletalMeshActor = Cast<ASkeletalMeshActor>(Actor))
			{
				const auto SkeletalMeshComponent = SkeletalMeshActor->GetSkeletalMeshComponent();
				if (SkeletalMeshComponent && SkeletalMeshComponent->GetSkeletalMeshAsset())
				{
					const auto& Materials = SkeletalMeshComponent->GetSkeletalMeshAsset()->GetMaterials();
					for (auto& Material : Materials)
					{
						const auto& MaterialInterface = Material.MaterialInterface;
						if (MaterialInterface != nullptr)
						{
							PSOCollectInfo.SkeletalMeshMaterialAssetPaths.Add(FSoftObjectPath{MaterialInterface}.ToString());
						}
						else
						{
							UE_LOG(LogPSOCollect, Log, TEXT("MaterialInterface == nullptr, Actor is :%s "), *Actor->GetPathName());
						}
					}
				}
			}
		}
	}
}

void UFinder::FindAllPSOAssetInDirectory(const TArray<FString>& DirectoryPaths, FPSOCollectInfo& PSOCollectInfo)
{
	const auto& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
	const IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

	FARFilter Filter;
	Filter.bRecursivePaths = true;
	Filter.bRecursiveClasses = true;
	for (auto& DirectoryPath : DirectoryPaths)
	{
		Filter.PackagePaths.Add(*DirectoryPath);
	}

	Filter.ClassPaths.Add(UMaterial::StaticClass()->GetClassPathName());
	Filter.ClassPaths.Add(UMaterialInstanceConstant::StaticClass()->GetClassPathName());
	Filter.ClassPaths.Add(UNiagaraSystem::StaticClass()->GetClassPathName());
	Filter.ClassPaths.Add(UParticleSystem::StaticClass()->GetClassPathName());
	Filter.ClassPaths.Add(UStaticMesh::StaticClass()->GetClassPathName());
	Filter.ClassPaths.Add(USkeletalMesh::StaticClass()->GetClassPathName());

	TArray<FAssetData> AssetDataList;
	AssetRegistry.GetAssets(Filter, AssetDataList);

	for (const auto& AssetData : AssetDataList)
	{
		CollectPathFromAsset(AssetData, PSOCollectInfo);
	}
}

void UFinder::CreatePSOMap(const FString& PSOMapName, const FPSOCollectInfo& PSOCollectInfo)
{
	UWorld* World = UEditorLoadingAndSavingUtils::NewBlankMap(true);

	TArray<AActor*> PastedActors;
	CreateOriginalStaticMeshActorFromPath(World, PSOCollectInfo.StaticMeshPaths, PastedActors);
	CreateOriginalSkeletalMeshActorFromPath(World, PSOCollectInfo.SkeletalMeshPaths, PastedActors);

	CreateStaticMeshActorFromPath(World, PSOCollectInfo.StaticMeshMaterialAssetPaths, PastedActors);
	CreateSkeletalMeshActorFromPath(World, PSOCollectInfo.SkeletalMeshMaterialAssetPaths, PastedActors);

	CreateNiagaraActorsFromPath(World, PSOCollectInfo.NiagaraAssetPaths, PastedActors);
	CreateEmitterActorsFromPath(World, PSOCollectInfo.EmitterAssetPaths, PastedActors);
	SortActors(PastedActors, 0.0f);

	const float SqrtNum = FMath::Sqrt(static_cast<float>(PastedActors.Num()));
	const float FullWidth = SqrtNum * BaseStep;
	const float HalfWidth = FullWidth * 0.5f;
	const FVector Location{HalfWidth, HalfWidth, HalfWidth};

	const auto Pawn = World->SpawnActor<APawn>();
	USceneComponent* SceneComponent = NewObject<USceneComponent>(Pawn);
	SceneComponent->SetRelativeLocation(FVector{Location.X, Location.Y * 2, Location.Z});
	SceneComponent->SetRelativeRotation(FRotator(0, -90, 0));
	Pawn->SetRootComponent(SceneComponent);
	Pawn->AutoPossessPlayer = EAutoReceiveInput::Type::Player0;

	const auto SkyLight = World->SpawnActor<ASkyLight>(ASkyLight::StaticClass(), Location, FRotator::ZeroRotator);
	SkyLight->GetLightComponent()->SetMobility(EComponentMobility::Movable);
	SkyLight->GetLightComponent()->SetIntensity(10.0f);
	SkyLight->Modify();

	const auto DirectionalLight = World->SpawnActor<ADirectionalLight>(ADirectionalLight::StaticClass(), Location, FRotator::ZeroRotator);
	DirectionalLight->SetMobility(EComponentMobility::Movable);
	DirectionalLight->SetActorRotation(FRotator(0, -90, 0));
	DirectionalLight->SetBrightness(1.0f);
	DirectionalLight->Modify();

	const auto PointLight = World->SpawnActor<APointLight>(APointLight::StaticClass(), Location, FRotator::ZeroRotator);
	PointLight->SetMobility(EComponentMobility::Movable);
	PointLight->PointLightComponent->SetIntensity(30000.0f);
	PointLight->PointLightComponent->SetAttenuationRadius(FullWidth);
	PointLight->Modify();

	const FVector SpotLocation{Location.X, Location.Y * 2, Location.Z};
	const auto SpotLight = World->SpawnActor<ASpotLight>(ASpotLight::StaticClass(), SpotLocation, FRotator::ZeroRotator);
	SpotLight->SetMobility(EComponentMobility::Movable);
	SpotLight->SetActorRotation(FRotator(0, -90, 0));
	SpotLight->SpotLightComponent->SetIntensity(30000.0f);
	SpotLight->SpotLightComponent->SetAttenuationRadius(FullWidth * 2);
	SpotLight->Modify();

	const auto RectLight = World->SpawnActor<ARectLight>(ARectLight::StaticClass(), Location, FRotator::ZeroRotator);
	RectLight->SetMobility(EComponentMobility::Movable);
	RectLight->SetActorRotation(FRotator(0, -90, 0));
	RectLight->RectLightComponent->SetIntensity(30000.0f);
	RectLight->RectLightComponent->SetSourceWidth(FullWidth);
	RectLight->RectLightComponent->SetSourceHeight(FullWidth);
	RectLight->RectLightComponent->SetAttenuationRadius(FullWidth);
	RectLight->Modify();

	const auto PSOLightController = World->SpawnActor<APSOLightController>(APSOLightController::StaticClass());
	PSOLightController->Modify();

	World->GetWorldSettings()->bForceNoPrecomputedLighting = true;
	UE_LOG(LogPSOCollect, Log, TEXT("PSO World Contain %d Actors"), PastedActors.Num());

	UEditorLoadingAndSavingUtils::SaveMap(World, PSOMapName);
}

void UFinder::FindAllPSOAssetFromJSON(const FString& JsonPath, FPSOCollectInfo& PSOCollectInfo)
{
	TArray<FString> AssetPaths;
	LoadAssetPathFromJson(AssetPaths, JsonPath);

	const auto& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
	const IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

	for (const auto& AssetPath : AssetPaths)
	{
		FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(AssetPath);
		CollectPathFromAsset(AssetData, PSOCollectInfo);
	}
}

void UFinder::FindAllMaps(const FString& FolderPath, TArray<FString>& MapPaths)
{
	auto& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	if (PlatformFile.DirectoryExists(*FolderPath))
	{
		TArray<FString> FoundFiles;
		PlatformFile.FindFilesRecursively(FoundFiles, *FolderPath, TEXT(".umap"));

		MapPaths.Append(FoundFiles);
	}
	else
	{
		UE_LOG(LogPSOCollect, Log, TEXT("Directory does not exist: %s"), *FolderPath);
	}
}

void UFinder::FindAllMaterialAssetPaths(UWorld* World, FPSOCollectInfo& PSOCollectInfo)
{
	TSet<UMaterialInterface*> AllUsedMaterials;
	GetUsedMaterialsInWorld(World, AllUsedMaterials, nullptr);
	FindAllMaterialInLevelSequence(World, AllUsedMaterials);

	for (const auto& MaterialInterface : AllUsedMaterials)
	{
		if (MaterialInterface->GetMaterial()->bUsedWithSkeletalMesh
		    && MaterialInterface->GetMaterial()->MaterialDomain == EMaterialDomain::MD_Surface)
		{
			PSOCollectInfo.SkeletalMeshMaterialAssetPaths.Add(FSoftObjectPath{MaterialInterface}.ToString());
		}

		AddStaticMeshMaterialAssetPath(PSOCollectInfo.StaticMeshMaterialAssetPaths, MaterialInterface);
	}
}

void UFinder::FindAllMaterialInLevelSequence(const UWorld* World, TSet<UMaterialInterface*>& Materials)
{
	TArray<AActor*> AllActors;
	UGameplayStatics::GetAllActorsOfClass(World, ALevelSequenceActor::StaticClass(), AllActors);

	for (const auto& Actor : AllActors)
	{
		if (const auto SequenceActor = Cast<ALevelSequenceActor>(Actor))
		{
			if (const auto LevelSequence = SequenceActor->GetSequence())
			{
				for (auto& Binding : LevelSequence->GetMovieScene()->GetBindings())
				{
					for (const auto& Track : Binding.GetTracks())
					{
						for (const auto& Section : Track->GetAllSections())
						{
							if (const auto PrimitiveMatSection = Cast<UMovieScenePrimitiveMaterialSection>(Section))
							{
								for (auto& ValueObj : PrimitiveMatSection->MaterialChannel.GetData().GetValues())
								{
									if (auto Mat = Cast<UMaterialInterface>(ValueObj.Get()))
									{
										Materials.Add(Mat);
									}
								}
							}
						}
					}
				}
			}
		}
	}
}

void UFinder::CreateNiagaraActorsFromPath(UWorld* World, const TSet<FString>& NiagaraAssetPaths, TArray<AActor*>& OutActors)
{
	auto AssetPaths = NiagaraAssetPaths.Array();
	AssetPaths.Sort();

	for (auto& AssetPath : AssetPaths)
	{
		if (const auto Asset = LoadObject<UNiagaraSystem>(nullptr, *AssetPath))
		{
			const auto Actor = World->SpawnActor<ANiagaraActor>();
			Actor->GetNiagaraComponent()->SetAsset(Asset);

			OutActors.Add(Actor);
			AddCustomMoveComponent(Actor);
		}
	}
}

void UFinder::CreateEmitterActorsFromPath(UWorld* World, const TSet<FString>& EmitterAssetPaths, TArray<AActor*>& OutActors)
{
	auto AssetPaths = EmitterAssetPaths.Array();
	AssetPaths.Sort();

	for (auto& AssetPath : AssetPaths)
	{
		if (const auto Asset = LoadObject<UParticleSystem>(nullptr, *AssetPath))
		{
			const auto Actor = World->SpawnActor<AEmitter>();
			Actor->GetParticleSystemComponent()->SetTemplate(Asset);

			OutActors.Add(Actor);
			AddCustomMoveComponent(Actor);
		}
	}
}

void UFinder::CreateStaticMeshActorFromPath(UWorld* World, const TSet<FString>& StaticMeshMaterialAssetPaths, TArray<AActor*>& OutActors)
{
	auto AssetPaths = StaticMeshMaterialAssetPaths.Array();
	AssetPaths.Sort();

	for (auto& AssetPath : AssetPaths)
	{
		if (const auto Material = LoadObject<UMaterialInterface>(nullptr, *AssetPath))
		{
			const auto Actor = World->SpawnActor<AStaticMeshActor>();
			Actor->SetMobility(EComponentMobility::Movable);

			const auto MeshComponent = Actor->GetStaticMeshComponent();
			MeshComponent->SetStaticMesh(StaticMeshTemplate);
			MeshComponent->SetMaterial(0, Material);

			OutActors.Add(Actor);
			AddCustomMoveComponent(Actor);
		}
	}
}

void UFinder::CreateSkeletalMeshActorFromPath(UWorld* World, const TSet<FString>& SkeletalMeshMaterialAssetPaths, TArray<AActor*>& OutActors)
{
	auto AssetPaths = SkeletalMeshMaterialAssetPaths.Array();
	AssetPaths.Sort();

	for (auto& AssetPath : AssetPaths)
	{
		if (const auto Material = LoadObject<UMaterialInterface>(nullptr, *AssetPath))
		{
			const auto Actor = World->SpawnActor<ASkeletalMeshActor>();

			const auto MeshComponent = Actor->GetSkeletalMeshComponent();
			MeshComponent->SetSkeletalMeshAsset(SkeletalMeshTemplate);
			MeshComponent->SetMaterial(0, Material);
			Actor->SetActorScale3D(FVector(4, 4, 4));
			// ScaleToTargetSize(MeshComponent, FVector(100.0f, 100.0f, 100.0f));

			OutActors.Add(Actor);
			AddCustomMoveComponent(Actor);
		}
	}
}

void UFinder::CreateOriginalSkeletalMeshActorFromPath(UWorld* World, const TSet<FString>& SkeletalMeshAssetPaths, TArray<AActor*>& OutActors)
{
	auto AssetPaths = SkeletalMeshAssetPaths.Array();
	AssetPaths.Sort();

	for (auto& AssetPath : AssetPaths)
	{
		if (const auto Asset = LoadObject<USkeletalMesh>(nullptr, *AssetPath))
		{
			Asset->InitResources();

			const auto Actor = World->SpawnActor<ASkeletalMeshActor>();
			const auto MeshComponent = Actor->GetSkeletalMeshComponent();

			MeshComponent->SetSkeletalMeshAsset(Asset);
			ScaleToTargetSize(MeshComponent, FVector(100.0f, 100.0f, 100.0f));

			MeshComponent->SetCPUSkinningEnabled(false);
			const auto SkeletalMesh = MeshComponent->GetSkeletalMeshAsset();

			for (int32 LODIndex = 0; LODIndex < SkeletalMesh->GetLODNum(); ++LODIndex)
			{
				if (auto LODInfo = SkeletalMesh->GetLODInfo(LODIndex))
				{
					LODInfo->SkinCacheUsage = ESkinCacheUsage::Enabled;
				}
			}

			OutActors.Add(Actor);
			AddCustomMoveComponent(Actor);
		}
	}
}

void UFinder::CreateOriginalStaticMeshActorFromPath(UWorld* World, const TSet<FString>& StaticMeshPaths, TArray<AActor*>& OutActors)
{
	UE_LOG(LogPSOCollect, Log, TEXT("StaticMeshTemplate Mesh: %s"), *StaticMeshTemplate->GetPathName());
	FVertexDeclarationElementList StaticMeshTemplateElementList;
	GetVertexDeclarationElementList(StaticMeshTemplate, StaticMeshTemplateElementList);
	PrintFVertexDeclarationElementList(StaticMeshTemplateElementList);

	TArray<FVertexDeclarationElementList> CubeMeshElementList;
	for (UStaticMesh* CubeMesh : CubeMeshes)
	{
		UE_LOG(LogPSOCollect, Log, TEXT("Cube Mesh: %s"), *CubeMesh->GetPathName());
		FVertexDeclarationElementList ElementList;
		GetVertexDeclarationElementList(CubeMesh, ElementList);
		PrintFVertexDeclarationElementList(ElementList);
		CubeMeshElementList.Add(ElementList);
	}
	UE_LOG(LogPSOCollect, Log, TEXT("PSOCollect Cube Mesh Num = %d"), CubeMeshes.Num());

	auto AssetPaths = StaticMeshPaths.Array();
	AssetPaths.Sort();

	TArray<UStaticMesh*> LoadedMeshes;
	LoadStaticMeshByPaths(AssetPaths, LoadedMeshes);

	TMap<FString, TSet<int>> Map;
	for (const auto& LoadedMesh : LoadedMeshes)
	{
		FVertexDeclarationElementList VertexDeclarationElementList;
		GetVertexDeclarationElementList(LoadedMesh, VertexDeclarationElementList);
		if (CompareVertexDeclarationElementList(VertexDeclarationElementList, StaticMeshTemplateElementList))
		{
			continue;
		}

		bool IsFind = false;
		for (int i = 0; i < CubeMeshElementList.Num(); i++)
		{
			if (CompareVertexDeclarationElementList(VertexDeclarationElementList, CubeMeshElementList[i]))
			{
				UE_LOG(LogPSOCollect, Log, TEXT("StaticMesh VertexElement is same to %s, %s"), *CubeMeshes[i]->GetPathName(), *LoadedMesh->GetPathName());

				const auto& Materials = LoadedMesh->GetStaticMaterials();
				for (const auto& Material : Materials)
				{
					FSoftObjectPath SoftObjectPath{};
					const auto& MaterialInterface = Material.MaterialInterface;
					if (MaterialInterface == nullptr)
					{
						UE_LOG(LogPSOCollect, Log, TEXT("MaterialInterface == nullptr, Asset is :%s, use WorldGridMaterial Instead"), *LoadedMesh->GetPathName());
						SoftObjectPath = FSoftObjectPath{WorldGridMaterial};
					}
					else
					{
						SoftObjectPath = FSoftObjectPath{MaterialInterface};
					}

					const auto SoftPath = SoftObjectPath.ToString();
					if (!Map.Contains(SoftPath))
					{
						Map.Add(SoftPath, TSet<int>());
					}

					Map[SoftPath].Add(i);
				}

				IsFind = true;
				break;
			}
		}

		if (!IsFind)
		{
			UE_LOG(LogPSOCollect, Log, TEXT("StaticMesh VertexElement is not same to All UVMesh, %s"), *LoadedMesh->GetPathName());
			PrintFVertexDeclarationElementList(VertexDeclarationElementList);

			const auto Actor = World->SpawnActor<AStaticMeshActor>();
			Actor->SetMobility(EComponentMobility::Movable);

			const auto MeshComponent = Actor->GetStaticMeshComponent();
			MeshComponent->SetStaticMesh(LoadedMesh);
			ScaleToTargetSize(MeshComponent, FVector(100.0f, 100.0f, 100.0f));

			OutActors.Add(Actor);
			AddCustomMoveComponent(Actor);
		}
	}

	for (auto& Result : Map)
	{
		if (const auto Material = LoadObject<UMaterialInterface>(nullptr, *Result.Key))
		{
			for (const auto& Index : Result.Value)
			{
				const auto Actor = World->SpawnActor<AStaticMeshActor>();
				Actor->SetMobility(EComponentMobility::Movable);

				const auto MeshComponent = Actor->GetStaticMeshComponent();
				MeshComponent->SetStaticMesh(CubeMeshes[Index]);
				MeshComponent->SetMaterial(0, Material);
				ScaleToTargetSize(MeshComponent, FVector(100.0f, 100.0f, 100.0f));

				OutActors.Add(Actor);
				AddCustomMoveComponent(Actor);
			}
		}
	}
}

void UFinder::SortActors(TArray<AActor*>& Actors, const float Y)
{
	const int32 NumActors = Actors.Num();
	const int32 NumSqrt = FMath::CeilToInt(FMath::Sqrt(static_cast<float>(NumActors)));

	for (int32 i = 0; i < NumSqrt; i++)
	{
		for (int32 j = 0; j < NumSqrt; j++)
		{
			const int32 Index = i + j * NumSqrt;
			if (Index >= NumActors)
			{
				break;
			}

			if (const auto SceneComponent = Actors[Index]->GetRootComponent())
			{
				SceneComponent->SetRelativeLocation(FVector(i * BaseStep, Y, j * BaseStep));
				SceneComponent->SetRelativeRotation(FRotator(0, 0, 90));
			}
		}
	}
}

void UFinder::AddStaticMeshMaterialAssetPath(TSet<FString>& MaterialAssetPaths, UMaterialInterface* MaterialInterface)
{
	if (MaterialInterface == nullptr)
	{
		return;
	}

	FSoftObjectPath SoftObjectPath{};
	const auto& MaterialInstanceConstant = Cast<UMaterialInstanceConstant>(MaterialInterface);
	if (IsStaticMeshPSODifferent(MaterialInstanceConstant))
	{
		SoftObjectPath = FSoftObjectPath{MaterialInstanceConstant};
	}
	else
	{
		SoftObjectPath = FSoftObjectPath{MaterialInterface->GetMaterial()};
	}

	if (SoftObjectPath.IsValid())
	{
		MaterialAssetPaths.Add(SoftObjectPath.ToString());
	}
}

void UFinder::CollectPathFromAsset(const FAssetData& AssetData, FPSOCollectInfo& PSOCollectInfo)
{
	if (AssetData.IsValid())
	{
		const auto AssetName = AssetData.AssetClassPath.GetAssetName();
		const auto ObjectPath = AssetData.GetSoftObjectPath().ToString();
		if (
			AssetName == UMaterial::StaticClass()->GetFName()
			|| AssetName == UMaterialInstanceConstant::StaticClass()->GetFName()
		)
		{
			if (const auto MaterialInterface = Cast<UMaterialInterface>(AssetData.GetAsset()))
			{
				if (MaterialInterface->GetMaterial()
				    && MaterialInterface->GetMaterial()->bUsedWithSkeletalMesh
				    && MaterialInterface->GetMaterial()->MaterialDomain == EMaterialDomain::MD_Surface)
				{
					PSOCollectInfo.SkeletalMeshMaterialAssetPaths.Add(FSoftObjectPath{MaterialInterface}.ToString());
				}

				AddStaticMeshMaterialAssetPath(PSOCollectInfo.StaticMeshMaterialAssetPaths, MaterialInterface);
			}
		}
		else if (AssetName == UNiagaraSystem::StaticClass()->GetFName())
		{
			PSOCollectInfo.NiagaraAssetPaths.Add(ObjectPath);
		}
		else if (AssetName == UParticleSystem::StaticClass()->GetFName())
		{
			PSOCollectInfo.EmitterAssetPaths.Add(ObjectPath);
		}
		else if (AssetName == UStaticMesh::StaticClass()->GetFName())
		{
			if (const auto Mesh = Cast<UStaticMesh>(AssetData.GetAsset()))
			{
				PSOCollectInfo.StaticMeshPaths.Add(ObjectPath);

				const auto& Materials = Mesh->GetStaticMaterials();
				for (const auto& Material : Materials)
				{
					const auto& MaterialInterface = Material.MaterialInterface;
					if (MaterialInterface != nullptr)
					{
						AddStaticMeshMaterialAssetPath(PSOCollectInfo.StaticMeshMaterialAssetPaths, MaterialInterface.Get());
					}
					else
					{
						UE_LOG(LogPSOCollect, Log, TEXT("MaterialInterface == nullptr, Mesh is :%s "), *Mesh->GetPathName());
					}
				}
			}
		}
		else if (AssetName == USkeletalMesh::StaticClass()->GetFName())
		{
			if (const auto Mesh = Cast<USkeletalMesh>(AssetData.GetAsset()))
			{
				PSOCollectInfo.SkeletalMeshPaths.Add(ObjectPath);

				const auto& Materials = Mesh->GetMaterials();
				for (const auto& Material : Materials)
				{
					const auto& MaterialInterface = Material.MaterialInterface;
					if (MaterialInterface != nullptr)
					{
						PSOCollectInfo.SkeletalMeshMaterialAssetPaths.Add(FSoftObjectPath{MaterialInterface}.ToString());
					}
					else
					{
						UE_LOG(LogPSOCollect, Log, TEXT("MaterialInterface == nullptr, Mesh is :%s "), *Mesh->GetPathName());
					}
				}
			}
		}
	}
}

void UFinder::LoadAssetPathFromJson(TArray<FString>& AssetPaths, const FString& JsonPath)
{
	FString JsonContent;
	if (FFileHelper::LoadFileToString(JsonContent, *JsonPath))
	{
		TArray<TSharedPtr<FJsonValue>> JsonArray;
		const auto JsonReader = TJsonReaderFactory<>::Create(JsonContent);

		if (FJsonSerializer::Deserialize(JsonReader, JsonArray) && JsonArray.Num() > 0)
		{
			for (const TSharedPtr<FJsonValue>& JsonValue : JsonArray)
			{
				if (JsonValue->Type == EJson::String)
				{
					AssetPaths.Add(JsonValue->AsString());
				}
			}
		}
		else
		{
			UE_LOG(LogPSOCollect, Log, TEXT("Failed to deserialize JSON file or JSON array is empty."));
		}
	}
	else
	{
		UE_LOG(LogPSOCollect, Log, TEXT("Failed to load JSON file from path: %s"), *JsonPath);
	}
}

void UFinder::AddCustomMoveComponent(AActor* Actor)
{
	if (Actor)
	{
		if (!Actor->FindComponentByClass<UCustomMoveComponent>())
		{
			if (const auto CustomMoveComponent = NewObject<UCustomMoveComponent>(Actor, UCustomMoveComponent::StaticClass()))
			{
				CustomMoveComponent->SetMoveSpeedAndDistance(100.0f, 500.0f);
				CustomMoveComponent->SetRotationSpeed(20.0f);
				CustomMoveComponent->RegisterComponent();
				Actor->AddInstanceComponent(CustomMoveComponent);
			}
		}
	}
}

void UFinder::GetVertexDeclarationElementList(UStaticMesh* Mesh, FVertexDeclarationElementList& VertexDeclarationElementList)
{
	auto& VertexFactory = Mesh->GetRenderData()->LODVertexFactories[0].VertexFactory;
	auto VertexData = static_cast<FCustomLocalVertexFactory*>(&VertexFactory)->AccessGetData();

	FLocalVertexFactory::GetVertexElements(GMaxRHIFeatureLevel, EVertexInputStreamType::Default, false, VertexData, VertexDeclarationElementList);
	VertexDeclarationElementList.Sort(SortVertexElements);
}

bool UFinder::SortVertexElements(const FVertexElement& A, const FVertexElement& B)
{
	if (A.StreamIndex < B.StreamIndex)
	{
		return true;
	}
	if (A.StreamIndex > B.StreamIndex)
	{
		return false;
	}
	if (A.Offset < B.Offset)
	{
		return true;
	}
	if (A.Offset > B.Offset)
	{
		return false;
	}
	if (A.AttributeIndex < B.AttributeIndex)
	{
		return true;
	}
	if (A.AttributeIndex > B.AttributeIndex)
	{
		return false;
	}
	return false;
}

bool UFinder::CompareVertexDeclarationElementList(const FVertexDeclarationElementList& A, const FVertexDeclarationElementList& B)
{
	if (A.Num() != B.Num())
	{
		// UE_LOG(LogPSOCollect, Log, TEXT("A.Num(): %d, B.Num(): %d"), A.Num(), B.Num());
		return false;
	}

	for (int i = 0; i < A.Num(); i++)
	{
		if (A[i] == B[i])
		{
			continue;
		}

		// UE_LOG(LogPSOCollect, Log, TEXT("A[%d] != B[%d], A[%d]: %s, B[%d]:%s"), i, i, i, *A[i].ToString(), i, *B[i].ToString());
		return false;
	}

	return true;
}

void UFinder::ScaleToTargetSize(UMeshComponent* MeshComponent, const FVector& TargetSize)
{
	FBoxSphereBounds MeshBounds = MeshComponent->Bounds;
	FVector CurrentSize = MeshBounds.BoxExtent * 2.0f; // BoxExtent是半径，需要乘2以得到直径
	FVector ScaleFactor = TargetSize / CurrentSize;
	float MinScaleFactor = FMath::Min3(ScaleFactor.X, ScaleFactor.Y, ScaleFactor.Z);
	MeshComponent->SetWorldScale3D(FVector(MinScaleFactor));
}

void UFinder::LoadStaticMeshInDirectory(const FString& DirectoryPath, TArray<UStaticMesh*>& LoadedMeshes)
{
	const auto& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
	auto& AssetRegistry = AssetRegistryModule.Get();
	AssetRegistry.SearchAllAssets(true);

	FARFilter Filter;
	Filter.bRecursivePaths = true;
	Filter.bRecursiveClasses = true;
	Filter.PackagePaths.Add(*DirectoryPath);
	Filter.ClassPaths.Add(UStaticMesh::StaticClass()->GetClassPathName());

	for (auto& PackagePath : Filter.PackagePaths)
	{
		UE_LOG(LogPSOCollect, Log, TEXT("PackagePath: %s"), *PackagePath.ToString());
	}
	UE_LOG(LogPSOCollect, Log, TEXT("DirectoryPath: %s"), *DirectoryPath);

	TArray<FAssetData> AssetDataList;
	AssetRegistry.GetAssets(Filter, AssetDataList);

	UE_LOG(LogPSOCollect, Log, TEXT("AssetDataList.Num: %d"), AssetDataList.Num());

	TArray<FSoftObjectPath> SoftObjectPaths;
	SoftObjectPaths.Empty(AssetDataList.Num());

	for (const auto& AssetData : AssetDataList)
	{
		SoftObjectPaths.Add(AssetData.GetSoftObjectPath());
	}

	LoadStaticMeshBySoftPaths(SoftObjectPaths, LoadedMeshes);
}

bool UFinder::IsStaticMeshPSODifferent(UMaterialInstanceConstant* MaterialInstance)
{
	if (!MaterialInstance || !MaterialInstance->Parent)
	{
		return false;
	}

	FStaticParameterSet InstanceParams;
	MaterialInstance->GetStaticParameterValues(InstanceParams);

	UMaterial* ParentMaterial = MaterialInstance->GetMaterial();
	FStaticParameterSet ParentParams;
	ParentMaterial->GetStaticParameterValues(ParentParams);

	for (const auto& InstanceSwitch : InstanceParams.StaticSwitchParameters)
	{
		const auto* ParentSwitch = ParentParams.StaticSwitchParameters.FindByPredicate(
			[&](const FStaticSwitchParameter& Param) {
				return Param.ParameterInfo == InstanceSwitch.ParameterInfo;
			});

		if (ParentSwitch && ParentSwitch->Value != InstanceSwitch.Value)
		{
			return true;
		}
	}

#if WITH_EDITORONLY_DATA
	for (const auto& InstanceMask : InstanceParams.EditorOnly.StaticComponentMaskParameters)
	{
		const auto* ParentMask = ParentParams.EditorOnly.StaticComponentMaskParameters.FindByPredicate(
			[&](const FStaticComponentMaskParameter& Param) {
				return Param.ParameterInfo == InstanceMask.ParameterInfo;
			});

		if (ParentMask && (ParentMask->R != InstanceMask.R || ParentMask->G != InstanceMask.G || ParentMask->B != InstanceMask.B || ParentMask->A != InstanceMask.A))
		{
			return true;
		}
	}
#endif

	if (ParentMaterial->IsTwoSided() != MaterialInstance->IsTwoSided())
	{
		return true;
	}

	if (ParentMaterial->GetBlendMode() != MaterialInstance->GetBlendMode())
	{
		return true;
	}

	return false;
}

void UFinder::LoadStaticMeshByPaths(const TArray<FString>& Paths, TArray<UStaticMesh*>& LoadedMeshes)
{
	TArray<FSoftObjectPath> SoftObjectPaths;
	SoftObjectPaths.Empty(Paths.Num());

	for (auto& Path : Paths)
	{
		SoftObjectPaths.Add(Path);
	}

	LoadStaticMeshBySoftPaths(SoftObjectPaths, LoadedMeshes);
}

void UFinder::LoadStaticMeshBySoftPaths(const TArray<FSoftObjectPath>& SoftObjectPaths, TArray<UStaticMesh*>& LoadedMeshes)
{
	TArray<UObject*> LoadedAssets;
	LoadAssetBySoftPaths(SoftObjectPaths, LoadedAssets);

	for (UObject* LoadedAsset : LoadedAssets)
	{
		const auto LoadedMesh = Cast<UStaticMesh>(LoadedAsset);
		LoadedMesh->InitResources();
		LoadedMeshes.Add(LoadedMesh);

		if (!LoadedMesh->GetRenderData()->IsInitialized())
		{
			UE_LOG(LogPSOCollect, Log, TEXT("=== Failed to fully load or initialize render data for asset at path: %s"), *LoadedMesh->GetPathName());
		}
	}
}

void UFinder::LoadAssetBySoftPaths(const TArray<FSoftObjectPath>& SoftObjectPaths, TArray<UObject*>& LoadedAssets)
{
	FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();
	const auto& Handle = StreamableManager.RequestSyncLoad(SoftObjectPaths);

	Handle->GetLoadedAssets(LoadedAssets);
	UE_LOG(LogPSOCollect, Log, TEXT("LoadedAssets Num = %d"), LoadedAssets.Num());
}