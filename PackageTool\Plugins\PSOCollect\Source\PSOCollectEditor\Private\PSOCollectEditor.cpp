﻿// Copyright Epic Games, Inc. All Rights Reserved.

#include "PSOCollectEditor.h"

#include "Finder.h"
#include "PSOCollectEditorStyle.h"
#include "PSOCollectEditorCommands.h"
#include "ToolMenus.h"

static const FName PSOCollectEditorTabName("PSOCollectEditor");

#define LOCTEXT_NAMESPACE "FPSOCollectEditorModule"

void FPSOCollectEditorModule::StartupModule()
{
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module

	FPSOCollectEditorStyle::Initialize();
	FPSOCollectEditorStyle::ReloadTextures();

	FPSOCollectEditorCommands::Register();

	PluginCommands = MakeShareable(new FUICommandList);

	PluginCommands->MapAction(
		FPSOCollectEditorCommands::Get().OpenPluginWindow,
		FExecuteAction::CreateRaw(this, &FPSOCollectEditorModule::PluginButtonClicked),
		FCanExecuteAction());

	UToolMenus::RegisterStartupCallback(FSimpleMulticastDelegate::FDelegate::CreateRaw(this, &FPSOCollectEditorModule::RegisterMenus));
}

void FPSOCollectEditorModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.

	UToolMenus::UnRegisterStartupCallback(this);

	UToolMenus::UnregisterOwner(this);

	FPSOCollectEditorStyle::Shutdown();

	FPSOCollectEditorCommands::Unregister();

	FGlobalTabmanager::Get()->UnregisterNomadTabSpawner(PSOCollectEditorTabName);
}


void FPSOCollectEditorModule::PluginButtonClicked()
{
	// FGlobalTabmanager::Get()->TryInvokeTab(PSOCollectTabName);
	UFinder::ScanLevels();
}

void FPSOCollectEditorModule::RegisterMenus()
{
	// Owner will be used for cleanup in call to UToolMenus::UnregisterOwner
	FToolMenuOwnerScoped OwnerScoped(this);

	{
		UToolMenu* Menu = UToolMenus::Get()->ExtendMenu("LevelEditor.MainMenu.Window");
		{
			FToolMenuSection& Section = Menu->FindOrAddSection("WindowLayout");
			Section.AddMenuEntryWithCommandList(FPSOCollectEditorCommands::Get().OpenPluginWindow, PluginCommands);
		}
	}

	{
		UToolMenu* ToolbarMenu = UToolMenus::Get()->ExtendMenu("LevelEditor.LevelEditorToolBar.PlayToolBar");
		{
			FToolMenuSection& Section = ToolbarMenu->FindOrAddSection("PluginTools");
			{
				FToolMenuEntry& Entry = Section.AddEntry(FToolMenuEntry::InitToolBarButton(FPSOCollectEditorCommands::Get().OpenPluginWindow));
				Entry.SetCommandList(PluginCommands);
			}
		}
	}
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FPSOCollectEditorModule, PSOCollect)