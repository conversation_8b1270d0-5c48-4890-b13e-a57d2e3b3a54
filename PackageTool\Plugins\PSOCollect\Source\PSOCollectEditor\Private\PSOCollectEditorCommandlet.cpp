﻿// Copyright Xverse. All Rights Reserved.


#include "PSOCollectEditorCommandlet.h"
#include "Finder.h"

int32 UPSOCollectEditorCommandlet::Main(const FString& Params)
{
	StartPSOCollectPlugin();
	// ModifyAndroidEngineConfig();
	return 0;
}

void UPSOCollectEditorCommandlet::StartPSOCollectPlugin()
{
	UE_LOG(LogTemp, Log, TEXT("============================ Start StartPSOCollectPlugin ============================"));
	UFinder::ScanLevels();
	UE_LOG(LogTemp, Log, TEXT("============================ End StartPSOCollectPlugin ============================"));
}

void UPSOCollectEditorCommandlet::ModifyAndroidEngineConfig()
{
	const FString ConfigFilePath = FPaths::ProjectConfigDir() / TEXT("Android/AndroidEngine.ini");
	FConfigFile AndroidConfig;

	AndroidConfig.Read(ConfigFilePath);
	AndroidConfig.SetString(TEXT("/Script/EngineSettings.GameMapsSettings"),TEXT("GameDefaultMap"),TEXT("/Game/PSOCollect/PSOWorld"));
	AndroidConfig.SetString(TEXT("ConsoleVariables"),TEXT("r.ShaderPipelineCache.Enabled"),TEXT("1"));
	AndroidConfig.SetString(TEXT("ConsoleVariables"),TEXT("r.ShaderPipelineCache.LogPSO"),TEXT("1"));
	AndroidConfig.SetString(TEXT("ConsoleVariables"),TEXT("r.ShaderPipelineCache.SaveBoundPSOLog"),TEXT("1"));
	AndroidConfig.SetString(TEXT("ConsoleVariables"),TEXT("r.ShaderPipelineCache.PrintNewPSODescriptors"),TEXT("2"));

	AndroidConfig.Write(ConfigFilePath);
}