﻿// Copyright Epic Games, Inc. All Rights Reserved.

#include "PSOCollectEditorStyle.h"
#include "Styling/SlateStyleRegistry.h"
#include "Framework/Application/SlateApplication.h"
#include "Slate/SlateGameResources.h"
#include "Interfaces/IPluginManager.h"
#include "Styling/SlateStyleMacros.h"

#define RootToContentDir Style->RootToContentDir

TSharedPtr<FSlateStyleSet> FPSOCollectEditorStyle::StyleInstance = nullptr;

void FPSOCollectEditorStyle::Initialize()
{
	if (!StyleInstance.IsValid())
	{
		StyleInstance = Create();
		FSlateStyleRegistry::RegisterSlateStyle(*StyleInstance);
	}
}

void FPSOCollectEditorStyle::Shutdown()
{
	FSlateStyleRegistry::UnRegisterSlateStyle(*StyleInstance);
	ensure(StyleInstance.IsUnique());
	StyleInstance.Reset();
}

FName FPSOCollectEditorStyle::GetStyleSetName()
{
	static FName StyleSetName(TEXT("PSOCollectStyle"));
	return StyleSetName;
}

const FVector2D Icon16x16(16.0f, 16.0f);
const FVector2D Icon20x20(20.0f, 20.0f);

TSharedRef< FSlateStyleSet > FPSOCollectEditorStyle::Create()
{
	TSharedRef< FSlateStyleSet > Style = MakeShareable(new FSlateStyleSet("PSOCollectStyle"));
	Style->SetContentRoot(IPluginManager::Get().FindPlugin("PSOCollect")->GetBaseDir() / TEXT("Resources"));

	Style->Set("PSOCollect.OpenPluginWindow", new IMAGE_BRUSH_SVG(TEXT("PSOIcon"), Icon20x20));

	return Style;
}

void FPSOCollectEditorStyle::ReloadTextures()
{
	if (FSlateApplication::IsInitialized())
	{
		FSlateApplication::Get().GetRenderer()->ReloadTextureResources();
	}
}

const ISlateStyle& FPSOCollectEditorStyle::Get()
{
	return *StyleInstance;
}
