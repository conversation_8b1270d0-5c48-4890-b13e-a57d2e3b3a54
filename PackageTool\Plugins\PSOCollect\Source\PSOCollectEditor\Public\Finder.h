﻿// Copyright Xverse. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"

#include "Materials/MaterialInstanceConstant.h"
#include "Finder.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogPSOCollect, Log, All);

USTRUCT()
struct FPSOCollectInfo
{
	GENERATED_BODY()

	TSet<FString> StaticMeshPaths;
	TSet<FString> SkeletalMeshPaths;
	TSet<FString> NiagaraAssetPaths;
	TSet<FString> EmitterAssetPaths;
	TSet<FString> StaticMeshMaterialAssetPaths;
	TSet<FString> SkeletalMeshMaterialAssetPaths;
};

class ENGINE_API FCustomLocalVertexFactory : public FLocalVertexFactory
{
public:
	explicit FCustomLocalVertexFactory(const FLocalVertexFactory& InBaseFactory)
		: FLocalVertexFactory(InBaseFactory)
	{
	}

	const FDataType& AccessGetData() const
	{
		return GetData();
	}
};

/**
 * 
 */
UCLASS()
class PSOCOLLECTEDITOR_API UFinder : public UObject
{
	GENERATED_BODY()

public:
	static void ScanLevels();
	static void FindAllPSOAssetInMap(const TArray<FString>& MapFolders, FPSOCollectInfo& PSOCollectInfo);
	static void FindAllPSOAssetInDirectory(const TArray<FString>& DirectoryPaths, FPSOCollectInfo& PSOCollectInfo);
	static void CreatePSOMap(const FString& PSOMapName, const FPSOCollectInfo& PSOCollectInfo);

	static void FindAllPSOAssetFromJSON(const FString& JsonPath, FPSOCollectInfo& PSOCollectInfo);

private:
	static void FindAllMaps(const FString& FolderPath, TArray<FString>& MapPaths);
	static void FindAllMaterialAssetPaths(UWorld* World, FPSOCollectInfo& PSOCollectInfo);
	static void FindAllMaterialInLevelSequence(const UWorld* World, TSet<UMaterialInterface*>& Materials);

	static void CreateNiagaraActorsFromPath(UWorld* World, const TSet<FString>& NiagaraAssetPaths, TArray<AActor*>& OutActors);
	static void CreateEmitterActorsFromPath(UWorld* World, const TSet<FString>& EmitterAssetPaths, TArray<AActor*>& OutActors);
	static void CreateStaticMeshActorFromPath(UWorld* World, const TSet<FString>& StaticMeshMaterialAssetPaths, TArray<AActor*>& OutActors);
	static void CreateSkeletalMeshActorFromPath(UWorld* World, const TSet<FString>& SkeletalMeshMaterialAssetPaths, TArray<AActor*>& OutActors);
	static void CreateOriginalSkeletalMeshActorFromPath(UWorld* World, const TSet<FString>& SkeletalMeshAssetPaths, TArray<AActor*>& OutActors);
	static void CreateOriginalStaticMeshActorFromPath(UWorld* World, const TSet<FString>& StaticMeshPaths, TArray<AActor*>& OutActors);


	static void SortActors(TArray<AActor*>& Actors, const float Y);
	static void AddStaticMeshMaterialAssetPath(TSet<FString>& MaterialAssetPaths, UMaterialInterface* MaterialInterface);


	static void CollectPathFromAsset(const FAssetData& AssetData, FPSOCollectInfo& PSOCollectInfo);

	static void LoadAssetPathFromJson(TArray<FString>& AssetPaths, const FString& JsonPath);

	static void AddCustomMoveComponent(AActor* Actor);


	static void GetVertexDeclarationElementList(UStaticMesh* Mesh, FVertexDeclarationElementList& VertexDeclarationElementList);
	static bool SortVertexElements(const FVertexElement& A, const FVertexElement& B);
	static bool CompareVertexDeclarationElementList(const FVertexDeclarationElementList& A, const FVertexDeclarationElementList& B);

	static void ScaleToTargetSize(UMeshComponent* MeshComponent, const FVector& TargetSize);

	static void LoadStaticMeshInDirectory(const FString& DirectoryPath, TArray<UStaticMesh*>& LoadedMeshes);
	static bool IsStaticMeshPSODifferent(UMaterialInstanceConstant* MaterialInstance);

	static void LoadStaticMeshByPaths(const TArray<FString>& Paths, TArray<UStaticMesh*>& LoadedMeshes);
	static void LoadStaticMeshBySoftPaths(const TArray<FSoftObjectPath>& SoftObjectPaths, TArray<UStaticMesh*>& LoadedMeshes);
	static void LoadAssetBySoftPaths(const TArray<FSoftObjectPath>& SoftObjectPaths, TArray<UObject*>& LoadedAssets);

	static float OpacityValues[3];
	static float BaseStep;

	static UStaticMesh* StaticMeshTemplate;
	static USkeletalMesh* SkeletalMeshTemplate;
	static TArray<UStaticMesh*> CubeMeshes;

	static UMaterialInterface* WorldGridMaterial;
};