﻿// Copyright Xverse. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Commandlets/Commandlet.h"
#include "PSOCollectEditorCommandlet.generated.h"


/**
 * 
 */
UCLASS()
class PSOCOLLECTEDITOR_API UPSOCollectEditorCommandlet : public UCommandlet
{
	GENERATED_BODY()


public:
	UPSOCollectEditorCommandlet()
	{
	}
	
	int32 Main(const FString& Params) override;
	void static StartPSOCollectPlugin();
	void static ModifyAndroidEngineConfig();
};
