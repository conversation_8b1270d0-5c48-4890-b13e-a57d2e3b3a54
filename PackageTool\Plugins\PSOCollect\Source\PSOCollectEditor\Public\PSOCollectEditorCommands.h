﻿// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"
#include "PSOCollectEditorStyle.h"

class FPSOCollectEditorCommands : public TCommands<FPSOCollectEditorCommands>
{
public:

	FPSOCollectEditorCommands()
		: TCommands<FPSOCollectEditorCommands>(TEXT("PSOCollect"), NSLOCTEXT("Contexts", "PSOCollect", "PSOCollect Plugin"), NAME_None, FPSOCollectEditorStyle::GetStyleSetName())
	{
	}

	// TCommands<> interface
	virtual void RegisterCommands() override;

public:
	TSharedPtr< FUICommandInfo > OpenPluginWindow;
};