﻿// Copyright Xverse. All Rights Reserved.


#include "CustomMoveComponent.h"


// Sets default values for this component's properties
UCustomMoveComponent::UCustomMoveComponent()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;

	Speed = 100.0f;
	Distance = 100.0f;
	CurrentEdgeIndex = 0;

	RotationSpeed = 10.0f;
}

void UCustomMoveComponent::SetMoveSpeedAndDistance(const float MoveSpeed, const float MoveDistance)
{
	Speed = MoveSpeed;
	Distance = MoveDistance;
}

void UCustomMoveComponent::SetRotationSpeed(const float FRotationSpeed)
{
	RotationSpeed = FRotationSpeed;
}

// Called when the game starts
void UCustomMoveComponent::BeginPlay()
{
	Super::BeginPlay();

	StartLocation = GetOwner()->GetActorLocation();
	CurrentLocation = StartLocation;

	PathCorners.Empty();
	PathCorners.Add(StartLocation + FVector(Distance, 0.0f, 0.0f));
	PathCorners.Add(StartLocation + FVector(Distance, Distance, 0.0f));
	PathCorners.Add(StartLocation + FVector(0.0f, Distance, 0.0f));
	PathCorners.Add(StartLocation + FVector(0.0f, 0.0f, 0.0f));
}


// Called every frame
void UCustomMoveComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (PathCorners.Num() > 0)
	{
		FVector TargetLocation = PathCorners[CurrentEdgeIndex];

		FVector Direction = (TargetLocation - CurrentLocation).GetSafeNormal();
		FVector Movement = Direction * Speed * DeltaTime;

		if ((CurrentLocation - TargetLocation).Size() <= Movement.Size())
		{
			CurrentLocation = TargetLocation;
			CurrentEdgeIndex = (CurrentEdgeIndex + 1) % PathCorners.Num();
		}
		else
		{
			CurrentLocation += Movement;
		}

		GetOwner()->SetActorLocation(CurrentLocation);
	}

	// Rotation
	// FRotator NewRotation = FRotator(RotationSpeed * DeltaTime, RotationSpeed * DeltaTime, 0); // 计算当前帧的旋转量
	// GetOwner()->AddActorLocalRotation(NewRotation);
}