﻿// Copyright Xverse. All Rights Reserved.


#include "PSOLightController.h"

#include "Engine/DirectionalLight.h"
#include "Engine/Light.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Engine/RectLight.h"
#include "Engine/SkyLight.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "ComponentRecreateRenderStateContext.h"
#include "NiagaraActor.h"
#include "NiagaraComponent.h"
#include "Particles/Emitter.h"


// Sets default values
APSOLightController::APSOLightController()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

FPermutationState CastStateFromInteger(uint32 State)
{
	State %= (1 << PERMUTATION_STATE_COUNT);
	FPermutationState Ret;
	Ret.DirectionLightState = (State & 0x1) == 0 ? 0 : 1;
	Ret.LocalLightState = (State & 0x2) == 0 ? 0 : 1;
	Ret.SkyLightState = (State & 0x4) == 0 ? 0 : 1;
	Ret.SkinCacheState = (State & 0x08) == 0 ? 0 : 1;
	Ret.PrecomputredLightingState = (State & 0x10) == 0 ? 0 : 1;
	Ret.CastShadowState = (State & 0x20) == 0 ? 0 : 1;
	UE_LOG(LogCore,
	       Warning,
	       TEXT("CastStateFromInteger STATE %d Ret.DirectionLightState %u Ret.LocalLightState %u Ret.SkyLightState %u Ret.PrecomputredLightingState %u Ret.SkinCacheState %u Ret.CastShadowState %u (1 << PERMUTATION_STATE_COUNT) %u"),
	       State,
	       Ret.DirectionLightState,
	       Ret.LocalLightState,
	       Ret.SkyLightState,
	       Ret.PrecomputredLightingState,
	       Ret.SkinCacheState,
	       Ret.CastShadowState,
	       (1 << PERMUTATION_STATE_COUNT)
		);
	return Ret;
}

// Called when the game starts or when spawned
void APSOLightController::BeginPlay()
{
	Super::BeginPlay();

	UGameplayStatics::GetAllActorsOfClass(GetWorld(), AActor::StaticClass(), AllActors);

	for (const auto Actor : AllActors)
	{
		if (const auto Light = Cast<ALight>(Actor))
		{
			Lights.Add(Light);
		}
		else if (const auto SkyLight = Cast<ASkyLight>(Actor))
		{
			Lights.Add(SkyLight);
		}
		else if (const auto NiagaraActor = Cast<ANiagaraActor>(Actor))
		{
			ParticleActors.Add(NiagaraActor);
		}
		else if (const auto Emitter = Cast<AEmitter>(Actor))
		{
			ParticleActors.Add(Emitter);
		}
	}

	CurrentCombinationIndex = 0;
	// init state
	memset(&CntPermutationState, 0, sizeof(FPermutationState));
	SwitchDirectionalLight(CntPermutationState.DirectionLightState);
	SwitchLocalLightState(CntPermutationState.LocalLightState);
	SwitchSkyLightState(CntPermutationState.SkyLightState);
	SwitchSkinCacheState(CntPermutationState.SkinCacheState);
	SwitchPrecomputedLightingState(CntPermutationState.PrecomputredLightingState);
	SwitchDirectionalCastShadow(CntPermutationState.CastShadowState);

	GetWorld()->GetTimerManager().SetTimer(LightSwitchTimer, this, &APSOLightController::SwitchPermutation, 1.0f, true);
	GetWorld()->GetTimerManager().SetTimer(ParticleTimer, this, &APSOLightController::ReActiveParticleActors, 10.0f, true);

	UE_LOG(LogTemp, Error, TEXT("Begin Play"));
}

void APSOLightController::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	AllActors.Empty();
	Lights.Empty();
	ParticleActors.Empty();

	GetWorld()->GetTimerManager().ClearTimer(LightSwitchTimer);
	GetWorld()->GetTimerManager().ClearTimer(ParticleTimer);

	UE_LOG(LogTemp, Error, TEXT("EndPlay Play"));
}

// Called every frame
void APSOLightController::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void APSOLightController::SwitchPrecomputedLightingState(uint32 NewState)
{
	GWorld->GetWorldSettings()->bForceNoPrecomputedLighting = static_cast<bool>(NewState);
}

void APSOLightController::SwitchPermutation()
{
	CurrentCombinationIndex++;
	UE_LOG(LogCore, Warning, TEXT("CALL APSOLightController::SwitchLights %x CurrentCombinationIndex %u"), this, CurrentCombinationIndex);
	FPermutationState NewState = CastStateFromInteger(CurrentCombinationIndex);
	if (NewState.DirectionLightState != CntPermutationState.DirectionLightState)
	{
		SwitchDirectionalLight(CntPermutationState.DirectionLightState);
	}
	if (NewState.LocalLightState != CntPermutationState.LocalLightState)
	{
		SwitchLocalLightState(CntPermutationState.LocalLightState);
	}
	if (NewState.SkyLightState != CntPermutationState.SkyLightState)
	{
		SwitchSkyLightState(CntPermutationState.SkyLightState);
	}
	if (NewState.SkinCacheState != CntPermutationState.SkinCacheState)
	{
		SwitchSkinCacheState(CntPermutationState.SkinCacheState);
	}
	if (NewState.PrecomputredLightingState != CntPermutationState.PrecomputredLightingState)
	{
		SwitchPrecomputedLightingState(CntPermutationState.PrecomputredLightingState);
	}
	if (NewState.CastShadowState != CntPermutationState.CastShadowState)
	{
		SwitchDirectionalCastShadow(CntPermutationState.CastShadowState);
	}

	CntPermutationState = NewState;
}

void APSOLightController::SwitchDirectionalLight(uint32 NewState)
{
	for (AActor* Light : Lights)
	{
		if (const auto DirectionalLight = Cast<ADirectionalLight>(Light))
		{
			DirectionalLight->SetActorHiddenInGame(static_cast<bool>(NewState));
		}
	}
}

void APSOLightController::SwitchLocalLightState(uint32 NewState)
{
	for (AActor* Light : Lights)
	{
		const auto PointLight = Cast<APointLight>(Light);
		const auto RectLight = Cast<ARectLight>(Light);
		const auto SpotLight = Cast<ASpotLight>(Light);
		if (PointLight)
		{
			PointLight->SetActorHiddenInGame(static_cast<bool>(NewState));
		}
		if (RectLight)
		{
			RectLight->SetActorHiddenInGame(static_cast<bool>(NewState));
		}
		if (SpotLight)
		{
			SpotLight->SetActorHiddenInGame(static_cast<bool>(NewState));
		}
	}
}


void APSOLightController::SwitchSkyLightState(uint32 NewState)
{
	for (AActor* Light : Lights)
	{
		if (const auto SkyLight = Cast<ASkyLight>(Light))
		{
			SkyLight->SetActorHiddenInGame(static_cast<bool>(NewState));
		}
	}
}

void APSOLightController::SwitchDirectionalCastShadow(uint32 NewState)
{
	for (AActor* Light : Lights)
	{
		if (const auto DirectionalLight = Cast<ADirectionalLight>(Light))
		{
			DirectionalLight->SetCastShadows(static_cast<bool>(NewState));
		}
	}

	for (TObjectIterator<UPrimitiveComponent> It; It; ++It)
	{
		if (It->GetWorld() == GetWorld())
		{
			It->bReceiveMobileCSMShadows = static_cast<bool>(NewState);
			It->SetCastShadow(static_cast<bool>(NewState));
		}
	}

	{
		FGlobalComponentRecreateRenderStateContext RecreateRenderState;
	}
}

void APSOLightController::SwitchSkinCacheState(uint32 NewState)
{
	APlayerController* PlayerController = UGameplayStatics::GetPlayerController(this, 0);
	FString command = FString::Printf(TEXT("r.SkinCache.Mode 1"));
	if (!static_cast<bool>(NewState))
	{
		command = FString::Printf(TEXT("r.SkinCache.Mode 0"));
	}
	PlayerController->ConsoleCommand(command, true);
	IConsoleManager::Get().CallAllConsoleVariableSinks();

	{
		//FGlobalComponentRecreateRenderStateContext RecreateRenderState;
	}
}

void APSOLightController::HideAllLights()
{
	for (AActor* Light : Lights)
	{
		Light->SetActorHiddenInGame(true);
	}
}

void APSOLightController::ReActiveParticleActors()
{
	for (AActor* ParticleActor : ParticleActors)
	{
		if (const auto NiagaraActor = Cast<ANiagaraActor>(ParticleActor))
		{
			const auto Component = NiagaraActor->GetNiagaraComponent();
			Component->Deactivate();
			Component->Activate(true);
		}
		else if (const auto Emitter = Cast<AEmitter>(ParticleActor))
		{
			const auto Component = Emitter->GetParticleSystemComponent();
			Component->Deactivate();
			Component->Activate(true);
		}
	}
}