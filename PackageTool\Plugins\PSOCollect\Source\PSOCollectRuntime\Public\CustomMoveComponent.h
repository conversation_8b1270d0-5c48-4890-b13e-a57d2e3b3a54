﻿// Copyright Xverse. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "CustomMoveComponent.generated.h"


UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class PSOCOLLECTRUNTIME_API UCustomMoveComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	UCustomMoveComponent();

	void SetMoveSpeedAndDistance(const float MoveSpeed, const float MoveDistance);

	void SetRotationSpeed(const float FRotationSpeed);

protected:
	// Called when the game starts
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;


private:
	FVector StartLocation;  // 初始位置
	TArray<FVector> PathCorners;  // 正方形的四个角点
	int32 CurrentEdgeIndex;  // 当前路径段的索引

	UPROPERTY(EditAnywhere)
	float Speed;  // 移动速度
	
	UPROPERTY(EditAnywhere)
	float Distance;
	
	FVector CurrentLocation;  // 当前的位置

	UPROPERTY(EditAnywhere)
	float RotationSpeed; 
};