﻿// Copyright Xverse. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "PSOLightController.generated.h"

#define PERMUTATION_STATE_COUNT 6
struct FPermutationState
{
	uint32 DirectionLightState; // 直射光
	uint32 SkyLightState; // 天光
	uint32 LocalLightState; // 点光类的光
	uint32 SkinCacheState; // skeletal mesh 类的状态, 会影响 VERTEX format 的permutation
	uint32 PrecomputredLightingState; // 开关预计算光照
	uint32 CastShadowState; // 直射光阴影
	
};

// 增加状态必须要更新这个 statecount, 2^PERMUTATION_STATE_COUNT 次的采样时间增长.
static_assert(sizeof(FPermutationState) == sizeof(uint32) * PERMUTATION_STATE_COUNT);
UCLASS()
class PSOCOLLECTRUNTIME_API APSOLightController : public AActor
{
	GENERATED_BODY()

	UPROPERTY()
	TArray<AActor*> AllActors;

	UPROPERTY()
	TArray<AActor*> Lights;

	FPermutationState CntPermutationState;

	// 当前组合索引
	uint32 CurrentCombinationIndex;

	FTimerHandle LightSwitchTimer;


	UPROPERTY()
	TArray<AActor*> ParticleActors;
	
	FTimerHandle ParticleTimer;

public:
	// Sets default values for this actor's properties
	APSOLightController();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

private:
	void SwitchDirectionalLight(uint32 NewState);
	void SwitchLocalLightState(uint32 NewState);
	void SwitchSkyLightState(uint32 NewState);
	void SwitchPrecomputedLightingState(uint32 NewState);
	void SwitchDirectionalCastShadow(uint32 NewState);
	void SwitchSkinCacheState(uint32 NewState);
	void SwitchPermutation();
	void HideAllLights();

	void ReActiveParticleActors();
};