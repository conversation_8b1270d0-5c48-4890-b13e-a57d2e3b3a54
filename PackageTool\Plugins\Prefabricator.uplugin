{"FileVersion": 3, "Version": 26, "VersionName": "1.7.1", "FriendlyName": "Prefabricator", "Description": "Create Prefabs in Unreal Engine", "Category": "Prefab", "CreatedBy": "Code Respawn", "CreatedByURL": "http://prefabricator.dev", "DocsURL": "http://docs.prefabricator.dev", "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/content/39ccbbf668e84847890992df9922e355", "EnabledByDefault": true, "CanContainContent": true, "Modules": [{"Name": "PrefabricatorRuntime", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "PrefabricatorEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "PrefabricatorEditorPostInit", "Type": "Editor", "LoadingPhase": "PostEngineInit"}, {"Name": "ConstructionSystemRuntime", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "ConstructionSystemEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}]}