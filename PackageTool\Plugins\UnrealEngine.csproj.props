<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- net6.0 projects are not fully supported in Visual Studio 2019 - projects will build, but there will be warnings that we suppress here -->
  <PropertyGroup Condition="'$(VisualStudioVersion)'=='16.0'">
    <NoWarn>NETSDK1182;CS8032</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <ProduceReferenceAssemblyInOutDir>true</ProduceReferenceAssemblyInOutDir>
  </PropertyGroup>
</Project>
 