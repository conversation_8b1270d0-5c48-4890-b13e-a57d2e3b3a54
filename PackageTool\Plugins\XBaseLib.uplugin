{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "XBaseLib", "Category": "XVerse", "CreatedBy": "", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": false, "EnabledByDefault": true, "Modules": [{"Name": "GenericStorages", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "GMP", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "GMPEditor", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON>efault", "WhitelistPlatforms": ["Win64", "Linux", "<PERSON>"]}, {"Name": "MessageTags", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON>efault", "WhitelistPlatforms": ["Win64", "Linux", "<PERSON>"]}, {"Name": "MessageTagsEditor", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON>efault", "WhitelistPlatforms": ["Win64", "Linux", "<PERSON>"]}, {"Name": "GameplayBase", "Type": "Runtime", "LoadingPhase": "PreLoadingScreen"}, {"Name": "JsonLibrary", "Type": "Runtime", "LoadingPhase": "PreLoadingScreen"}, {"Name": "Resource<PERSON><PERSON>der", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "ProjectItem", "Type": "Runtime", "LoadingPhase": "PreLoadingScreen"}, {"Name": "MultiProjectSupports", "Type": "Editor", "LoadingPhase": "PreLoadingScreen"}], "Plugins": [{"Name": "AssetManagerEditor", "Enabled": true}, {"Name": "GameplayTagsEditor", "Enabled": true}, {"Name": "Niagara", "Enabled": true}, {"Name": "PythonScriptPlugin", "Enabled": true, "WhitelistTargets": ["Editor"]}]}