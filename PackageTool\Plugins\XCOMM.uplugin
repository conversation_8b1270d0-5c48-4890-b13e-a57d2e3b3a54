{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "XVerse Communication", "Category": "XVerse", "Description": "Communication utilities", "CreatedBy": "", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "EnabledByDefault": true, "Modules": [{"Name": "XDataInterface", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "XverseConsoleApi", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "XverseCOSApi", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "XExportFbx", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ExportNavRuntime", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "XBaseLib", "Enabled": true}, {"Name": "Protobuf", "Enabled": true}, {"Name": "ProceduralMeshComponent", "Enabled": true}]}