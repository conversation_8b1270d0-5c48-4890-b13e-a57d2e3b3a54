language: c

sudo: false

cache:
  timeout: 1000
  directories:
    - $HOME/cpu_features_archives

addons:
  apt_packages:
    - ninja-build

env:
  global:
    TOOLCHAIN=NATIVE
    CMAKE_GENERATOR=Ninja

matrix:
  include:
  - os: linux
    compiler: gcc
    env:
      TARGET=x86_64-linux-gnu
  - os: linux
    compiler: clang
    env:
      TARGET=x86_64-linux-gnu
  - os: osx
    compiler: gcc
    env:
      TARGET=x86_64-osx
      CMAKE_GENERATOR="Unix Makefiles"
  - os: osx
    compiler: clang
    env:
      TARGET=x86_64-osx
      CMAKE_GENERATOR="Unix Makefiles"
  - os: windows
    env:
      TARGET=x86_64-windows
      CMAKE_GENERATOR="Visual Studio 15 2017 Win64"

  # see: https://docs.travis-ci.com/user/multi-cpu-architectures/
  - os: linux
    arch: ppc64le
    compiler: gcc
    env:
      TARGET=ppc64le-linux-gnu
  - os: linux
    arch: ppc64le
    compiler: clang
    env:
      TARGET=ppc64le-linux-gnu

  # Toolchains for little-endian, 64-bit ARMv8 for GNU/Linux systems
  - os: linux
    env:
      TOOLCHAIN=LINARO
      TARGET=aarch64-linux-gnu
      QEMU_ARCH=aarch64
  # Toolchains for little-endian, hard-float, 32-bit ARMv7 (and earlier) for GNU/Linux systems
  - os: linux
    env:
      TOOLCHAIN=LINARO
      TARGET=arm-linux-gnueabihf
      QEMU_ARCH=arm
  # Toolchains for little-endian, 32-bit ARMv8 for GNU/Linux systems
  - os: linux
    env:
      TOOLCHAIN=LINARO
      TARGET=armv8l-linux-gnueabihf
      QEMU_ARCH=arm
  # Toolchains for little-endian, soft-float, 32-bit ARMv7 (and earlier) for GNU/Linux systems
  - os: linux
    env:
      TOOLCHAIN=LINARO
      TARGET=arm-linux-gnueabi
      QEMU_ARCH=arm
  # Toolchains for big-endian, 64-bit ARMv8 for GNU/Linux systems
  - os: linux
    env:
      TOOLCHAIN=LINARO
      TARGET=aarch64_be-linux-gnu
      QEMU_ARCH=DISABLED
  # Toolchains for big-endian, hard-float, 32-bit ARMv7 (and earlier) for GNU/Linux systems
  - os: linux
    env:
      TOOLCHAIN=LINARO
      TARGET=armeb-linux-gnueabihf
      QEMU_ARCH=DISABLED
  # Toolchains for big-endian, soft-float, 32-bit ARMv7 (and earlier) for GNU/Linux systems
  - os: linux
    env:
      TOOLCHAIN=LINARO
      TARGET=armeb-linux-gnueabi
      QEMU_ARCH=DISABLED
  - os: linux
    env:
      TOOLCHAIN=CODESCAPE
      TARGET=mips32
      QEMU_ARCH=mips
  - os: linux
    env:
      TOOLCHAIN=CODESCAPE
      TARGET=mips32el
      QEMU_ARCH=mipsel
  - os: linux
    env:
      TOOLCHAIN=CODESCAPE
      TARGET=mips64
      QEMU_ARCH=mips64
  - os: linux
    env:
      TOOLCHAIN=CODESCAPE
      TARGET=mips64el
      QEMU_ARCH=mips64el

script:
  - cmake --version
  - bash -e -x ./scripts/run_integration.sh
