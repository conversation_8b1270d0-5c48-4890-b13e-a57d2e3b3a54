//
// Created by s<PERSON><PERSON><PERSON> on 6/29/21.
//

#ifndef CRC64_TABLE_HPP
#define CRC64_TABLE_HPP

#include <array>
#include <cstddef>
#include <cstdint>

namespace crc64::detail
{
  static inline const uint64_t K_127 = 0xdabe'95af'c787'5f40;
  static inline const uint64_t K_191 = 0xe05d'd497'ca39'3ae4;
  static inline const uint64_t K_255 = 0x3be6'53a3'0fe1'af51;
  static inline const uint64_t K_319 = 0x6009'5b00'8a9e'fa44;
  static inline const uint64_t K_383 = 0x69a3'5d91'c373'0254;
  static inline const uint64_t K_447 = 0xb5ea'1af9'c013'aca4;
  static inline const uint64_t K_511 = 0x081f'6054'a784'2df4;
  static inline const uint64_t K_575 = 0x6ae3'efbb'9dd4'41f3;
  static inline const uint64_t K_639 = 0x0e31'd519'421a'63a5;
  static inline const uint64_t K_703 = 0x2e30'2032'12ca'c325;
  static inline const uint64_t K_767 = 0xe4ce'2cd5'5fea'0037;
  static inline const uint64_t K_831 = 0x2fe3'fd29'20ce'82ec;
  static inline const uint64_t K_895 = 0x9478'74de'5950'52cb;
  static inline const uint64_t K_959 = 0x9e73'5cb5'9b47'24da;
  static inline const uint64_t K_1023 = 0xd7d8'6b2a'f73d'e740;
  static inline const uint64_t K_1087 = 0x8757'd71d'4fcc'1000;

  static inline const uint64_t POLY = 0x92d8'af2b'af0e'1e85;
  static inline const uint64_t MU = 0x9c3e'466c'1729'63d5;

  // Essentially, `TABLE_0[m]` is the polynomial remainder in GF(2) when `m`
  // (`1 = x⁷¹, 2 = x⁷⁰, 4 = x⁶⁹, …, 128 = x⁶⁴`) is divided by the ECMA poly.
  static inline const uint64_t TABLE_0[256] = {
    0x0000000000000000, 0xb32e4cbe03a75f6f, 0xf4843657a840a05b,
    0x47aa7ae9abe7ff34, 0x7bd0c384ff8f5e33, 0xc8fe8f3afc28015c,
    0x8f54f5d357cffe68, 0x3c7ab96d5468a107, 0xf7a18709ff1ebc66,
    0x448fcbb7fcb9e309, 0x0325b15e575e1c3d, 0xb00bfde054f94352,
    0x8c71448d0091e255, 0x3f5f08330336bd3a, 0x78f572daa8d1420e,
    0xcbdb3e64ab761d61, 0x7d9ba13851336649, 0xceb5ed8652943926,
    0x891f976ff973c612, 0x3a31dbd1fad4997d, 0x064b62bcaebc387a,
    0xb5652e02ad1b6715, 0xf2cf54eb06fc9821, 0x41e11855055bc74e,
    0x8a3a2631ae2dda2f, 0x39146a8fad8a8540, 0x7ebe1066066d7a74,
    0xcd905cd805ca251b, 0xf1eae5b551a2841c, 0x42c4a90b5205db73,
    0x056ed3e2f9e22447, 0xb6409f5cfa457b28, 0xfb374270a266cc92,
    0x48190ecea1c193fd, 0x0fb374270a266cc9, 0xbc9d3899098133a6,
    0x80e781f45de992a1, 0x33c9cd4a5e4ecdce, 0x7463b7a3f5a932fa,
    0xc74dfb1df60e6d95, 0x0c96c5795d7870f4, 0xbfb889c75edf2f9b,
    0xf812f32ef538d0af, 0x4b3cbf90f69f8fc0, 0x774606fda2f72ec7,
    0xc4684a43a15071a8, 0x83c230aa0ab78e9c, 0x30ec7c140910d1f3,
    0x86ace348f355aadb, 0x3582aff6f0f2f5b4, 0x7228d51f5b150a80,
    0xc10699a158b255ef, 0xfd7c20cc0cdaf4e8, 0x4e526c720f7dab87,
    0x09f8169ba49a54b3, 0xbad65a25a73d0bdc, 0x710d64410c4b16bd,
    0xc22328ff0fec49d2, 0x85895216a40bb6e6, 0x36a71ea8a7ace989,
    0x0adda7c5f3c4488e, 0xb9f3eb7bf06317e1, 0xfe5991925b84e8d5,
    0x4d77dd2c5823b7ba, 0x64b62bcaebc387a1, 0xd7986774e864d8ce,
    0x90321d9d438327fa, 0x231c512340247895, 0x1f66e84e144cd992,
    0xac48a4f017eb86fd, 0xebe2de19bc0c79c9, 0x58cc92a7bfab26a6,
    0x9317acc314dd3bc7, 0x2039e07d177a64a8, 0x67939a94bc9d9b9c,
    0xd4bdd62abf3ac4f3, 0xe8c76f47eb5265f4, 0x5be923f9e8f53a9b,
    0x1c4359104312c5af, 0xaf6d15ae40b59ac0, 0x192d8af2baf0e1e8,
    0xaa03c64cb957be87, 0xeda9bca512b041b3, 0x5e87f01b11171edc,
    0x62fd4976457fbfdb, 0xd1d305c846d8e0b4, 0x96797f21ed3f1f80,
    0x2557339fee9840ef, 0xee8c0dfb45ee5d8e, 0x5da24145464902e1,
    0x1a083bacedaefdd5, 0xa9267712ee09a2ba, 0x955cce7fba6103bd,
    0x267282c1b9c65cd2, 0x61d8f8281221a3e6, 0xd2f6b4961186fc89,
    0x9f8169ba49a54b33, 0x2caf25044a02145c, 0x6b055fede1e5eb68,
    0xd82b1353e242b407, 0xe451aa3eb62a1500, 0x577fe680b58d4a6f,
    0x10d59c691e6ab55b, 0xa3fbd0d71dcdea34, 0x6820eeb3b6bbf755,
    0xdb0ea20db51ca83a, 0x9ca4d8e41efb570e, 0x2f8a945a1d5c0861,
    0x13f02d374934a966, 0xa0de61894a93f609, 0xe7741b60e174093d,
    0x545a57dee2d35652, 0xe21ac88218962d7a, 0x5134843c1b317215,
    0x169efed5b0d68d21, 0xa5b0b26bb371d24e, 0x99ca0b06e7197349,
    0x2ae447b8e4be2c26, 0x6d4e3d514f59d312, 0xde6071ef4cfe8c7d,
    0x15bb4f8be788911c, 0xa6950335e42fce73, 0xe13f79dc4fc83147,
    0x521135624c6f6e28, 0x6e6b8c0f1807cf2f, 0xdd45c0b11ba09040,
    0x9aefba58b0476f74, 0x29c1f6e6b3e0301b, 0xc96c5795d7870f42,
    0x7a421b2bd420502d, 0x3de861c27fc7af19, 0x8ec62d7c7c60f076,
    0xb2bc941128085171, 0x0192d8af2baf0e1e, 0x4638a2468048f12a,
    0xf516eef883efae45, 0x3ecdd09c2899b324, 0x8de39c222b3eec4b,
    0xca49e6cb80d9137f, 0x7967aa75837e4c10, 0x451d1318d716ed17,
    0xf6335fa6d4b1b278, 0xb199254f7f564d4c, 0x02b769f17cf11223,
    0xb4f7f6ad86b4690b, 0x07d9ba1385133664, 0x4073c0fa2ef4c950,
    0xf35d8c442d53963f, 0xcf273529793b3738, 0x7c0979977a9c6857,
    0x3ba3037ed17b9763, 0x888d4fc0d2dcc80c, 0x435671a479aad56d,
    0xf0783d1a7a0d8a02, 0xb7d247f3d1ea7536, 0x04fc0b4dd24d2a59,
    0x3886b22086258b5e, 0x8ba8fe9e8582d431, 0xcc0284772e652b05,
    0x7f2cc8c92dc2746a, 0x325b15e575e1c3d0, 0x8175595b76469cbf,
    0xc6df23b2dda1638b, 0x75f16f0cde063ce4, 0x498bd6618a6e9de3,
    0xfaa59adf89c9c28c, 0xbd0fe036222e3db8, 0x0e21ac88218962d7,
    0xc5fa92ec8aff7fb6, 0x76d4de52895820d9, 0x317ea4bb22bfdfed,
    0x8250e80521188082, 0xbe2a516875702185, 0x0d041dd676d77eea,
    0x4aae673fdd3081de, 0xf9802b81de97deb1, 0x4fc0b4dd24d2a599,
    0xfceef8632775faf6, 0xbb44828a8c9205c2, 0x086ace348f355aad,
    0x34107759db5dfbaa, 0x873e3be7d8faa4c5, 0xc094410e731d5bf1,
    0x73ba0db070ba049e, 0xb86133d4dbcc19ff, 0x0b4f7f6ad86b4690,
    0x4ce50583738cb9a4, 0xffcb493d702be6cb, 0xc3b1f050244347cc,
    0x709fbcee27e418a3, 0x3735c6078c03e797, 0x841b8ab98fa4b8f8,
    0xadda7c5f3c4488e3, 0x1ef430e13fe3d78c, 0x595e4a08940428b8,
    0xea7006b697a377d7, 0xd60abfdbc3cbd6d0, 0x6524f365c06c89bf,
    0x228e898c6b8b768b, 0x91a0c532682c29e4, 0x5a7bfb56c35a3485,
    0xe955b7e8c0fd6bea, 0xaeffcd016b1a94de, 0x1dd181bf68bdcbb1,
    0x21ab38d23cd56ab6, 0x9285746c3f7235d9, 0xd52f0e859495caed,
    0x6601423b97329582, 0xd041dd676d77eeaa, 0x636f91d96ed0b1c5,
    0x24c5eb30c5374ef1, 0x97eba78ec690119e, 0xab911ee392f8b099,
    0x18bf525d915feff6, 0x5f1528b43ab810c2, 0xec3b640a391f4fad,
    0x27e05a6e926952cc, 0x94ce16d091ce0da3, 0xd3646c393a29f297,
    0x604a2087398eadf8, 0x5c3099ea6de60cff, 0xef1ed5546e415390,
    0xa8b4afbdc5a6aca4, 0x1b9ae303c601f3cb, 0x56ed3e2f9e224471,
    0xe5c372919d851b1e, 0xa26908783662e42a, 0x114744c635c5bb45,
    0x2d3dfdab61ad1a42, 0x9e13b115620a452d, 0xd9b9cbfcc9edba19,
    0x6a978742ca4ae576, 0xa14cb926613cf817, 0x1262f598629ba778,
    0x55c88f71c97c584c, 0xe6e6c3cfcadb0723, 0xda9c7aa29eb3a624,
    0x69b2361c9d14f94b, 0x2e184cf536f3067f, 0x9d36004b35545910,
    0x2b769f17cf112238, 0x9858d3a9ccb67d57, 0xdff2a94067518263,
    0x6cdce5fe64f6dd0c, 0x50a65c93309e7c0b, 0xe388102d33392364,
    0xa4226ac498dedc50, 0x170c267a9b79833f, 0xdcd7181e300f9e5e,
    0x6ff954a033a8c131, 0x28532e49984f3e05, 0x9b7d62f79be8616a,
    0xa707db9acf80c06d, 0x14299724cc279f02, 0x5383edcd67c06036,
    0xe0ada17364673f59,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x⁷⁹, …, 128 = x⁷²`).
  // Generated by running `./build_table 1`.
  static inline const uint64_t TABLE_1[256] = {
    0x0000000000000000, 0x54e979925cd0f10d, 0xa9d2f324b9a1e21a,
    0xfd3b8ab6e5711317, 0xc17d4962dc4ddab1, 0x959430f0809d2bbc,
    0x68afba4665ec38ab, 0x3c46c3d4393cc9a6, 0x10223dee1795abe7,
    0x44cb447c4b455aea, 0xb9f0cecaae3449fd, 0xed19b758f2e4b8f0,
    0xd15f748ccbd87156, 0x85b60d1e9708805b, 0x788d87a87279934c,
    0x2c64fe3a2ea96241, 0x20447bdc2f2b57ce, 0x74ad024e73fba6c3,
    0x899688f8968ab5d4, 0xdd7ff16aca5a44d9, 0xe13932bef3668d7f,
    0xb5d04b2cafb67c72, 0x48ebc19a4ac76f65, 0x1c02b80816179e68,
    0x3066463238befc29, 0x648f3fa0646e0d24, 0x99b4b516811f1e33,
    0xcd5dcc84ddcfef3e, 0xf11b0f50e4f32698, 0xa5f276c2b823d795,
    0x58c9fc745d52c482, 0x0c2085e60182358f, 0x4088f7b85e56af9c,
    0x14618e2a02865e91, 0xe95a049ce7f74d86, 0xbdb37d0ebb27bc8b,
    0x81f5beda821b752d, 0xd51cc748decb8420, 0x28274dfe3bba9737,
    0x7cce346c676a663a, 0x50aaca5649c3047b, 0x0443b3c41513f576,
    0xf9783972f062e661, 0xad9140e0acb2176c, 0x91d78334958edeca,
    0xc53efaa6c95e2fc7, 0x380570102c2f3cd0, 0x6cec098270ffcddd,
    0x60cc8c64717df852, 0x3425f5f62dad095f, 0xc91e7f40c8dc1a48,
    0x9df706d2940ceb45, 0xa1b1c506ad3022e3, 0xf558bc94f1e0d3ee,
    0x086336221491c0f9, 0x5c8a4fb0484131f4, 0x70eeb18a66e853b5,
    0x2407c8183a38a2b8, 0xd93c42aedf49b1af, 0x8dd53b3c839940a2,
    0xb193f8e8baa58904, 0xe57a817ae6757809, 0x18410bcc03046b1e,
    0x4ca8725e5fd49a13, 0x8111ef70bcad5f38, 0xd5f896e2e07dae35,
    0x28c31c54050cbd22, 0x7c2a65c659dc4c2f, 0x406ca61260e08589,
    0x1485df803c307484, 0xe9be5536d9416793, 0xbd572ca48591969e,
    0x9133d29eab38f4df, 0xc5daab0cf7e805d2, 0x38e121ba129916c5,
    0x6c0858284e49e7c8, 0x504e9bfc77752e6e, 0x04a7e26e2ba5df63,
    0xf99c68d8ced4cc74, 0xad75114a92043d79, 0xa15594ac938608f6,
    0xf5bced3ecf56f9fb, 0x088767882a27eaec, 0x5c6e1e1a76f71be1,
    0x6028ddce4fcbd247, 0x34c1a45c131b234a, 0xc9fa2eeaf66a305d,
    0x9d135778aabac150, 0xb177a9428413a311, 0xe59ed0d0d8c3521c,
    0x18a55a663db2410b, 0x4c4c23f46162b006, 0x700ae020585e79a0,
    0x24e399b2048e88ad, 0xd9d81304e1ff9bba, 0x8d316a96bd2f6ab7,
    0xc19918c8e2fbf0a4, 0x9570615abe2b01a9, 0x684bebec5b5a12be,
    0x3ca2927e078ae3b3, 0x00e451aa3eb62a15, 0x540d28386266db18,
    0xa936a28e8717c80f, 0xfddfdb1cdbc73902, 0xd1bb2526f56e5b43,
    0x85525cb4a9beaa4e, 0x7869d6024ccfb959, 0x2c80af90101f4854,
    0x10c66c44292381f2, 0x442f15d675f370ff, 0xb9149f60908263e8,
    0xedfde6f2cc5292e5, 0xe1dd6314cdd0a76a, 0xb5341a8691005667,
    0x480f903074714570, 0x1ce6e9a228a1b47d, 0x20a02a76119d7ddb,
    0x744953e44d4d8cd6, 0x8972d952a83c9fc1, 0xdd9ba0c0f4ec6ecc,
    0xf1ff5efada450c8d, 0xa51627688695fd80, 0x582dadde63e4ee97,
    0x0cc4d44c3f341f9a, 0x308217980608d63c, 0x646b6e0a5ad82731,
    0x9950e4bcbfa93426, 0xcdb99d2ee379c52b, 0x90fb71cad654a0f5,
    0xc41208588a8451f8, 0x392982ee6ff542ef, 0x6dc0fb7c3325b3e2,
    0x518638a80a197a44, 0x056f413a56c98b49, 0xf854cb8cb3b8985e,
    0xacbdb21eef686953, 0x80d94c24c1c10b12, 0xd43035b69d11fa1f,
    0x290bbf007860e908, 0x7de2c69224b01805, 0x41a405461d8cd1a3,
    0x154d7cd4415c20ae, 0xe876f662a42d33b9, 0xbc9f8ff0f8fdc2b4,
    0xb0bf0a16f97ff73b, 0xe4567384a5af0636, 0x196df93240de1521,
    0x4d8480a01c0ee42c, 0x71c2437425322d8a, 0x252b3ae679e2dc87,
    0xd810b0509c93cf90, 0x8cf9c9c2c0433e9d, 0xa09d37f8eeea5cdc,
    0xf4744e6ab23aadd1, 0x094fc4dc574bbec6, 0x5da6bd4e0b9b4fcb,
    0x61e07e9a32a7866d, 0x350907086e777760, 0xc8328dbe8b066477,
    0x9cdbf42cd7d6957a, 0xd073867288020f69, 0x849affe0d4d2fe64,
    0x79a1755631a3ed73, 0x2d480cc46d731c7e, 0x110ecf10544fd5d8,
    0x45e7b682089f24d5, 0xb8dc3c34edee37c2, 0xec3545a6b13ec6cf,
    0xc051bb9c9f97a48e, 0x94b8c20ec3475583, 0x698348b826364694,
    0x3d6a312a7ae6b799, 0x012cf2fe43da7e3f, 0x55c58b6c1f0a8f32,
    0xa8fe01dafa7b9c25, 0xfc177848a6ab6d28, 0xf037fdaea72958a7,
    0xa4de843cfbf9a9aa, 0x59e50e8a1e88babd, 0x0d0c771842584bb0,
    0x314ab4cc7b648216, 0x65a3cd5e27b4731b, 0x989847e8c2c5600c,
    0xcc713e7a9e159101, 0xe015c040b0bcf340, 0xb4fcb9d2ec6c024d,
    0x49c73364091d115a, 0x1d2e4af655cde057, 0x216889226cf129f1,
    0x7581f0b03021d8fc, 0x88ba7a06d550cbeb, 0xdc53039489803ae6,
    0x11ea9eba6af9ffcd, 0x4503e72836290ec0, 0xb8386d9ed3581dd7,
    0xecd1140c8f88ecda, 0xd097d7d8b6b4257c, 0x847eae4aea64d471,
    0x794524fc0f15c766, 0x2dac5d6e53c5366b, 0x01c8a3547d6c542a,
    0x5521dac621bca527, 0xa81a5070c4cdb630, 0xfcf329e2981d473d,
    0xc0b5ea36a1218e9b, 0x945c93a4fdf17f96, 0x6967191218806c81,
    0x3d8e608044509d8c, 0x31aee56645d2a803, 0x65479cf41902590e,
    0x987c1642fc734a19, 0xcc956fd0a0a3bb14, 0xf0d3ac04999f72b2,
    0xa43ad596c54f83bf, 0x59015f20203e90a8, 0x0de826b27cee61a5,
    0x218cd888524703e4, 0x7565a11a0e97f2e9, 0x885e2bacebe6e1fe,
    0xdcb7523eb73610f3, 0xe0f191ea8e0ad955, 0xb418e878d2da2858,
    0x492362ce37ab3b4f, 0x1dca1b5c6b7bca42, 0x5162690234af5051,
    0x058b1090687fa15c, 0xf8b09a268d0eb24b, 0xac59e3b4d1de4346,
    0x901f2060e8e28ae0, 0xc4f659f2b4327bed, 0x39cdd344514368fa,
    0x6d24aad60d9399f7, 0x414054ec233afbb6, 0x15a92d7e7fea0abb,
    0xe892a7c89a9b19ac, 0xbc7bde5ac64be8a1, 0x803d1d8eff772107,
    0xd4d4641ca3a7d00a, 0x29efeeaa46d6c31d, 0x7d0697381a063210,
    0x712612de1b84079f, 0x25cf6b4c4754f692, 0xd8f4e1faa225e585,
    0x8c1d9868fef51488, 0xb05b5bbcc7c9dd2e, 0xe4b2222e9b192c23,
    0x1989a8987e683f34, 0x4d60d10a22b8ce39, 0x61042f300c11ac78,
    0x35ed56a250c15d75, 0xc8d6dc14b5b04e62, 0x9c3fa586e960bf6f,
    0xa0796652d05c76c9, 0xf4901fc08c8c87c4, 0x09ab957669fd94d3,
    0x5d42ece4352d65de,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x⁸⁷, …, 128 = x⁸⁰`).
  // Generated by running `./build_table 2`.
  static inline const uint64_t TABLE_2[256] = {
    0x0000000000000000, 0x3f0be14a916a6dcb, 0x7e17c29522d4db96,
    0x411c23dfb3beb65d, 0xfc2f852a45a9b72c, 0xc3246460d4c3dae7,
    0x823847bf677d6cba, 0xbd33a6f5f6170171, 0x6a87a57f245d70dd,
    0x558c4435b5371d16, 0x149067ea0689ab4b, 0x2b9b86a097e3c680,
    0x96a8205561f4c7f1, 0xa9a3c11ff09eaa3a, 0xe8bfe2c043201c67,
    0xd7b4038ad24a71ac, 0xd50f4afe48bae1ba, 0xea04abb4d9d08c71,
    0xab18886b6a6e3a2c, 0x94136921fb0457e7, 0x2920cfd40d135696,
    0x162b2e9e9c793b5d, 0x57370d412fc78d00, 0x683cec0bbeade0cb,
    0xbf88ef816ce79167, 0x80830ecbfd8dfcac, 0xc19f2d144e334af1,
    0xfe94cc5edf59273a, 0x43a76aab294e264b, 0x7cac8be1b8244b80,
    0x3db0a83e0b9afddd, 0x02bb49749af09016, 0x38c63ad73e7bddf1,
    0x07cddb9daf11b03a, 0x46d1f8421caf0667, 0x79da19088dc56bac,
    0xc4e9bffd7bd26add, 0xfbe25eb7eab80716, 0xbafe7d685906b14b,
    0x85f59c22c86cdc80, 0x52419fa81a26ad2c, 0x6d4a7ee28b4cc0e7,
    0x2c565d3d38f276ba, 0x135dbc77a9981b71, 0xae6e1a825f8f1a00,
    0x9165fbc8cee577cb, 0xd079d8177d5bc196, 0xef72395dec31ac5d,
    0xedc9702976c13c4b, 0xd2c29163e7ab5180, 0x93deb2bc5415e7dd,
    0xacd553f6c57f8a16, 0x11e6f50333688b67, 0x2eed1449a202e6ac,
    0x6ff1379611bc50f1, 0x50fad6dc80d63d3a, 0x874ed556529c4c96,
    0xb845341cc3f6215d, 0xf95917c370489700, 0xc652f689e122facb,
    0x7b61507c1735fbba, 0x446ab136865f9671, 0x057692e935e1202c,
    0x3a7d73a3a48b4de7, 0x718c75ae7cf7bbe2, 0x4e8794e4ed9dd629,
    0x0f9bb73b5e236074, 0x30905671cf490dbf, 0x8da3f084395e0cce,
    0xb2a811cea8346105, 0xf3b432111b8ad758, 0xccbfd35b8ae0ba93,
    0x1b0bd0d158aacb3f, 0x2400319bc9c0a6f4, 0x651c12447a7e10a9,
    0x5a17f30eeb147d62, 0xe72455fb1d037c13, 0xd82fb4b18c6911d8,
    0x9933976e3fd7a785, 0xa6387624aebdca4e, 0xa4833f50344d5a58,
    0x9b88de1aa5273793, 0xda94fdc5169981ce, 0xe59f1c8f87f3ec05,
    0x58acba7a71e4ed74, 0x67a75b30e08e80bf, 0x26bb78ef533036e2,
    0x19b099a5c25a5b29, 0xce049a2f10102a85, 0xf10f7b65817a474e,
    0xb01358ba32c4f113, 0x8f18b9f0a3ae9cd8, 0x322b1f0555b99da9,
    0x0d20fe4fc4d3f062, 0x4c3cdd90776d463f, 0x73373cdae6072bf4,
    0x494a4f79428c6613, 0x7641ae33d3e60bd8, 0x375d8dec6058bd85,
    0x08566ca6f132d04e, 0xb565ca530725d13f, 0x8a6e2b19964fbcf4,
    0xcb7208c625f10aa9, 0xf479e98cb49b6762, 0x23cdea0666d116ce,
    0x1cc60b4cf7bb7b05, 0x5dda28934405cd58, 0x62d1c9d9d56fa093,
    0xdfe26f2c2378a1e2, 0xe0e98e66b212cc29, 0xa1f5adb901ac7a74,
    0x9efe4cf390c617bf, 0x9c4505870a3687a9, 0xa34ee4cd9b5cea62,
    0xe252c71228e25c3f, 0xdd592658b98831f4, 0x606a80ad4f9f3085,
    0x5f6161e7def55d4e, 0x1e7d42386d4beb13, 0x2176a372fc2186d8,
    0xf6c2a0f82e6bf774, 0xc9c941b2bf019abf, 0x88d5626d0cbf2ce2,
    0xb7de83279dd54129, 0x0aed25d26bc24058, 0x35e6c498faa82d93,
    0x74fae74749169bce, 0x4bf1060dd87cf605, 0xe318eb5cf9ef77c4,
    0xdc130a1668851a0f, 0x9d0f29c9db3bac52, 0xa204c8834a51c199,
    0x1f376e76bc46c0e8, 0x203c8f3c2d2cad23, 0x6120ace39e921b7e,
    0x5e2b4da90ff876b5, 0x899f4e23ddb20719, 0xb694af694cd86ad2,
    0xf7888cb6ff66dc8f, 0xc8836dfc6e0cb144, 0x75b0cb09981bb035,
    0x4abb2a430971ddfe, 0x0ba7099cbacf6ba3, 0x34ace8d62ba50668,
    0x3617a1a2b155967e, 0x091c40e8203ffbb5, 0x4800633793814de8,
    0x770b827d02eb2023, 0xca382488f4fc2152, 0xf533c5c265964c99,
    0xb42fe61dd628fac4, 0x8b2407574742970f, 0x5c9004dd9508e6a3,
    0x639be59704628b68, 0x2287c648b7dc3d35, 0x1d8c270226b650fe,
    0xa0bf81f7d0a1518f, 0x9fb460bd41cb3c44, 0xdea84362f2758a19,
    0xe1a3a228631fe7d2, 0xdbded18bc794aa35, 0xe4d530c156fec7fe,
    0xa5c9131ee54071a3, 0x9ac2f254742a1c68, 0x27f154a1823d1d19,
    0x18fab5eb135770d2, 0x59e69634a0e9c68f, 0x66ed777e3183ab44,
    0xb15974f4e3c9dae8, 0x8e5295be72a3b723, 0xcf4eb661c11d017e,
    0xf045572b50776cb5, 0x4d76f1dea6606dc4, 0x727d1094370a000f,
    0x3361334b84b4b652, 0x0c6ad20115dedb99, 0x0ed19b758f2e4b8f,
    0x31da7a3f1e442644, 0x70c659e0adfa9019, 0x4fcdb8aa3c90fdd2,
    0xf2fe1e5fca87fca3, 0xcdf5ff155bed9168, 0x8ce9dccae8532735,
    0xb3e23d8079394afe, 0x64563e0aab733b52, 0x5b5ddf403a195699,
    0x1a41fc9f89a7e0c4, 0x254a1dd518cd8d0f, 0x9879bb20eeda8c7e,
    0xa7725a6a7fb0e1b5, 0xe66e79b5cc0e57e8, 0xd96598ff5d643a23,
    0x92949ef28518cc26, 0xad9f7fb81472a1ed, 0xec835c67a7cc17b0,
    0xd388bd2d36a67a7b, 0x6ebb1bd8c0b17b0a, 0x51b0fa9251db16c1,
    0x10acd94de265a09c, 0x2fa73807730fcd57, 0xf8133b8da145bcfb,
    0xc718dac7302fd130, 0x8604f9188391676d, 0xb90f185212fb0aa6,
    0x043cbea7e4ec0bd7, 0x3b375fed7586661c, 0x7a2b7c32c638d041,
    0x45209d785752bd8a, 0x479bd40ccda22d9c, 0x789035465cc84057,
    0x398c1699ef76f60a, 0x0687f7d37e1c9bc1, 0xbbb45126880b9ab0,
    0x84bfb06c1961f77b, 0xc5a393b3aadf4126, 0xfaa872f93bb52ced,
    0x2d1c7173e9ff5d41, 0x121790397895308a, 0x530bb3e6cb2b86d7,
    0x6c0052ac5a41eb1c, 0xd133f459ac56ea6d, 0xee3815133d3c87a6,
    0xaf2436cc8e8231fb, 0x902fd7861fe85c30, 0xaa52a425bb6311d7,
    0x9559456f2a097c1c, 0xd44566b099b7ca41, 0xeb4e87fa08dda78a,
    0x567d210ffecaa6fb, 0x6976c0456fa0cb30, 0x286ae39adc1e7d6d,
    0x176102d04d7410a6, 0xc0d5015a9f3e610a, 0xffdee0100e540cc1,
    0xbec2c3cfbdeaba9c, 0x81c922852c80d757, 0x3cfa8470da97d626,
    0x03f1653a4bfdbbed, 0x42ed46e5f8430db0, 0x7de6a7af6929607b,
    0x7f5deedbf3d9f06d, 0x40560f9162b39da6, 0x014a2c4ed10d2bfb,
    0x3e41cd0440674630, 0x83726bf1b6704741, 0xbc798abb271a2a8a,
    0xfd65a96494a49cd7, 0xc26e482e05cef11c, 0x15da4ba4d78480b0,
    0x2ad1aaee46eeed7b, 0x6bcd8931f5505b26, 0x54c6687b643a36ed,
    0xe9f5ce8e922d379c, 0xd6fe2fc403475a57, 0x97e20c1bb0f9ec0a,
    0xa8e9ed51219381c1,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x⁹⁵, …, 128 = x⁸⁸`).
  // Generated by running `./build_table 3`.
  static inline const uint64_t TABLE_3[256] = {
    0x0000000000000000, 0x1dee8a5e222ca1dc, 0x3bdd14bc445943b8,
    0x26339ee26675e264, 0x77ba297888b28770, 0x6a54a326aa9e26ac,
    0x4c673dc4ccebc4c8, 0x5189b79aeec76514, 0xef7452f111650ee0,
    0xf29ad8af3349af3c, 0xd4a9464d553c4d58, 0xc947cc137710ec84,
    0x98ce7b8999d78990, 0x8520f1d7bbfb284c, 0xa3136f35dd8eca28,
    0xbefde56bffa26bf4, 0x4c300ac98dc40345, 0x51de8097afe8a299,
    0x77ed1e75c99d40fd, 0x6a03942bebb1e121, 0x3b8a23b105768435,
    0x2664a9ef275a25e9, 0x0057370d412fc78d, 0x1db9bd5363036651,
    0xa34458389ca10da5, 0xbeaad266be8dac79, 0x98994c84d8f84e1d,
    0x8577c6dafad4efc1, 0xd4fe714014138ad5, 0xc910fb1e363f2b09,
    0xef2365fc504ac96d, 0xf2cdefa2726668b1, 0x986015931b88068a,
    0x858e9fcd39a4a756, 0xa3bd012f5fd14532, 0xbe538b717dfde4ee,
    0xefda3ceb933a81fa, 0xf234b6b5b1162026, 0xd4072857d763c242,
    0xc9e9a209f54f639e, 0x771447620aed086a, 0x6afacd3c28c1a9b6,
    0x4cc953de4eb44bd2, 0x5127d9806c98ea0e, 0x00ae6e1a825f8f1a,
    0x1d40e444a0732ec6, 0x3b737aa6c606cca2, 0x269df0f8e42a6d7e,
    0xd4501f5a964c05cf, 0xc9be9504b460a413, 0xef8d0be6d2154677,
    0xf26381b8f039e7ab, 0xa3ea36221efe82bf, 0xbe04bc7c3cd22363,
    0x9837229e5aa7c107, 0x85d9a8c0788b60db, 0x3b244dab87290b2f,
    0x26cac7f5a505aaf3, 0x00f95917c3704897, 0x1d17d349e15ce94b,
    0x4c9e64d30f9b8c5f, 0x5170ee8d2db72d83, 0x7743706f4bc2cfe7,
    0x6aadfa3169ee6e3b, 0xa218840d981e1391, 0xbff60e53ba32b24d,
    0x99c590b1dc475029, 0x842b1aeffe6bf1f5, 0xd5a2ad7510ac94e1,
    0xc84c272b3280353d, 0xee7fb9c954f5d759, 0xf391339776d97685,
    0x4d6cd6fc897b1d71, 0x50825ca2ab57bcad, 0x76b1c240cd225ec9,
    0x6b5f481eef0eff15, 0x3ad6ff8401c99a01, 0x273875da23e53bdd,
    0x010beb384590d9b9, 0x1ce5616667bc7865, 0xee288ec415da10d4,
    0xf3c6049a37f6b108, 0xd5f59a785183536c, 0xc81b102673aff2b0,
    0x9992a7bc9d6897a4, 0x847c2de2bf443678, 0xa24fb300d931d41c,
    0xbfa1395efb1d75c0, 0x015cdc3504bf1e34, 0x1cb2566b2693bfe8,
    0x3a81c88940e65d8c, 0x276f42d762cafc50, 0x76e6f54d8c0d9944,
    0x6b087f13ae213898, 0x4d3be1f1c854dafc, 0x50d56bafea787b20,
    0x3a78919e8396151b, 0x27961bc0a1bab4c7, 0x01a58522c7cf56a3,
    0x1c4b0f7ce5e3f77f, 0x4dc2b8e60b24926b, 0x502c32b8290833b7,
    0x761fac5a4f7dd1d3, 0x6bf126046d51700f, 0xd50cc36f92f31bfb,
    0xc8e24931b0dfba27, 0xeed1d7d3d6aa5843, 0xf33f5d8df486f99f,
    0xa2b6ea171a419c8b, 0xbf586049386d3d57, 0x996bfeab5e18df33,
    0x848574f57c347eef, 0x76489b570e52165e, 0x6ba611092c7eb782,
    0x4d958feb4a0b55e6, 0x507b05b56827f43a, 0x01f2b22f86e0912e,
    0x1c1c3871a4cc30f2, 0x3a2fa693c2b9d296, 0x27c12ccde095734a,
    0x993cc9a61f3718be, 0x84d243f83d1bb962, 0xa2e1dd1a5b6e5b06,
    0xbf0f57447942fada, 0xee86e0de97859fce, 0xf3686a80b5a93e12,
    0xd55bf462d3dcdc76, 0xc8b57e3cf1f07daa, 0xd6e9a7309f3239a7,
    0xcb072d6ebd1e987b, 0xed34b38cdb6b7a1f, 0xf0da39d2f947dbc3,
    0xa1538e481780bed7, 0xbcbd041635ac1f0b, 0x9a8e9af453d9fd6f,
    0x876010aa71f55cb3, 0x399df5c18e573747, 0x24737f9fac7b969b,
    0x0240e17dca0e74ff, 0x1fae6b23e822d523, 0x4e27dcb906e5b037,
    0x53c956e724c911eb, 0x75fac80542bcf38f, 0x6814425b60905253,
    0x9ad9adf912f63ae2, 0x873727a730da9b3e, 0xa104b94556af795a,
    0xbcea331b7483d886, 0xed6384819a44bd92, 0xf08d0edfb8681c4e,
    0xd6be903dde1dfe2a, 0xcb501a63fc315ff6, 0x75adff0803933402,
    0x6843755621bf95de, 0x4e70ebb447ca77ba, 0x539e61ea65e6d666,
    0x0217d6708b21b372, 0x1ff95c2ea90d12ae, 0x39cac2cccf78f0ca,
    0x24244892ed545116, 0x4e89b2a384ba3f2d, 0x536738fda6969ef1,
    0x7554a61fc0e37c95, 0x68ba2c41e2cfdd49, 0x39339bdb0c08b85d,
    0x24dd11852e241981, 0x02ee8f674851fbe5, 0x1f0005396a7d5a39,
    0xa1fde05295df31cd, 0xbc136a0cb7f39011, 0x9a20f4eed1867275,
    0x87ce7eb0f3aad3a9, 0xd647c92a1d6db6bd, 0xcba943743f411761,
    0xed9add965934f505, 0xf07457c87b1854d9, 0x02b9b86a097e3c68,
    0x1f5732342b529db4, 0x3964acd64d277fd0, 0x248a26886f0bde0c,
    0x7503911281ccbb18, 0x68ed1b4ca3e01ac4, 0x4ede85aec595f8a0,
    0x53300ff0e7b9597c, 0xedcdea9b181b3288, 0xf02360c53a379354,
    0xd610fe275c427130, 0xcbfe74797e6ed0ec, 0x9a77c3e390a9b5f8,
    0x879949bdb2851424, 0xa1aad75fd4f0f640, 0xbc445d01f6dc579c,
    0x74f1233d072c2a36, 0x691fa96325008bea, 0x4f2c37814375698e,
    0x52c2bddf6159c852, 0x034b0a458f9ead46, 0x1ea5801badb20c9a,
    0x38961ef9cbc7eefe, 0x257894a7e9eb4f22, 0x9b8571cc164924d6,
    0x866bfb923465850a, 0xa05865705210676e, 0xbdb6ef2e703cc6b2,
    0xec3f58b49efba3a6, 0xf1d1d2eabcd7027a, 0xd7e24c08daa2e01e,
    0xca0cc656f88e41c2, 0x38c129f48ae82973, 0x252fa3aaa8c488af,
    0x031c3d48ceb16acb, 0x1ef2b716ec9dcb17, 0x4f7b008c025aae03,
    0x52958ad220760fdf, 0x74a614304603edbb, 0x69489e6e642f4c67,
    0xd7b57b059b8d2793, 0xca5bf15bb9a1864f, 0xec686fb9dfd4642b,
    0xf186e5e7fdf8c5f7, 0xa00f527d133fa0e3, 0xbde1d8233113013f,
    0x9bd246c15766e35b, 0x863ccc9f754a4287, 0xec9136ae1ca42cbc,
    0xf17fbcf03e888d60, 0xd74c221258fd6f04, 0xcaa2a84c7ad1ced8,
    0x9b2b1fd69416abcc, 0x86c59588b63a0a10, 0xa0f60b6ad04fe874,
    0xbd188134f26349a8, 0x03e5645f0dc1225c, 0x1e0bee012fed8380,
    0x383870e3499861e4, 0x25d6fabd6bb4c038, 0x745f4d278573a52c,
    0x69b1c779a75f04f0, 0x4f82599bc12ae694, 0x526cd3c5e3064748,
    0xa0a13c6791602ff9, 0xbd4fb639b34c8e25, 0x9b7c28dbd5396c41,
    0x8692a285f715cd9d, 0xd71b151f19d2a889, 0xcaf59f413bfe0955,
    0xecc601a35d8beb31, 0xf1288bfd7fa74aed, 0x4fd56e9680052119,
    0x523be4c8a22980c5, 0x74087a2ac45c62a1, 0x69e6f074e670c37d,
    0x386f47ee08b7a669, 0x2581cdb02a9b07b5, 0x03b253524ceee5d1,
    0x1e5cd90c6ec2440d,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹⁰³, …, 128 = x⁹⁶`).
  // Generated by running `./build_table 4`.
  static inline const uint64_t TABLE_4[256] = {
    0x0000000000000000, 0x5c2d776033c4205e, 0xb85aeec0678840bc,
    0xe47799a0544c60e2, 0xe26d72ab601e9ffd, 0xbe4005cb53dabfa3,
    0x5a379c6b0796df41, 0x061aeb0b3452ff1f, 0x56024a7d6f33217f,
    0x0a2f3d1d5cf70121, 0xee58a4bd08bb61c3, 0xb275d3dd3b7f419d,
    0xb46f38d60f2dbe82, 0xe8424fb63ce99edc, 0x0c35d61668a5fe3e,
    0x5018a1765b61de60, 0xac0494fade6642fe, 0xf029e39aeda262a0,
    0x145e7a3ab9ee0242, 0x48730d5a8a2a221c, 0x4e69e651be78dd03,
    0x124491318dbcfd5d, 0xf6330891d9f09dbf, 0xaa1e7ff1ea34bde1,
    0xfa06de87b1556381, 0xa62ba9e7829143df, 0x425c3047d6dd233d,
    0x1e714727e5190363, 0x186bac2cd14bfc7c, 0x4446db4ce28fdc22,
    0xa03142ecb6c3bcc0, 0xfc1c358c85079c9e, 0xcad186de13c29b79,
    0x96fcf1be2006bb27, 0x728b681e744adbc5, 0x2ea61f7e478efb9b,
    0x28bcf47573dc0484, 0x74918315401824da, 0x90e61ab514544438,
    0xcccb6dd527906466, 0x9cd3cca37cf1ba06, 0xc0febbc34f359a58,
    0x248922631b79faba, 0x78a4550328bddae4, 0x7ebebe081cef25fb,
    0x2293c9682f2b05a5, 0xc6e450c87b676547, 0x9ac927a848a34519,
    0x66d51224cda4d987, 0x3af86544fe60f9d9, 0xde8ffce4aa2c993b,
    0x82a28b8499e8b965, 0x84b8608fadba467a, 0xd89517ef9e7e6624,
    0x3ce28e4fca3206c6, 0x60cff92ff9f62698, 0x30d75859a297f8f8,
    0x6cfa2f399153d8a6, 0x888db699c51fb844, 0xd4a0c1f9f6db981a,
    0xd2ba2af2c2896705, 0x8e975d92f14d475b, 0x6ae0c432a50127b9,
    0x36cdb35296c507e7, 0x077ba297888b2877, 0x5b56d5f7bb4f0829,
    0xbf214c57ef0368cb, 0xe30c3b37dcc74895, 0xe516d03ce895b78a,
    0xb93ba75cdb5197d4, 0x5d4c3efc8f1df736, 0x0161499cbcd9d768,
    0x5179e8eae7b80908, 0x0d549f8ad47c2956, 0xe923062a803049b4,
    0xb50e714ab3f469ea, 0xb3149a4187a696f5, 0xef39ed21b462b6ab,
    0x0b4e7481e02ed649, 0x576303e1d3eaf617, 0xab7f366d56ed6a89,
    0xf752410d65294ad7, 0x1325d8ad31652a35, 0x4f08afcd02a10a6b,
    0x491244c636f3f574, 0x153f33a60537d52a, 0xf148aa06517bb5c8,
    0xad65dd6662bf9596, 0xfd7d7c1039de4bf6, 0xa1500b700a1a6ba8,
    0x452792d05e560b4a, 0x190ae5b06d922b14, 0x1f100ebb59c0d40b,
    0x433d79db6a04f455, 0xa74ae07b3e4894b7, 0xfb67971b0d8cb4e9,
    0xcdaa24499b49b30e, 0x91875329a88d9350, 0x75f0ca89fcc1f3b2,
    0x29ddbde9cf05d3ec, 0x2fc756e2fb572cf3, 0x73ea2182c8930cad,
    0x979db8229cdf6c4f, 0xcbb0cf42af1b4c11, 0x9ba86e34f47a9271,
    0xc7851954c7beb22f, 0x23f280f493f2d2cd, 0x7fdff794a036f293,
    0x79c51c9f94640d8c, 0x25e86bffa7a02dd2, 0xc19ff25ff3ec4d30,
    0x9db2853fc0286d6e, 0x61aeb0b3452ff1f0, 0x3d83c7d376ebd1ae,
    0xd9f45e7322a7b14c, 0x85d9291311639112, 0x83c3c21825316e0d,
    0xdfeeb57816f54e53, 0x3b992cd842b92eb1, 0x67b45bb8717d0eef,
    0x37acface2a1cd08f, 0x6b818dae19d8f0d1, 0x8ff6140e4d949033,
    0xd3db636e7e50b06d, 0xd5c188654a024f72, 0x89ecff0579c66f2c,
    0x6d9b66a52d8a0fce, 0x31b611c51e4e2f90, 0x0ef7452f111650ee,
    0x52da324f22d270b0, 0xb6adabef769e1052, 0xea80dc8f455a300c,
    0xec9a37847108cf13, 0xb0b740e442ccef4d, 0x54c0d94416808faf,
    0x08edae242544aff1, 0x58f50f527e257191, 0x04d878324de151cf,
    0xe0afe19219ad312d, 0xbc8296f22a691173, 0xba987df91e3bee6c,
    0xe6b50a992dffce32, 0x02c2933979b3aed0, 0x5eefe4594a778e8e,
    0xa2f3d1d5cf701210, 0xfedea6b5fcb4324e, 0x1aa93f15a8f852ac,
    0x468448759b3c72f2, 0x409ea37eaf6e8ded, 0x1cb3d41e9caaadb3,
    0xf8c44dbec8e6cd51, 0xa4e93adefb22ed0f, 0xf4f19ba8a043336f,
    0xa8dcecc893871331, 0x4cab7568c7cb73d3, 0x10860208f40f538d,
    0x169ce903c05dac92, 0x4ab19e63f3998ccc, 0xaec607c3a7d5ec2e,
    0xf2eb70a39411cc70, 0xc426c3f102d4cb97, 0x980bb4913110ebc9,
    0x7c7c2d31655c8b2b, 0x20515a515698ab75, 0x264bb15a62ca546a,
    0x7a66c63a510e7434, 0x9e115f9a054214d6, 0xc23c28fa36863488,
    0x9224898c6de7eae8, 0xce09feec5e23cab6, 0x2a7e674c0a6faa54,
    0x7653102c39ab8a0a, 0x7049fb270df97515, 0x2c648c473e3d554b,
    0xc81315e76a7135a9, 0x943e628759b515f7, 0x6822570bdcb28969,
    0x340f206bef76a937, 0xd078b9cbbb3ac9d5, 0x8c55ceab88fee98b,
    0x8a4f25a0bcac1694, 0xd66252c08f6836ca, 0x3215cb60db245628,
    0x6e38bc00e8e07676, 0x3e201d76b381a816, 0x620d6a1680458848,
    0x867af3b6d409e8aa, 0xda5784d6e7cdc8f4, 0xdc4d6fddd39f37eb,
    0x806018bde05b17b5, 0x6417811db4177757, 0x383af67d87d35709,
    0x098ce7b8999d7899, 0x55a190d8aa5958c7, 0xb1d60978fe153825,
    0xedfb7e18cdd1187b, 0xebe19513f983e764, 0xb7cce273ca47c73a,
    0x53bb7bd39e0ba7d8, 0x0f960cb3adcf8786, 0x5f8eadc5f6ae59e6,
    0x03a3daa5c56a79b8, 0xe7d443059126195a, 0xbbf93465a2e23904,
    0xbde3df6e96b0c61b, 0xe1cea80ea574e645, 0x05b931aef13886a7,
    0x599446cec2fca6f9, 0xa588734247fb3a67, 0xf9a50422743f1a39,
    0x1dd29d8220737adb, 0x41ffeae213b75a85, 0x47e501e927e5a59a,
    0x1bc87689142185c4, 0xffbfef29406de526, 0xa392984973a9c578,
    0xf38a393f28c81b18, 0xafa74e5f1b0c3b46, 0x4bd0d7ff4f405ba4,
    0x17fda09f7c847bfa, 0x11e74b9448d684e5, 0x4dca3cf47b12a4bb,
    0xa9bda5542f5ec459, 0xf590d2341c9ae407, 0xc35d61668a5fe3e0,
    0x9f701606b99bc3be, 0x7b078fa6edd7a35c, 0x272af8c6de138302,
    0x213013cdea417c1d, 0x7d1d64add9855c43, 0x996afd0d8dc93ca1,
    0xc5478a6dbe0d1cff, 0x955f2b1be56cc29f, 0xc9725c7bd6a8e2c1,
    0x2d05c5db82e48223, 0x7128b2bbb120a27d, 0x773259b085725d62,
    0x2b1f2ed0b6b67d3c, 0xcf68b770e2fa1dde, 0x9345c010d13e3d80,
    0x6f59f59c5439a11e, 0x337482fc67fd8140, 0xd7031b5c33b1e1a2,
    0x8b2e6c3c0075c1fc, 0x8d34873734273ee3, 0xd119f05707e31ebd,
    0x356e69f753af7e5f, 0x69431e97606b5e01, 0x395bbfe13b0a8061,
    0x6576c88108cea03f, 0x810151215c82c0dd, 0xdd2c26416f46e083,
    0xdb36cd4a5b141f9c, 0x871bba2a68d03fc2, 0x636c238a3c9c5f20,
    0x3f4154ea0f587f7e,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹¹¹, …, 128 = x¹⁰⁴`).
  // Generated by running `./build_table 5`.
  static inline const uint64_t TABLE_5[256] = {
    0x0000000000000000, 0x6184d55f721267c6, 0xc309aabee424cf8c,
    0xa28d7fe19636a84a, 0x14cbfa566747819d, 0x754f2f091555e65b,
    0xd7c250e883634e11, 0xb64685b7f17129d7, 0x2997f4acce8f033a,
    0x481321f3bc9d64fc, 0xea9e5e122aabccb6, 0x8b1a8b4d58b9ab70,
    0x3d5c0efaa9c882a7, 0x5cd8dba5dbdae561, 0xfe55a4444dec4d2b,
    0x9fd1711b3ffe2aed, 0x532fe9599d1e0674, 0x32ab3c06ef0c61b2,
    0x902643e7793ac9f8, 0xf1a296b80b28ae3e, 0x47e4130ffa5987e9,
    0x2660c650884be02f, 0x84edb9b11e7d4865, 0xe5696cee6c6f2fa3,
    0x7ab81df55391054e, 0x1b3cc8aa21836288, 0xb9b1b74bb7b5cac2,
    0xd8356214c5a7ad04, 0x6e73e7a334d684d3, 0x0ff732fc46c4e315,
    0xad7a4d1dd0f24b5f, 0xccfe9842a2e02c99, 0xa65fd2b33a3c0ce8,
    0xc7db07ec482e6b2e, 0x6556780dde18c364, 0x04d2ad52ac0aa4a2,
    0xb29428e55d7b8d75, 0xd310fdba2f69eab3, 0x719d825bb95f42f9,
    0x10195704cb4d253f, 0x8fc8261ff4b30fd2, 0xee4cf34086a16814,
    0x4cc18ca11097c05e, 0x2d4559fe6285a798, 0x9b03dc4993f48e4f,
    0xfa870916e1e6e989, 0x580a76f777d041c3, 0x398ea3a805c22605,
    0xf5703beaa7220a9c, 0x94f4eeb5d5306d5a, 0x367991544306c510,
    0x57fd440b3114a2d6, 0xe1bbc1bcc0658b01, 0x803f14e3b277ecc7,
    0x22b26b022441448d, 0x4336be5d5653234b, 0xdce7cf4669ad09a6,
    0xbd631a191bbf6e60, 0x1fee65f88d89c62a, 0x7e6ab0a7ff9ba1ec,
    0xc82c35100eea883b, 0xa9a8e04f7cf8effd, 0x0b259faeeace47b7,
    0x6aa14af198dc2071, 0xde670a4ddb760755, 0xbfe3df12a9646093,
    0x1d6ea0f33f52c8d9, 0x7cea75ac4d40af1f, 0xcaacf01bbc3186c8,
    0xab282544ce23e10e, 0x09a55aa558154944, 0x68218ffa2a072e82,
    0xf7f0fee115f9046f, 0x96742bbe67eb63a9, 0x34f9545ff1ddcbe3,
    0x557d810083cfac25, 0xe33b04b772be85f2, 0x82bfd1e800ace234,
    0x2032ae09969a4a7e, 0x41b67b56e4882db8, 0x8d48e31446680121,
    0xeccc364b347a66e7, 0x4e4149aaa24ccead, 0x2fc59cf5d05ea96b,
    0x99831942212f80bc, 0xf807cc1d533de77a, 0x5a8ab3fcc50b4f30,
    0x3b0e66a3b71928f6, 0xa4df17b888e7021b, 0xc55bc2e7faf565dd,
    0x67d6bd066cc3cd97, 0x065268591ed1aa51, 0xb014edeeefa08386,
    0xd19038b19db2e440, 0x731d47500b844c0a, 0x1299920f79962bcc,
    0x7838d8fee14a0bbd, 0x19bc0da193586c7b, 0xbb317240056ec431,
    0xdab5a71f777ca3f7, 0x6cf322a8860d8a20, 0x0d77f7f7f41fede6,
    0xaffa8816622945ac, 0xce7e5d49103b226a, 0x51af2c522fc50887,
    0x302bf90d5dd76f41, 0x92a686eccbe1c70b, 0xf32253b3b9f3a0cd,
    0x4564d6044882891a, 0x24e0035b3a90eedc, 0x866d7cbaaca64696,
    0xe7e9a9e5deb42150, 0x2b1731a77c540dc9, 0x4a93e4f80e466a0f,
    0xe81e9b199870c245, 0x899a4e46ea62a583, 0x3fdccbf11b138c54,
    0x5e581eae6901eb92, 0xfcd5614fff3743d8, 0x9d51b4108d25241e,
    0x0280c50bb2db0ef3, 0x63041054c0c96935, 0xc1896fb556ffc17f,
    0xa00dbaea24eda6b9, 0x164b3f5dd59c8f6e, 0x77cfea02a78ee8a8,
    0xd54295e331b840e2, 0xb4c640bc43aa2724, 0x2e16bbb019e2102f,
    0x4f926eef6bf077e9, 0xed1f110efdc6dfa3, 0x8c9bc4518fd4b865,
    0x3add41e67ea591b2, 0x5b5994b90cb7f674, 0xf9d4eb589a815e3e,
    0x98503e07e89339f8, 0x07814f1cd76d1315, 0x66059a43a57f74d3,
    0xc488e5a23349dc99, 0xa50c30fd415bbb5f, 0x134ab54ab02a9288,
    0x72ce6015c238f54e, 0xd0431ff4540e5d04, 0xb1c7caab261c3ac2,
    0x7d3952e984fc165b, 0x1cbd87b6f6ee719d, 0xbe30f85760d8d9d7,
    0xdfb42d0812cabe11, 0x69f2a8bfe3bb97c6, 0x08767de091a9f000,
    0xaafb0201079f584a, 0xcb7fd75e758d3f8c, 0x54aea6454a731561,
    0x352a731a386172a7, 0x97a70cfbae57daed, 0xf623d9a4dc45bd2b,
    0x40655c132d3494fc, 0x21e1894c5f26f33a, 0x836cf6adc9105b70,
    0xe2e823f2bb023cb6, 0x8849690323de1cc7, 0xe9cdbc5c51cc7b01,
    0x4b40c3bdc7fad34b, 0x2ac416e2b5e8b48d, 0x9c82935544999d5a,
    0xfd06460a368bfa9c, 0x5f8b39eba0bd52d6, 0x3e0fecb4d2af3510,
    0xa1de9dafed511ffd, 0xc05a48f09f43783b, 0x62d737110975d071,
    0x0353e24e7b67b7b7, 0xb51567f98a169e60, 0xd491b2a6f804f9a6,
    0x761ccd476e3251ec, 0x179818181c20362a, 0xdb66805abec01ab3,
    0xbae25505ccd27d75, 0x186f2ae45ae4d53f, 0x79ebffbb28f6b2f9,
    0xcfad7a0cd9879b2e, 0xae29af53ab95fce8, 0x0ca4d0b23da354a2,
    0x6d2005ed4fb13364, 0xf2f174f6704f1989, 0x9375a1a9025d7e4f,
    0x31f8de48946bd605, 0x507c0b17e679b1c3, 0xe63a8ea017089814,
    0x87be5bff651affd2, 0x2533241ef32c5798, 0x44b7f141813e305e,
    0xf071b1fdc294177a, 0x91f564a2b08670bc, 0x33781b4326b0d8f6,
    0x52fcce1c54a2bf30, 0xe4ba4baba5d396e7, 0x853e9ef4d7c1f121,
    0x27b3e11541f7596b, 0x4637344a33e53ead, 0xd9e645510c1b1440,
    0xb862900e7e097386, 0x1aefefefe83fdbcc, 0x7b6b3ab09a2dbc0a,
    0xcd2dbf076b5c95dd, 0xaca96a58194ef21b, 0x0e2415b98f785a51,
    0x6fa0c0e6fd6a3d97, 0xa35e58a45f8a110e, 0xc2da8dfb2d9876c8,
    0x6057f21abbaede82, 0x01d32745c9bcb944, 0xb795a2f238cd9093,
    0xd61177ad4adff755, 0x749c084cdce95f1f, 0x1518dd13aefb38d9,
    0x8ac9ac0891051234, 0xeb4d7957e31775f2, 0x49c006b67521ddb8,
    0x2844d3e90733ba7e, 0x9e02565ef64293a9, 0xff8683018450f46f,
    0x5d0bfce012665c25, 0x3c8f29bf60743be3, 0x562e634ef8a81b92,
    0x37aab6118aba7c54, 0x9527c9f01c8cd41e, 0xf4a31caf6e9eb3d8,
    0x42e599189fef9a0f, 0x23614c47edfdfdc9, 0x81ec33a67bcb5583,
    0xe068e6f909d93245, 0x7fb997e2362718a8, 0x1e3d42bd44357f6e,
    0xbcb03d5cd203d724, 0xdd34e803a011b0e2, 0x6b726db451609935,
    0x0af6b8eb2372fef3, 0xa87bc70ab54456b9, 0xc9ff1255c756317f,
    0x05018a1765b61de6, 0x64855f4817a47a20, 0xc60820a98192d26a,
    0xa78cf5f6f380b5ac, 0x11ca704102f19c7b, 0x704ea51e70e3fbbd,
    0xd2c3daffe6d553f7, 0xb3470fa094c73431, 0x2c967ebbab391edc,
    0x4d12abe4d92b791a, 0xef9fd4054f1dd150, 0x8e1b015a3d0fb696,
    0x385d84edcc7e9f41, 0x59d951b2be6cf887, 0xfb542e53285a50cd,
    0x9ad0fb0c5a48370b,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹¹⁹, …, 128 = x¹¹²`).
  // Generated by running `./build_table 6`.
  static inline const uint64_t TABLE_6[256] = {
    0x0000000000000000, 0x22ef0d5934f964ec, 0x45de1ab269f2c9d8,
    0x673117eb5d0bad34, 0x8bbc3564d3e593b0, 0xa953383de71cf75c,
    0xce622fd6ba175a68, 0xec8d228f8eee3e84, 0x85a0c5e208c539e5,
    0xa74fc8bb3c3c5d09, 0xc07edf506137f03d, 0xe291d20955ce94d1,
    0x0e1cf086db20aa55, 0x2cf3fddfefd9ceb9, 0x4bc2ea34b2d2638d,
    0x692de76d862b0761, 0x999924efbe846d4f, 0xbb7629b68a7d09a3,
    0xdc473e5dd776a497, 0xfea83304e38fc07b, 0x1225118b6d61feff,
    0x30ca1cd259989a13, 0x57fb0b3904933727, 0x75140660306a53cb,
    0x1c39e10db64154aa, 0x3ed6ec5482b83046, 0x59e7fbbfdfb39d72,
    0x7b08f6e6eb4af99e, 0x9785d46965a4c71a, 0xb56ad930515da3f6,
    0xd25bcedb0c560ec2, 0xf0b4c38238af6a2e, 0xa1eae6f4d206c41b,
    0x8305ebade6ffa0f7, 0xe434fc46bbf40dc3, 0xc6dbf11f8f0d692f,
    0x2a56d39001e357ab, 0x08b9dec9351a3347, 0x6f88c92268119e73,
    0x4d67c47b5ce8fa9f, 0x244a2316dac3fdfe, 0x06a52e4fee3a9912,
    0x619439a4b3313426, 0x437b34fd87c850ca, 0xaff6167209266e4e,
    0x8d191b2b3ddf0aa2, 0xea280cc060d4a796, 0xc8c70199542dc37a,
    0x3873c21b6c82a954, 0x1a9ccf42587bcdb8, 0x7dadd8a90570608c,
    0x5f42d5f031890460, 0xb3cff77fbf673ae4, 0x9120fa268b9e5e08,
    0xf611edcdd695f33c, 0xd4fee094e26c97d0, 0xbdd307f9644790b1,
    0x9f3c0aa050bef45d, 0xf80d1d4b0db55969, 0xdae21012394c3d85,
    0x366f329db7a20301, 0x14803fc4835b67ed, 0x73b1282fde50cad9,
    0x515e2576eaa9ae35, 0xd10d62c20b0396b3, 0xf3e26f9b3ffaf25f,
    0x94d3787062f15f6b, 0xb63c752956083b87, 0x5ab157a6d8e60503,
    0x785e5affec1f61ef, 0x1f6f4d14b114ccdb, 0x3d80404d85eda837,
    0x54ada72003c6af56, 0x7642aa79373fcbba, 0x1173bd926a34668e,
    0x339cb0cb5ecd0262, 0xdf119244d0233ce6, 0xfdfe9f1de4da580a,
    0x9acf88f6b9d1f53e, 0xb82085af8d2891d2, 0x4894462db587fbfc,
    0x6a7b4b74817e9f10, 0x0d4a5c9fdc753224, 0x2fa551c6e88c56c8,
    0xc32873496662684c, 0xe1c77e10529b0ca0, 0x86f669fb0f90a194,
    0xa41964a23b69c578, 0xcd3483cfbd42c219, 0xefdb8e9689bba6f5,
    0x88ea997dd4b00bc1, 0xaa059424e0496f2d, 0x4688b6ab6ea751a9,
    0x6467bbf25a5e3545, 0x0356ac1907559871, 0x21b9a14033acfc9d,
    0x70e78436d90552a8, 0x5208896fedfc3644, 0x35399e84b0f79b70,
    0x17d693dd840eff9c, 0xfb5bb1520ae0c118, 0xd9b4bc0b3e19a5f4,
    0xbe85abe0631208c0, 0x9c6aa6b957eb6c2c, 0xf54741d4d1c06b4d,
    0xd7a84c8de5390fa1, 0xb0995b66b832a295, 0x9276563f8ccbc679,
    0x7efb74b00225f8fd, 0x5c1479e936dc9c11, 0x3b256e026bd73125,
    0x19ca635b5f2e55c9, 0xe97ea0d967813fe7, 0xcb91ad8053785b0b,
    0xaca0ba6b0e73f63f, 0x8e4fb7323a8a92d3, 0x62c295bdb464ac57,
    0x402d98e4809dc8bb, 0x271c8f0fdd96658f, 0x05f38256e96f0163,
    0x6cde653b6f440602, 0x4e3168625bbd62ee, 0x29007f8906b6cfda,
    0x0bef72d0324fab36, 0xe762505fbca195b2, 0xc58d5d068858f15e,
    0xa2bc4aedd5535c6a, 0x805347b4e1aa3886, 0x30c26aafb90933e3,
    0x122d67f68df0570f, 0x751c701dd0fbfa3b, 0x57f37d44e4029ed7,
    0xbb7e5fcb6aeca053, 0x999152925e15c4bf, 0xfea04579031e698b,
    0xdc4f482037e70d67, 0xb562af4db1cc0a06, 0x978da21485356eea,
    0xf0bcb5ffd83ec3de, 0xd253b8a6ecc7a732, 0x3ede9a29622999b6,
    0x1c31977056d0fd5a, 0x7b00809b0bdb506e, 0x59ef8dc23f223482,
    0xa95b4e40078d5eac, 0x8bb4431933743a40, 0xec8554f26e7f9774,
    0xce6a59ab5a86f398, 0x22e77b24d468cd1c, 0x0008767de091a9f0,
    0x67396196bd9a04c4, 0x45d66ccf89636028, 0x2cfb8ba20f486749,
    0x0e1486fb3bb103a5, 0x6925911066baae91, 0x4bca9c495243ca7d,
    0xa747bec6dcadf4f9, 0x85a8b39fe8549015, 0xe299a474b55f3d21,
    0xc076a92d81a659cd, 0x91288c5b6b0ff7f8, 0xb3c781025ff69314,
    0xd4f696e902fd3e20, 0xf6199bb036045acc, 0x1a94b93fb8ea6448,
    0x387bb4668c1300a4, 0x5f4aa38dd118ad90, 0x7da5aed4e5e1c97c,
    0x148849b963cace1d, 0x366744e05733aaf1, 0x5156530b0a3807c5,
    0x73b95e523ec16329, 0x9f347cddb02f5dad, 0xbddb718484d63941,
    0xdaea666fd9dd9475, 0xf8056b36ed24f099, 0x08b1a8b4d58b9ab7,
    0x2a5ea5ede172fe5b, 0x4d6fb206bc79536f, 0x6f80bf5f88803783,
    0x830d9dd0066e0907, 0xa1e2908932976deb, 0xc6d387626f9cc0df,
    0xe43c8a3b5b65a433, 0x8d116d56dd4ea352, 0xaffe600fe9b7c7be,
    0xc8cf77e4b4bc6a8a, 0xea207abd80450e66, 0x06ad58320eab30e2,
    0x2442556b3a52540e, 0x437342806759f93a, 0x619c4fd953a09dd6,
    0xe1cf086db20aa550, 0xc320053486f3c1bc, 0xa41112dfdbf86c88,
    0x86fe1f86ef010864, 0x6a733d0961ef36e0, 0x489c30505516520c,
    0x2fad27bb081dff38, 0x0d422ae23ce49bd4, 0x646fcd8fbacf9cb5,
    0x4680c0d68e36f859, 0x21b1d73dd33d556d, 0x035eda64e7c43181,
    0xefd3f8eb692a0f05, 0xcd3cf5b25dd36be9, 0xaa0de25900d8c6dd,
    0x88e2ef003421a231, 0x78562c820c8ec81f, 0x5ab921db3877acf3,
    0x3d883630657c01c7, 0x1f673b695185652b, 0xf3ea19e6df6b5baf,
    0xd10514bfeb923f43, 0xb6340354b6999277, 0x94db0e0d8260f69b,
    0xfdf6e960044bf1fa, 0xdf19e43930b29516, 0xb828f3d26db93822,
    0x9ac7fe8b59405cce, 0x764adc04d7ae624a, 0x54a5d15de35706a6,
    0x3394c6b6be5cab92, 0x117bcbef8aa5cf7e, 0x4025ee99600c614b,
    0x62cae3c054f505a7, 0x05fbf42b09fea893, 0x2714f9723d07cc7f,
    0xcb99dbfdb3e9f2fb, 0xe976d6a487109617, 0x8e47c14fda1b3b23,
    0xaca8cc16eee25fcf, 0xc5852b7b68c958ae, 0xe76a26225c303c42,
    0x805b31c9013b9176, 0xa2b43c9035c2f59a, 0x4e391e1fbb2ccb1e,
    0x6cd613468fd5aff2, 0x0be704add2de02c6, 0x290809f4e627662a,
    0xd9bcca76de880c04, 0xfb53c72fea7168e8, 0x9c62d0c4b77ac5dc,
    0xbe8ddd9d8383a130, 0x5200ff120d6d9fb4, 0x70eff24b3994fb58,
    0x17dee5a0649f566c, 0x3531e8f950663280, 0x5c1c0f94d64d35e1,
    0x7ef302cde2b4510d, 0x19c21526bfbffc39, 0x3b2d187f8b4698d5,
    0xd7a03af005a8a651, 0xf54f37a93151c2bd, 0x927e20426c5a6f89,
    0xb0912d1b58a30b65,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹²⁷, …, 128 = x¹²⁰`).
  // Generated by running `./build_table 7`.
  static inline const uint64_t TABLE_7[256] = {
    0x0000000000000000, 0xdabe95afc7875f40, 0x27a584742000a005,
    0xfd1b11dbe787ff45, 0x4f4b08e84001400a, 0x95f59d4787861f4a,
    0x68ee8c9c6001e00f, 0xb2501933a786bf4f, 0x9e9611d080028014,
    0x4428847f4785df54, 0xb93395a4a0022011, 0x638d000b67857f51,
    0xd1dd1938c003c01e, 0x0b638c9707849f5e, 0xf6789d4ce003601b,
    0x2cc608e327843f5b, 0xaff48c8aaf0b1ead, 0x754a1925688c41ed,
    0x885108fe8f0bbea8, 0x52ef9d51488ce1e8, 0xe0bf8462ef0a5ea7,
    0x3a0111cd288d01e7, 0xc71a0016cf0afea2, 0x1da495b9088da1e2,
    0x31629d5a2f099eb9, 0xebdc08f5e88ec1f9, 0x16c7192e0f093ebc,
    0xcc798c81c88e61fc, 0x7e2995b26f08deb3, 0xa497001da88f81f3,
    0x598c11c64f087eb6, 0x83328469888f21f6, 0xcd31b63ef11823df,
    0x178f2391369f7c9f, 0xea94324ad11883da, 0x302aa7e5169fdc9a,
    0x827abed6b11963d5, 0x58c42b79769e3c95, 0xa5df3aa29119c3d0,
    0x7f61af0d569e9c90, 0x53a7a7ee711aa3cb, 0x89193241b69dfc8b,
    0x7402239a511a03ce, 0xaebcb635969d5c8e, 0x1cecaf06311be3c1,
    0xc6523aa9f69cbc81, 0x3b492b72111b43c4, 0xe1f7beddd69c1c84,
    0x62c53ab45e133d72, 0xb87baf1b99946232, 0x4560bec07e139d77,
    0x9fde2b6fb994c237, 0x2d8e325c1e127d78, 0xf730a7f3d9952238,
    0x0a2bb6283e12dd7d, 0xd0952387f995823d, 0xfc532b64de11bd66,
    0x26edbecb1996e226, 0xdbf6af10fe111d63, 0x01483abf39964223,
    0xb318238c9e10fd6c, 0x69a6b6235997a22c, 0x94bda7f8be105d69,
    0x4e03325779970229, 0x08bbc3564d3e593b, 0xd20556f98ab9067b,
    0x2f1e47226d3ef93e, 0xf5a0d28daab9a67e, 0x47f0cbbe0d3f1931,
    0x9d4e5e11cab84671, 0x60554fca2d3fb934, 0xbaebda65eab8e674,
    0x962dd286cd3cd92f, 0x4c9347290abb866f, 0xb18856f2ed3c792a,
    0x6b36c35d2abb266a, 0xd966da6e8d3d9925, 0x03d84fc14abac665,
    0xfec35e1aad3d3920, 0x247dcbb56aba6660, 0xa74f4fdce2354796,
    0x7df1da7325b218d6, 0x80eacba8c235e793, 0x5a545e0705b2b8d3,
    0xe8044734a234079c, 0x32bad29b65b358dc, 0xcfa1c3408234a799,
    0x151f56ef45b3f8d9, 0x39d95e0c6237c782, 0xe367cba3a5b098c2,
    0x1e7cda7842376787, 0xc4c24fd785b038c7, 0x769256e422368788,
    0xac2cc34be5b1d8c8, 0x5137d2900236278d, 0x8b89473fc5b178cd,
    0xc58a7568bc267ae4, 0x1f34e0c77ba125a4, 0xe22ff11c9c26dae1,
    0x389164b35ba185a1, 0x8ac17d80fc273aee, 0x507fe82f3ba065ae,
    0xad64f9f4dc279aeb, 0x77da6c5b1ba0c5ab, 0x5b1c64b83c24faf0,
    0x81a2f117fba3a5b0, 0x7cb9e0cc1c245af5, 0xa6077563dba305b5,
    0x14576c507c25bafa, 0xcee9f9ffbba2e5ba, 0x33f2e8245c251aff,
    0xe94c7d8b9ba245bf, 0x6a7ef9e2132d6449, 0xb0c06c4dd4aa3b09,
    0x4ddb7d96332dc44c, 0x9765e839f4aa9b0c, 0x2535f10a532c2443,
    0xff8b64a594ab7b03, 0x0290757e732c8446, 0xd82ee0d1b4abdb06,
    0xf4e8e832932fe45d, 0x2e567d9d54a8bb1d, 0xd34d6c46b32f4458,
    0x09f3f9e974a81b18, 0xbba3e0dad32ea457, 0x611d757514a9fb17,
    0x9c0664aef32e0452, 0x46b8f10134a95b12, 0x117786ac9a7cb276,
    0xcbc913035dfbed36, 0x36d202d8ba7c1273, 0xec6c97777dfb4d33,
    0x5e3c8e44da7df27c, 0x84821beb1dfaad3c, 0x79990a30fa7d5279,
    0xa3279f9f3dfa0d39, 0x8fe1977c1a7e3262, 0x555f02d3ddf96d22,
    0xa84413083a7e9267, 0x72fa86a7fdf9cd27, 0xc0aa9f945a7f7268,
    0x1a140a3b9df82d28, 0xe70f1be07a7fd26d, 0x3db18e4fbdf88d2d,
    0xbe830a263577acdb, 0x643d9f89f2f0f39b, 0x99268e5215770cde,
    0x43981bfdd2f0539e, 0xf1c802ce7576ecd1, 0x2b769761b2f1b391,
    0xd66d86ba55764cd4, 0x0cd3131592f11394, 0x20151bf6b5752ccf,
    0xfaab8e5972f2738f, 0x07b09f8295758cca, 0xdd0e0a2d52f2d38a,
    0x6f5e131ef5746cc5, 0xb5e086b132f33385, 0x48fb976ad574ccc0,
    0x924502c512f39380, 0xdc4630926b6491a9, 0x06f8a53dace3cee9,
    0xfbe3b4e64b6431ac, 0x215d21498ce36eec, 0x930d387a2b65d1a3,
    0x49b3add5ece28ee3, 0xb4a8bc0e0b6571a6, 0x6e1629a1cce22ee6,
    0x42d02142eb6611bd, 0x986eb4ed2ce14efd, 0x6575a536cb66b1b8,
    0xbfcb30990ce1eef8, 0x0d9b29aaab6751b7, 0xd725bc056ce00ef7,
    0x2a3eadde8b67f1b2, 0xf08038714ce0aef2, 0x73b2bc18c46f8f04,
    0xa90c29b703e8d044, 0x5417386ce46f2f01, 0x8ea9adc323e87041,
    0x3cf9b4f0846ecf0e, 0xe647215f43e9904e, 0x1b5c3084a46e6f0b,
    0xc1e2a52b63e9304b, 0xed24adc8446d0f10, 0x379a386783ea5050,
    0xca8129bc646daf15, 0x103fbc13a3eaf055, 0xa26fa520046c4f1a,
    0x78d1308fc3eb105a, 0x85ca2154246cef1f, 0x5f74b4fbe3ebb05f,
    0x19cc45fad742eb4d, 0xc372d05510c5b40d, 0x3e69c18ef7424b48,
    0xe4d7542130c51408, 0x56874d129743ab47, 0x8c39d8bd50c4f407,
    0x7122c966b7430b42, 0xab9c5cc970c45402, 0x875a542a57406b59,
    0x5de4c18590c73419, 0xa0ffd05e7740cb5c, 0x7a4145f1b0c7941c,
    0xc8115cc217412b53, 0x12afc96dd0c67413, 0xefb4d8b637418b56,
    0x350a4d19f0c6d416, 0xb638c9707849f5e0, 0x6c865cdfbfceaaa0,
    0x919d4d04584955e5, 0x4b23d8ab9fce0aa5, 0xf973c1983848b5ea,
    0x23cd5437ffcfeaaa, 0xded645ec184815ef, 0x0468d043dfcf4aaf,
    0x28aed8a0f84b75f4, 0xf2104d0f3fcc2ab4, 0x0f0b5cd4d84bd5f1,
    0xd5b5c97b1fcc8ab1, 0x67e5d048b84a35fe, 0xbd5b45e77fcd6abe,
    0x4040543c984a95fb, 0x9afec1935fcdcabb, 0xd4fdf3c4265ac892,
    0x0e43666be1dd97d2, 0xf35877b0065a6897, 0x29e6e21fc1dd37d7,
    0x9bb6fb2c665b8898, 0x41086e83a1dcd7d8, 0xbc137f58465b289d,
    0x66adeaf781dc77dd, 0x4a6be214a6584886, 0x90d577bb61df17c6,
    0x6dce66608658e883, 0xb770f3cf41dfb7c3, 0x0520eafce659088c,
    0xdf9e7f5321de57cc, 0x22856e88c659a889, 0xf83bfb2701def7c9,
    0x7b097f4e8951d63f, 0xa1b7eae14ed6897f, 0x5cacfb3aa951763a,
    0x86126e956ed6297a, 0x344277a6c9509635, 0xeefce2090ed7c975,
    0x13e7f3d2e9503630, 0xc959667d2ed76970, 0xe59f6e9e0953562b,
    0x3f21fb31ced4096b, 0xc23aeaea2953f62e, 0x18847f45eed4a96e,
    0xaad4667649521621, 0x706af3d98ed54961, 0x8d71e2026952b624,
    0x57cf77adaed5e964,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹³⁵, …, 128 = x¹²⁸`).
  // Generated by running `./build_table 8`.
  static inline const uint64_t TABLE_8[256] = {
    0x0000000000000000, 0x646c955f440400fe, 0xc8d92abe880801fc,
    0xacb5bfe1cc0c0102, 0x036afa56bf1e1d7d, 0x67066f09fb1a1d83,
    0xcbb3d0e837161c81, 0xafdf45b773121c7f, 0x06d5f4ad7e3c3afa,
    0x62b961f23a383a04, 0xce0cde13f6343b06, 0xaa604b4cb2303bf8,
    0x05bf0efbc1222787, 0x61d39ba485262779, 0xcd662445492a267b,
    0xa90ab11a0d2e2685, 0x0dabe95afc7875f4, 0x69c77c05b87c750a,
    0xc572c3e474707408, 0xa11e56bb307474f6, 0x0ec1130c43666889,
    0x6aad865307626877, 0xc61839b2cb6e6975, 0xa274aced8f6a698b,
    0x0b7e1df782444f0e, 0x6f1288a8c6404ff0, 0xc3a737490a4c4ef2,
    0xa7cba2164e484e0c, 0x0814e7a13d5a5273, 0x6c7872fe795e528d,
    0xc0cdcd1fb552538f, 0xa4a15840f1565371, 0x1b57d2b5f8f0ebe8,
    0x7f3b47eabcf4eb16, 0xd38ef80b70f8ea14, 0xb7e26d5434fceaea,
    0x183d28e347eef695, 0x7c51bdbc03eaf66b, 0xd0e4025dcfe6f769,
    0xb48897028be2f797, 0x1d82261886ccd112, 0x79eeb347c2c8d1ec,
    0xd55b0ca60ec4d0ee, 0xb13799f94ac0d010, 0x1ee8dc4e39d2cc6f,
    0x7a8449117dd6cc91, 0xd631f6f0b1dacd93, 0xb25d63aff5decd6d,
    0x16fc3bef04889e1c, 0x7290aeb0408c9ee2, 0xde2511518c809fe0,
    0xba49840ec8849f1e, 0x1596c1b9bb968361, 0x71fa54e6ff92839f,
    0xdd4feb07339e829d, 0xb9237e58779a8263, 0x1029cf427ab4a4e6,
    0x74455a1d3eb0a418, 0xd8f0e5fcf2bca51a, 0xbc9c70a3b6b8a5e4,
    0x13433514c5aab99b, 0x772fa04b81aeb965, 0xdb9a1faa4da2b867,
    0xbff68af509a6b899, 0x36afa56bf1e1d7d0, 0x52c33034b5e5d72e,
    0xfe768fd579e9d62c, 0x9a1a1a8a3dedd6d2, 0x35c55f3d4effcaad,
    0x51a9ca620afbca53, 0xfd1c7583c6f7cb51, 0x9970e0dc82f3cbaf,
    0x307a51c68fdded2a, 0x5416c499cbd9edd4, 0xf8a37b7807d5ecd6,
    0x9ccfee2743d1ec28, 0x3310ab9030c3f057, 0x577c3ecf74c7f0a9,
    0xfbc9812eb8cbf1ab, 0x9fa51471fccff155, 0x3b044c310d99a224,
    0x5f68d96e499da2da, 0xf3dd668f8591a3d8, 0x97b1f3d0c195a326,
    0x386eb667b287bf59, 0x5c022338f683bfa7, 0xf0b79cd93a8fbea5,
    0x94db09867e8bbe5b, 0x3dd1b89c73a598de, 0x59bd2dc337a19820,
    0xf5089222fbad9922, 0x9164077dbfa999dc, 0x3ebb42caccbb85a3,
    0x5ad7d79588bf855d, 0xf662687444b3845f, 0x920efd2b00b784a1,
    0x2df877de09113c38, 0x4994e2814d153cc6, 0xe5215d6081193dc4,
    0x814dc83fc51d3d3a, 0x2e928d88b60f2145, 0x4afe18d7f20b21bb,
    0xe64ba7363e0720b9, 0x822732697a032047, 0x2b2d8373772d06c2,
    0x4f41162c3329063c, 0xe3f4a9cdff25073e, 0x87983c92bb2107c0,
    0x28477925c8331bbf, 0x4c2bec7a8c371b41, 0xe09e539b403b1a43,
    0x84f2c6c4043f1abd, 0x20539e84f56949cc, 0x443f0bdbb16d4932,
    0xe88ab43a7d614830, 0x8ce62165396548ce, 0x233964d24a7754b1,
    0x4755f18d0e73544f, 0xebe04e6cc27f554d, 0x8f8cdb33867b55b3,
    0x26866a298b557336, 0x42eaff76cf5173c8, 0xee5f4097035d72ca,
    0x8a33d5c847597234, 0x25ec907f344b6e4b, 0x41800520704f6eb5,
    0xed35bac1bc436fb7, 0x89592f9ef8476f49, 0x6d5f4ad7e3c3afa0,
    0x0933df88a7c7af5e, 0xa58660696bcbae5c, 0xc1eaf5362fcfaea2,
    0x6e35b0815cddb2dd, 0x0a5925de18d9b223, 0xa6ec9a3fd4d5b321,
    0xc2800f6090d1b3df, 0x6b8abe7a9dff955a, 0x0fe62b25d9fb95a4,
    0xa35394c415f794a6, 0xc73f019b51f39458, 0x68e0442c22e18827,
    0x0c8cd17366e588d9, 0xa0396e92aae989db, 0xc455fbcdeeed8925,
    0x60f4a38d1fbbda54, 0x049836d25bbfdaaa, 0xa82d893397b3dba8,
    0xcc411c6cd3b7db56, 0x639e59dba0a5c729, 0x07f2cc84e4a1c7d7,
    0xab47736528adc6d5, 0xcf2be63a6ca9c62b, 0x662157206187e0ae,
    0x024dc27f2583e050, 0xaef87d9ee98fe152, 0xca94e8c1ad8be1ac,
    0x654bad76de99fdd3, 0x012738299a9dfd2d, 0xad9287c85691fc2f,
    0xc9fe12971295fcd1, 0x760898621b334448, 0x12640d3d5f3744b6,
    0xbed1b2dc933b45b4, 0xdabd2783d73f454a, 0x75626234a42d5935,
    0x110ef76be02959cb, 0xbdbb488a2c2558c9, 0xd9d7ddd568215837,
    0x70dd6ccf650f7eb2, 0x14b1f990210b7e4c, 0xb8044671ed077f4e,
    0xdc68d32ea9037fb0, 0x73b79699da1163cf, 0x17db03c69e156331,
    0xbb6ebc2752196233, 0xdf022978161d62cd, 0x7ba37138e74b31bc,
    0x1fcfe467a34f3142, 0xb37a5b866f433040, 0xd716ced92b4730be,
    0x78c98b6e58552cc1, 0x1ca51e311c512c3f, 0xb010a1d0d05d2d3d,
    0xd47c348f94592dc3, 0x7d76859599770b46, 0x191a10cadd730bb8,
    0xb5afaf2b117f0aba, 0xd1c33a74557b0a44, 0x7e1c7fc32669163b,
    0x1a70ea9c626d16c5, 0xb6c5557dae6117c7, 0xd2a9c022ea651739,
    0x5bf0efbc12227870, 0x3f9c7ae35626788e, 0x9329c5029a2a798c,
    0xf745505dde2e7972, 0x589a15eaad3c650d, 0x3cf680b5e93865f3,
    0x90433f54253464f1, 0xf42faa0b6130640f, 0x5d251b116c1e428a,
    0x39498e4e281a4274, 0x95fc31afe4164376, 0xf190a4f0a0124388,
    0x5e4fe147d3005ff7, 0x3a23741897045f09, 0x9696cbf95b085e0b,
    0xf2fa5ea61f0c5ef5, 0x565b06e6ee5a0d84, 0x323793b9aa5e0d7a,
    0x9e822c5866520c78, 0xfaeeb90722560c86, 0x5531fcb0514410f9,
    0x315d69ef15401007, 0x9de8d60ed94c1105, 0xf98443519d4811fb,
    0x508ef24b9066377e, 0x34e26714d4623780, 0x9857d8f5186e3682,
    0xfc3b4daa5c6a367c, 0x53e4081d2f782a03, 0x37889d426b7c2afd,
    0x9b3d22a3a7702bff, 0xff51b7fce3742b01, 0x40a73d09ead29398,
    0x24cba856aed69366, 0x887e17b762da9264, 0xec1282e826de929a,
    0x43cdc75f55cc8ee5, 0x27a1520011c88e1b, 0x8b14ede1ddc48f19,
    0xef7878be99c08fe7, 0x4672c9a494eea962, 0x221e5cfbd0eaa99c,
    0x8eabe31a1ce6a89e, 0xeac7764558e2a860, 0x451833f22bf0b41f,
    0x2174a6ad6ff4b4e1, 0x8dc1194ca3f8b5e3, 0xe9ad8c13e7fcb51d,
    0x4d0cd45316aae66c, 0x2960410c52aee692, 0x85d5feed9ea2e790,
    0xe1b96bb2daa6e76e, 0x4e662e05a9b4fb11, 0x2a0abb5aedb0fbef,
    0x86bf04bb21bcfaed, 0xe2d391e465b8fa13, 0x4bd920fe6896dc96,
    0x2fb5b5a12c92dc68, 0x83000a40e09edd6a, 0xe76c9f1fa49add94,
    0x48b3daa8d788c1eb, 0x2cdf4ff7938cc115, 0x806af0165f80c017,
    0xe40665491b84c0e9,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹⁴³, …, 128 = x¹³⁶`).
  // Generated by running `./build_table 9`.
  static inline const uint64_t TABLE_9[256] = {
    0x0000000000000000, 0x53e7815838846436, 0xa7cf02b07108c86c,
    0xf42883e8498cac5a, 0xdd46aa4b4d1f8e5d, 0x8ea12b13759bea6b,
    0x7a89a8fb3c174631, 0x296e29a304932207, 0x2855fbbd3531023f,
    0x7bb27ae50db56609, 0x8f9af90d4439ca53, 0xdc7d78557cbdae65,
    0xf51351f6782e8c62, 0xa6f4d0ae40aae854, 0x52dc53460926440e,
    0x013bd21e31a22038, 0x50abf77a6a62047e, 0x034c762252e66048,
    0xf764f5ca1b6acc12, 0xa483749223eea824, 0x8ded5d31277d8a23,
    0xde0adc691ff9ee15, 0x2a225f815675424f, 0x79c5ded96ef12679,
    0x78fe0cc75f530641, 0x2b198d9f67d76277, 0xdf310e772e5bce2d,
    0x8cd68f2f16dfaa1b, 0xa5b8a68c124c881c, 0xf65f27d42ac8ec2a,
    0x0277a43c63444070, 0x519025645bc02446, 0xa157eef4d4c408fc,
    0xf2b06facec406cca, 0x0698ec44a5ccc090, 0x557f6d1c9d48a4a6,
    0x7c1144bf99db86a1, 0x2ff6c5e7a15fe297, 0xdbde460fe8d34ecd,
    0x8839c757d0572afb, 0x89021549e1f50ac3, 0xdae59411d9716ef5,
    0x2ecd17f990fdc2af, 0x7d2a96a1a879a699, 0x5444bf02acea849e,
    0x07a33e5a946ee0a8, 0xf38bbdb2dde24cf2, 0xa06c3ceae56628c4,
    0xf1fc198ebea60c82, 0xa21b98d6862268b4, 0x56331b3ecfaec4ee,
    0x05d49a66f72aa0d8, 0x2cbab3c5f3b982df, 0x7f5d329dcb3de6e9,
    0x8b75b17582b14ab3, 0xd892302dba352e85, 0xd9a9e2338b970ebd,
    0x8a4e636bb3136a8b, 0x7e66e083fa9fc6d1, 0x2d8161dbc21ba2e7,
    0x04ef4878c68880e0, 0x5708c920fe0ce4d6, 0xa3204ac8b780488c,
    0xf0c7cb908f042cba, 0xd07772c206860f7d, 0x8390f39a3e026b4b,
    0x77b87072778ec711, 0x245ff12a4f0aa327, 0x0d31d8894b998120,
    0x5ed659d1731de516, 0xaafeda393a91494c, 0xf9195b6102152d7a,
    0xf822897f33b70d42, 0xabc508270b336974, 0x5fed8bcf42bfc52e,
    0x0c0a0a977a3ba118, 0x256423347ea8831f, 0x7683a26c462ce729,
    0x82ab21840fa04b73, 0xd14ca0dc37242f45, 0x80dc85b86ce40b03,
    0xd33b04e054606f35, 0x271387081decc36f, 0x74f406502568a759,
    0x5d9a2ff321fb855e, 0x0e7daeab197fe168, 0xfa552d4350f34d32,
    0xa9b2ac1b68772904, 0xa8897e0559d5093c, 0xfb6eff5d61516d0a,
    0x0f467cb528ddc150, 0x5ca1fded1059a566, 0x75cfd44e14ca8761,
    0x262855162c4ee357, 0xd200d6fe65c24f0d, 0x81e757a65d462b3b,
    0x71209c36d2420781, 0x22c71d6eeac663b7, 0xd6ef9e86a34acfed,
    0x85081fde9bceabdb, 0xac66367d9f5d89dc, 0xff81b725a7d9edea,
    0x0ba934cdee5541b0, 0x584eb595d6d12586, 0x5975678be77305be,
    0x0a92e6d3dff76188, 0xfeba653b967bcdd2, 0xad5de463aeffa9e4,
    0x8433cdc0aa6c8be3, 0xd7d44c9892e8efd5, 0x23fccf70db64438f,
    0x701b4e28e3e027b9, 0x218b6b4cb82003ff, 0x726cea1480a467c9,
    0x864469fcc928cb93, 0xd5a3e8a4f1acafa5, 0xfccdc107f53f8da2,
    0xaf2a405fcdbbe994, 0x5b02c3b7843745ce, 0x08e542efbcb321f8,
    0x09de90f18d1101c0, 0x5a3911a9b59565f6, 0xae119241fc19c9ac,
    0xfdf61319c49dad9a, 0xd4983abac00e8f9d, 0x877fbbe2f88aebab,
    0x7357380ab10647f1, 0x20b0b952898223c7, 0x32364aafa202007f,
    0x61d1cbf79a866449, 0x95f9481fd30ac813, 0xc61ec947eb8eac25,
    0xef70e0e4ef1d8e22, 0xbc9761bcd799ea14, 0x48bfe2549e15464e,
    0x1b58630ca6912278, 0x1a63b11297330240, 0x4984304aafb76676,
    0xbdacb3a2e63bca2c, 0xee4b32fadebfae1a, 0xc7251b59da2c8c1d,
    0x94c29a01e2a8e82b, 0x60ea19e9ab244471, 0x330d98b193a02047,
    0x629dbdd5c8600401, 0x317a3c8df0e46037, 0xc552bf65b968cc6d,
    0x96b53e3d81eca85b, 0xbfdb179e857f8a5c, 0xec3c96c6bdfbee6a,
    0x1814152ef4774230, 0x4bf39476ccf32606, 0x4ac84668fd51063e,
    0x192fc730c5d56208, 0xed0744d88c59ce52, 0xbee0c580b4ddaa64,
    0x978eec23b04e8863, 0xc4696d7b88caec55, 0x3041ee93c146400f,
    0x63a66fcbf9c22439, 0x9361a45b76c60883, 0xc08625034e426cb5,
    0x34aea6eb07cec0ef, 0x674927b33f4aa4d9, 0x4e270e103bd986de,
    0x1dc08f48035de2e8, 0xe9e80ca04ad14eb2, 0xba0f8df872552a84,
    0xbb345fe643f70abc, 0xe8d3debe7b736e8a, 0x1cfb5d5632ffc2d0,
    0x4f1cdc0e0a7ba6e6, 0x6672f5ad0ee884e1, 0x359574f5366ce0d7,
    0xc1bdf71d7fe04c8d, 0x925a7645476428bb, 0xc3ca53211ca40cfd,
    0x902dd279242068cb, 0x640551916dacc491, 0x37e2d0c95528a0a7,
    0x1e8cf96a51bb82a0, 0x4d6b7832693fe696, 0xb943fbda20b34acc,
    0xeaa47a8218372efa, 0xeb9fa89c29950ec2, 0xb87829c411116af4,
    0x4c50aa2c589dc6ae, 0x1fb72b746019a298, 0x36d902d7648a809f,
    0x653e838f5c0ee4a9, 0x91160067158248f3, 0xc2f1813f2d062cc5,
    0xe241386da4840f02, 0xb1a6b9359c006b34, 0x458e3addd58cc76e,
    0x1669bb85ed08a358, 0x3f079226e99b815f, 0x6ce0137ed11fe569,
    0x98c8909698934933, 0xcb2f11cea0172d05, 0xca14c3d091b50d3d,
    0x99f34288a931690b, 0x6ddbc160e0bdc551, 0x3e3c4038d839a167,
    0x1752699bdcaa8360, 0x44b5e8c3e42ee756, 0xb09d6b2bada24b0c,
    0xe37aea7395262f3a, 0xb2eacf17cee60b7c, 0xe10d4e4ff6626f4a,
    0x1525cda7bfeec310, 0x46c24cff876aa726, 0x6fac655c83f98521,
    0x3c4be404bb7de117, 0xc86367ecf2f14d4d, 0x9b84e6b4ca75297b,
    0x9abf34aafbd70943, 0xc958b5f2c3536d75, 0x3d70361a8adfc12f,
    0x6e97b742b25ba519, 0x47f99ee1b6c8871e, 0x141e1fb98e4ce328,
    0xe0369c51c7c04f72, 0xb3d11d09ff442b44, 0x4316d699704007fe,
    0x10f157c148c463c8, 0xe4d9d4290148cf92, 0xb73e557139ccaba4,
    0x9e507cd23d5f89a3, 0xcdb7fd8a05dbed95, 0x399f7e624c5741cf,
    0x6a78ff3a74d325f9, 0x6b432d24457105c1, 0x38a4ac7c7df561f7,
    0xcc8c2f943479cdad, 0x9f6baecc0cfda99b, 0xb605876f086e8b9c,
    0xe5e2063730eaefaa, 0x11ca85df796643f0, 0x422d048741e227c6,
    0x13bd21e31a220380, 0x405aa0bb22a667b6, 0xb47223536b2acbec,
    0xe795a20b53aeafda, 0xcefb8ba8573d8ddd, 0x9d1c0af06fb9e9eb,
    0x69348918263545b1, 0x3ad308401eb12187, 0x3be8da5e2f1301bf,
    0x680f5b0617976589, 0x9c27d8ee5e1bc9d3, 0xcfc059b6669fade5,
    0xe6ae7015620c8fe2, 0xb549f14d5a88ebd4, 0x416172a51304478e,
    0x1286f3fd2b8023b8,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹⁵¹, …, 128 = x¹⁴⁴`).
  // Generated by running `./build_table 10`.
  static inline const uint64_t TABLE_10[256] = {
    0x0000000000000000, 0x09abf11afca2d0d7, 0x1357e235f945a1ae,
    0x1afc132f05e77179, 0x26afc46bf28b435c, 0x2f0435710e29938b,
    0x35f8265e0bcee2f2, 0x3c53d744f76c3225, 0x4d5f88d7e51686b8,
    0x44f479cd19b4566f, 0x5e086ae21c532716, 0x57a39bf8e0f1f7c1,
    0x6bf04cbc179dc5e4, 0x625bbda6eb3f1533, 0x78a7ae89eed8644a,
    0x710c5f93127ab49d, 0x9abf11afca2d0d70, 0x9314e0b5368fdda7,
    0x89e8f39a3368acde, 0x80430280cfca7c09, 0xbc10d5c438a64e2c,
    0xb5bb24dec4049efb, 0xaf4737f1c1e3ef82, 0xa6ecc6eb3d413f55,
    0xd7e099782f3b8bc8, 0xde4b6862d3995b1f, 0xc4b77b4dd67e2a66,
    0xcd1c8a572adcfab1, 0xf14f5d13ddb0c894, 0xf8e4ac0921121843,
    0xe218bf2624f5693a, 0xebb34e3cd857b9ed, 0xa7a68c743b540465,
    0xae0d7d6ec7f6d4b2, 0xb4f16e41c211a5cb, 0xbd5a9f5b3eb3751c,
    0x8109481fc9df4739, 0x88a2b905357d97ee, 0x925eaa2a309ae697,
    0x9bf55b30cc383640, 0xeaf904a3de4282dd, 0xe352f5b922e0520a,
    0xf9aee69627072373, 0xf005178cdba5f3a4, 0xcc56c0c82cc9c181,
    0xc5fd31d2d06b1156, 0xdf0122fdd58c602f, 0xd6aad3e7292eb0f8,
    0x3d199ddbf1790915, 0x34b26cc10ddbd9c2, 0x2e4e7fee083ca8bb,
    0x27e58ef4f49e786c, 0x1bb659b003f24a49, 0x121da8aaff509a9e,
    0x08e1bb85fab7ebe7, 0x014a4a9f06153b30, 0x7046150c146f8fad,
    0x79ede416e8cd5f7a, 0x6311f739ed2a2e03, 0x6aba06231188fed4,
    0x56e9d167e6e4ccf1, 0x5f42207d1a461c26, 0x45be33521fa16d5f,
    0x4c15c248e303bd88, 0xdd95b7c3d9a6164f, 0xd43e46d92504c698,
    0xcec255f620e3b7e1, 0xc769a4ecdc416736, 0xfb3a73a82b2d5513,
    0xf29182b2d78f85c4, 0xe86d919dd268f4bd, 0xe1c660872eca246a,
    0x90ca3f143cb090f7, 0x9961ce0ec0124020, 0x839ddd21c5f53159,
    0x8a362c3b3957e18e, 0xb665fb7fce3bd3ab, 0xbfce0a653299037c,
    0xa532194a377e7205, 0xac99e850cbdca2d2, 0x472aa66c138b1b3f,
    0x4e815776ef29cbe8, 0x547d4459eaceba91, 0x5dd6b543166c6a46,
    0x61856207e1005863, 0x682e931d1da288b4, 0x72d280321845f9cd,
    0x7b797128e4e7291a, 0x0a752ebbf69d9d87, 0x03dedfa10a3f4d50,
    0x1922cc8e0fd83c29, 0x10893d94f37aecfe, 0x2cdaead00416dedb,
    0x25711bcaf8b40e0c, 0x3f8d08e5fd537f75, 0x3626f9ff01f1afa2,
    0x7a333bb7e2f2122a, 0x7398caad1e50c2fd, 0x6964d9821bb7b384,
    0x60cf2898e7156353, 0x5c9cffdc10795176, 0x55370ec6ecdb81a1,
    0x4fcb1de9e93cf0d8, 0x4660ecf3159e200f, 0x376cb36007e49492,
    0x3ec7427afb464445, 0x243b5155fea1353c, 0x2d90a04f0203e5eb,
    0x11c3770bf56fd7ce, 0x1868861109cd0719, 0x0294953e0c2a7660,
    0x0b3f6424f088a6b7, 0xe08c2a1828df1f5a, 0xe927db02d47dcf8d,
    0xf3dbc82dd19abef4, 0xfa7039372d386e23, 0xc623ee73da545c06,
    0xcf881f6926f68cd1, 0xd5740c462311fda8, 0xdcdffd5cdfb32d7f,
    0xadd3a2cfcdc999e2, 0xa47853d5316b4935, 0xbe8440fa348c384c,
    0xb72fb1e0c82ee89b, 0x8b7c66a43f42dabe, 0x82d797bec3e00a69,
    0x982b8491c6077b10, 0x9180758b3aa5abc7, 0x29f3c0ac1c42321b,
    0x205831b6e0e0e2cc, 0x3aa42299e50793b5, 0x330fd38319a54362,
    0x0f5c04c7eec97147, 0x06f7f5dd126ba190, 0x1c0be6f2178cd0e9,
    0x15a017e8eb2e003e, 0x64ac487bf954b4a3, 0x6d07b96105f66474,
    0x77fbaa4e0011150d, 0x7e505b54fcb3c5da, 0x42038c100bdff7ff,
    0x4ba87d0af77d2728, 0x51546e25f29a5651, 0x58ff9f3f0e388686,
    0xb34cd103d66f3f6b, 0xbae720192acdefbc, 0xa01b33362f2a9ec5,
    0xa9b0c22cd3884e12, 0x95e3156824e47c37, 0x9c48e472d846ace0,
    0x86b4f75ddda1dd99, 0x8f1f064721030d4e, 0xfe1359d43379b9d3,
    0xf7b8a8cecfdb6904, 0xed44bbe1ca3c187d, 0xe4ef4afb369ec8aa,
    0xd8bc9dbfc1f2fa8f, 0xd1176ca53d502a58, 0xcbeb7f8a38b75b21,
    0xc2408e90c4158bf6, 0x8e554cd82716367e, 0x87febdc2dbb4e6a9,
    0x9d02aeedde5397d0, 0x94a95ff722f14707, 0xa8fa88b3d59d7522,
    0xa15179a9293fa5f5, 0xbbad6a862cd8d48c, 0xb2069b9cd07a045b,
    0xc30ac40fc200b0c6, 0xcaa135153ea26011, 0xd05d263a3b451168,
    0xd9f6d720c7e7c1bf, 0xe5a50064308bf39a, 0xec0ef17ecc29234d,
    0xf6f2e251c9ce5234, 0xff59134b356c82e3, 0x14ea5d77ed3b3b0e,
    0x1d41ac6d1199ebd9, 0x07bdbf42147e9aa0, 0x0e164e58e8dc4a77,
    0x3245991c1fb07852, 0x3bee6806e312a885, 0x21127b29e6f5d9fc,
    0x28b98a331a57092b, 0x59b5d5a0082dbdb6, 0x501e24baf48f6d61,
    0x4ae23795f1681c18, 0x4349c68f0dcacccf, 0x7f1a11cbfaa6feea,
    0x76b1e0d106042e3d, 0x6c4df3fe03e35f44, 0x65e602e4ff418f93,
    0xf466776fc5e42454, 0xfdcd86753946f483, 0xe731955a3ca185fa,
    0xee9a6440c003552d, 0xd2c9b304376f6708, 0xdb62421ecbcdb7df,
    0xc19e5131ce2ac6a6, 0xc835a02b32881671, 0xb939ffb820f2a2ec,
    0xb0920ea2dc50723b, 0xaa6e1d8dd9b70342, 0xa3c5ec972515d395,
    0x9f963bd3d279e1b0, 0x963dcac92edb3167, 0x8cc1d9e62b3c401e,
    0x856a28fcd79e90c9, 0x6ed966c00fc92924, 0x677297daf36bf9f3,
    0x7d8e84f5f68c888a, 0x742575ef0a2e585d, 0x4876a2abfd426a78,
    0x41dd53b101e0baaf, 0x5b21409e0407cbd6, 0x528ab184f8a51b01,
    0x2386ee17eadfaf9c, 0x2a2d1f0d167d7f4b, 0x30d10c22139a0e32,
    0x397afd38ef38dee5, 0x05292a7c1854ecc0, 0x0c82db66e4f63c17,
    0x167ec849e1114d6e, 0x1fd539531db39db9, 0x53c0fb1bfeb02031,
    0x5a6b0a010212f0e6, 0x4097192e07f5819f, 0x493ce834fb575148,
    0x756f3f700c3b636d, 0x7cc4ce6af099b3ba, 0x6638dd45f57ec2c3,
    0x6f932c5f09dc1214, 0x1e9f73cc1ba6a689, 0x173482d6e704765e,
    0x0dc891f9e2e30727, 0x046360e31e41d7f0, 0x3830b7a7e92de5d5,
    0x319b46bd158f3502, 0x2b6755921068447b, 0x22cca488ecca94ac,
    0xc97feab4349d2d41, 0xc0d41baec83ffd96, 0xda280881cdd88cef,
    0xd383f99b317a5c38, 0xefd02edfc6166e1d, 0xe67bdfc53ab4beca,
    0xfc87ccea3f53cfb3, 0xf52c3df0c3f11f64, 0x84206263d18babf9,
    0x8d8b93792d297b2e, 0x9777805628ce0a57, 0x9edc714cd46cda80,
    0xa28fa6082300e8a5, 0xab245712dfa23872, 0xb1d8443dda45490b,
    0xb873b52726e799dc,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹⁵⁹, …, 128 = x¹⁵²`).
  // Generated by running `./build_table 11`.
  static inline const uint64_t TABLE_11[256] = {
    0x0000000000000000, 0xec32cffb23e3ed7d, 0x4abd30dde8c9c47f,
    0xa68fff26cb2a2902, 0x957a61bbd19388fe, 0x7948ae40f2706583,
    0xdfc75166395a4c81, 0x33f59e9d1ab9a1fc, 0xb82c6c5c0c290f79,
    0x541ea3a72fcae204, 0xf2915c81e4e0cb06, 0x1ea3937ac703267b,
    0x2d560de7ddba8787, 0xc164c21cfe596afa, 0x67eb3d3a357343f8,
    0x8bd9f2c11690ae85, 0xe2807793b75c0077, 0x0eb2b86894bfed0a,
    0xa83d474e5f95c408, 0x440f88b57c762975, 0x77fa162866cf8889,
    0x9bc8d9d3452c65f4, 0x3d4726f58e064cf6, 0xd175e90eade5a18b,
    0x5aac1bcfbb750f0e, 0xb69ed4349896e273, 0x10112b1253bccb71,
    0xfc23e4e9705f260c, 0xcfd67a746ae687f0, 0x23e4b58f49056a8d,
    0x856b4aa9822f438f, 0x69598552a1ccaef2, 0x57d8400cc1b61e6b,
    0xbbea8ff7e255f316, 0x1d6570d1297fda14, 0xf157bf2a0a9c3769,
    0xc2a221b710259695, 0x2e90ee4c33c67be8, 0x881f116af8ec52ea,
    0x642dde91db0fbf97, 0xeff42c50cd9f1112, 0x03c6e3abee7cfc6f,
    0xa5491c8d2556d56d, 0x497bd37606b53810, 0x7a8e4deb1c0c99ec,
    0x96bc82103fef7491, 0x30337d36f4c55d93, 0xdc01b2cdd726b0ee,
    0xb558379f76ea1e1c, 0x596af8645509f361, 0xffe507429e23da63,
    0x13d7c8b9bdc0371e, 0x20225624a77996e2, 0xcc1099df849a7b9f,
    0x6a9f66f94fb0529d, 0x86ada9026c53bfe0, 0x0d745bc37ac31165,
    0xe14694385920fc18, 0x47c96b1e920ad51a, 0xabfba4e5b1e93867,
    0x980e3a78ab50999b, 0x743cf58388b374e6, 0xd2b30aa543995de4,
    0x3e81c55e607ab099, 0xafb08019836c3cd6, 0x43824fe2a08fd1ab,
    0xe50db0c46ba5f8a9, 0x093f7f3f484615d4, 0x3acae1a252ffb428,
    0xd6f82e59711c5955, 0x7077d17fba367057, 0x9c451e8499d59d2a,
    0x179cec458f4533af, 0xfbae23beaca6ded2, 0x5d21dc98678cf7d0,
    0xb1131363446f1aad, 0x82e68dfe5ed6bb51, 0x6ed442057d35562c,
    0xc85bbd23b61f7f2e, 0x246972d895fc9253, 0x4d30f78a34303ca1,
    0xa102387117d3d1dc, 0x078dc757dcf9f8de, 0xebbf08acff1a15a3,
    0xd84a9631e5a3b45f, 0x347859cac6405922, 0x92f7a6ec0d6a7020,
    0x7ec569172e899d5d, 0xf51c9bd6381933d8, 0x192e542d1bfadea5,
    0xbfa1ab0bd0d0f7a7, 0x539364f0f3331ada, 0x6066fa6de98abb26,
    0x8c543596ca69565b, 0x2adbcab001437f59, 0xc6e9054b22a09224,
    0xf868c01542da22bd, 0x145a0fee6139cfc0, 0xb2d5f0c8aa13e6c2,
    0x5ee73f3389f00bbf, 0x6d12a1ae9349aa43, 0x81206e55b0aa473e,
    0x27af91737b806e3c, 0xcb9d5e8858638341, 0x4044ac494ef32dc4,
    0xac7663b26d10c0b9, 0x0af99c94a63ae9bb, 0xe6cb536f85d904c6,
    0xd53ecdf29f60a53a, 0x390c0209bc834847, 0x9f83fd2f77a96145,
    0x73b132d4544a8c38, 0x1ae8b786f58622ca, 0xf6da787dd665cfb7,
    0x5055875b1d4fe6b5, 0xbc6748a03eac0bc8, 0x8f92d63d2415aa34,
    0x63a019c607f64749, 0xc52fe6e0ccdc6e4b, 0x291d291bef3f8336,
    0xa2c4dbdaf9af2db3, 0x4ef61421da4cc0ce, 0xe879eb071166e9cc,
    0x044b24fc328504b1, 0x37beba61283ca54d, 0xdb8c759a0bdf4830,
    0x7d038abcc0f56132, 0x91314547e3168c4f, 0xcdb9af18a9d66729,
    0x218b60e38a358a54, 0x87049fc5411fa356, 0x6b36503e62fc4e2b,
    0x58c3cea37845efd7, 0xb4f101585ba602aa, 0x127efe7e908c2ba8,
    0xfe4c3185b36fc6d5, 0x7595c344a5ff6850, 0x99a70cbf861c852d,
    0x3f28f3994d36ac2f, 0xd31a3c626ed54152, 0xe0efa2ff746ce0ae,
    0x0cdd6d04578f0dd3, 0xaa5292229ca524d1, 0x46605dd9bf46c9ac,
    0x2f39d88b1e8a675e, 0xc30b17703d698a23, 0x6584e856f643a321,
    0x89b627add5a04e5c, 0xba43b930cf19efa0, 0x567176cbecfa02dd,
    0xf0fe89ed27d02bdf, 0x1ccc46160433c6a2, 0x9715b4d712a36827,
    0x7b277b2c3140855a, 0xdda8840afa6aac58, 0x319a4bf1d9894125,
    0x026fd56cc330e0d9, 0xee5d1a97e0d30da4, 0x48d2e5b12bf924a6,
    0xa4e02a4a081ac9db, 0x9a61ef1468607942, 0x765320ef4b83943f,
    0xd0dcdfc980a9bd3d, 0x3cee1032a34a5040, 0x0f1b8eafb9f3f1bc,
    0xe32941549a101cc1, 0x45a6be72513a35c3, 0xa994718972d9d8be,
    0x224d83486449763b, 0xce7f4cb347aa9b46, 0x68f0b3958c80b244,
    0x84c27c6eaf635f39, 0xb737e2f3b5dafec5, 0x5b052d08963913b8,
    0xfd8ad22e5d133aba, 0x11b81dd57ef0d7c7, 0x78e19887df3c7935,
    0x94d3577cfcdf9448, 0x325ca85a37f5bd4a, 0xde6e67a114165037,
    0xed9bf93c0eaff1cb, 0x01a936c72d4c1cb6, 0xa726c9e1e66635b4,
    0x4b14061ac585d8c9, 0xc0cdf4dbd315764c, 0x2cff3b20f0f69b31,
    0x8a70c4063bdcb233, 0x66420bfd183f5f4e, 0x55b795600286feb2,
    0xb9855a9b216513cf, 0x1f0aa5bdea4f3acd, 0xf3386a46c9acd7b0,
    0x62092f012aba5bff, 0x8e3be0fa0959b682, 0x28b41fdcc2739f80,
    0xc486d027e19072fd, 0xf7734ebafb29d301, 0x1b418141d8ca3e7c,
    0xbdce7e6713e0177e, 0x51fcb19c3003fa03, 0xda25435d26935486,
    0x36178ca60570b9fb, 0x90987380ce5a90f9, 0x7caabc7bedb97d84,
    0x4f5f22e6f700dc78, 0xa36ded1dd4e33105, 0x05e2123b1fc91807,
    0xe9d0ddc03c2af57a, 0x808958929de65b88, 0x6cbb9769be05b6f5,
    0xca34684f752f9ff7, 0x2606a7b456cc728a, 0x15f339294c75d376,
    0xf9c1f6d26f963e0b, 0x5f4e09f4a4bc1709, 0xb37cc60f875ffa74,
    0x38a534ce91cf54f1, 0xd497fb35b22cb98c, 0x721804137906908e,
    0x9e2acbe85ae57df3, 0xaddf5575405cdc0f, 0x41ed9a8e63bf3172,
    0xe76265a8a8951870, 0x0b50aa538b76f50d, 0x35d16f0deb0c4594,
    0xd9e3a0f6c8efa8e9, 0x7f6c5fd003c581eb, 0x935e902b20266c96,
    0xa0ab0eb63a9fcd6a, 0x4c99c14d197c2017, 0xea163e6bd2560915,
    0x0624f190f1b5e468, 0x8dfd0351e7254aed, 0x61cfccaac4c6a790,
    0xc740338c0fec8e92, 0x2b72fc772c0f63ef, 0x188762ea36b6c213,
    0xf4b5ad1115552f6e, 0x523a5237de7f066c, 0xbe089dccfd9ceb11,
    0xd751189e5c5045e3, 0x3b63d7657fb3a89e, 0x9dec2843b499819c,
    0x71dee7b8977a6ce1, 0x422b79258dc3cd1d, 0xae19b6deae202060,
    0x089649f8650a0962, 0xe4a4860346e9e41f, 0x6f7d74c250794a9a,
    0x834fbb39739aa7e7, 0x25c0441fb8b08ee5, 0xc9f28be49b536398,
    0xfa07157981eac264, 0x1635da82a2092f19, 0xb0ba25a46923061b,
    0x5c88ea5f4ac0eb66,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹⁶⁷, …, 128 = x¹⁶⁰`).
  // Generated by running `./build_table 12`.
  static inline const uint64_t TABLE_12[256] = {
    0x0000000000000000, 0xdda9f27ee08373ad, 0x298b4bd66e08f9df,
    0xf422b9a88e8b8a72, 0x531697acdc11f3be, 0x8ebf65d23c928013,
    0x7a9ddc7ab2190a61, 0xa7342e04529a79cc, 0xa62d2f59b823e77c,
    0x7b84dd2758a094d1, 0x8fa6648fd62b1ea3, 0x520f96f136a86d0e,
    0xf53bb8f5643214c2, 0x28924a8b84b1676f, 0xdcb0f3230a3aed1d,
    0x0119015deab99eb0, 0xde82f198df49d07d, 0x032b03e63fcaa3d0,
    0xf709ba4eb14129a2, 0x2aa0483051c25a0f, 0x8d946634035823c3,
    0x503d944ae3db506e, 0xa41f2de26d50da1c, 0x79b6df9c8dd3a9b1,
    0x78afdec1676a3701, 0xa5062cbf87e944ac, 0x512495170962cede,
    0x8c8d6769e9e1bd73, 0x2bb9496dbb7bc4bf, 0xf610bb135bf8b712,
    0x023202bbd5733d60, 0xdf9bf0c535f04ecd, 0x2fdd4c1a119dbe7f,
    0xf274be64f11ecdd2, 0x065607cc7f9547a0, 0xdbfff5b29f16340d,
    0x7ccbdbb6cd8c4dc1, 0xa16229c82d0f3e6c, 0x55409060a384b41e,
    0x88e9621e4307c7b3, 0x89f06343a9be5903, 0x5459913d493d2aae,
    0xa07b2895c7b6a0dc, 0x7dd2daeb2735d371, 0xdae6f4ef75afaabd,
    0x074f0691952cd910, 0xf36dbf391ba75362, 0x2ec44d47fb2420cf,
    0xf15fbd82ced46e02, 0x2cf64ffc2e571daf, 0xd8d4f654a0dc97dd,
    0x057d042a405fe470, 0xa2492a2e12c59dbc, 0x7fe0d850f246ee11,
    0x8bc261f87ccd6463, 0x566b93869c4e17ce, 0x577292db76f7897e,
    0x8adb60a59674fad3, 0x7ef9d90d18ff70a1, 0xa3502b73f87c030c,
    0x04640577aae67ac0, 0xd9cdf7094a65096d, 0x2def4ea1c4ee831f,
    0xf046bcdf246df0b2, 0x5fba9834233b7cfe, 0x82136a4ac3b80f53,
    0x7631d3e24d338521, 0xab98219cadb0f68c, 0x0cac0f98ff2a8f40,
    0xd105fde61fa9fced, 0x2527444e9122769f, 0xf88eb63071a10532,
    0xf997b76d9b189b82, 0x243e45137b9be82f, 0xd01cfcbbf510625d,
    0x0db50ec5159311f0, 0xaa8120c14709683c, 0x7728d2bfa78a1b91,
    0x830a6b17290191e3, 0x5ea39969c982e24e, 0x813869acfc72ac83,
    0x5c919bd21cf1df2e, 0xa8b3227a927a555c, 0x751ad00472f926f1,
    0xd22efe0020635f3d, 0x0f870c7ec0e02c90, 0xfba5b5d64e6ba6e2,
    0x260c47a8aee8d54f, 0x271546f544514bff, 0xfabcb48ba4d23852,
    0x0e9e0d232a59b220, 0xd337ff5dcadac18d, 0x7403d1599840b841,
    0xa9aa232778c3cbec, 0x5d889a8ff648419e, 0x802168f116cb3233,
    0x7067d42e32a6c281, 0xadce2650d225b12c, 0x59ec9ff85cae3b5e,
    0x84456d86bc2d48f3, 0x23714382eeb7313f, 0xfed8b1fc0e344292,
    0x0afa085480bfc8e0, 0xd753fa2a603cbb4d, 0xd64afb778a8525fd,
    0x0be309096a065650, 0xffc1b0a1e48ddc22, 0x226842df040eaf8f,
    0x855c6cdb5694d643, 0x58f59ea5b617a5ee, 0xacd7270d389c2f9c,
    0x717ed573d81f5c31, 0xaee525b6edef12fc, 0x734cd7c80d6c6151,
    0x876e6e6083e7eb23, 0x5ac79c1e6364988e, 0xfdf3b21a31fee142,
    0x205a4064d17d92ef, 0xd478f9cc5ff6189d, 0x09d10bb2bf756b30,
    0x08c80aef55ccf580, 0xd561f891b54f862d, 0x214341393bc40c5f,
    0xfceab347db477ff2, 0x5bde9d4389dd063e, 0x86776f3d695e7593,
    0x7255d695e7d5ffe1, 0xaffc24eb07568c4c, 0xbf7530684676f9fc,
    0x62dcc216a6f58a51, 0x96fe7bbe287e0023, 0x4b5789c0c8fd738e,
    0xec63a7c49a670a42, 0x31ca55ba7ae479ef, 0xc5e8ec12f46ff39d,
    0x18411e6c14ec8030, 0x19581f31fe551e80, 0xc4f1ed4f1ed66d2d,
    0x30d354e7905de75f, 0xed7aa69970de94f2, 0x4a4e889d2244ed3e,
    0x97e77ae3c2c79e93, 0x63c5c34b4c4c14e1, 0xbe6c3135accf674c,
    0x61f7c1f0993f2981, 0xbc5e338e79bc5a2c, 0x487c8a26f737d05e,
    0x95d5785817b4a3f3, 0x32e1565c452eda3f, 0xef48a422a5ada992,
    0x1b6a1d8a2b2623e0, 0xc6c3eff4cba5504d, 0xc7daeea9211ccefd,
    0x1a731cd7c19fbd50, 0xee51a57f4f143722, 0x33f85701af97448f,
    0x94cc7905fd0d3d43, 0x49658b7b1d8e4eee, 0xbd4732d39305c49c,
    0x60eec0ad7386b731, 0x90a87c7257eb4783, 0x4d018e0cb768342e,
    0xb92337a439e3be5c, 0x648ac5dad960cdf1, 0xc3beebde8bfab43d,
    0x1e1719a06b79c790, 0xea35a008e5f24de2, 0x379c527605713e4f,
    0x3685532befc8a0ff, 0xeb2ca1550f4bd352, 0x1f0e18fd81c05920,
    0xc2a7ea8361432a8d, 0x6593c48733d95341, 0xb83a36f9d35a20ec,
    0x4c188f515dd1aa9e, 0x91b17d2fbd52d933, 0x4e2a8dea88a297fe,
    0x93837f946821e453, 0x67a1c63ce6aa6e21, 0xba08344206291d8c,
    0x1d3c1a4654b36440, 0xc095e838b43017ed, 0x34b751903abb9d9f,
    0xe91ea3eeda38ee32, 0xe807a2b330817082, 0x35ae50cdd002032f,
    0xc18ce9655e89895d, 0x1c251b1bbe0afaf0, 0xbb11351fec90833c,
    0x66b8c7610c13f091, 0x929a7ec982987ae3, 0x4f338cb7621b094e,
    0xe0cfa85c654d8502, 0x3d665a2285cef6af, 0xc944e38a0b457cdd,
    0x14ed11f4ebc60f70, 0xb3d93ff0b95c76bc, 0x6e70cd8e59df0511,
    0x9a527426d7548f63, 0x47fb865837d7fcce, 0x46e28705dd6e627e,
    0x9b4b757b3ded11d3, 0x6f69ccd3b3669ba1, 0xb2c03ead53e5e80c,
    0x15f410a9017f91c0, 0xc85de2d7e1fce26d, 0x3c7f5b7f6f77681f,
    0xe1d6a9018ff41bb2, 0x3e4d59c4ba04557f, 0xe3e4abba5a8726d2,
    0x17c61212d40caca0, 0xca6fe06c348fdf0d, 0x6d5bce686615a6c1,
    0xb0f23c168696d56c, 0x44d085be081d5f1e, 0x997977c0e89e2cb3,
    0x9860769d0227b203, 0x45c984e3e2a4c1ae, 0xb1eb3d4b6c2f4bdc,
    0x6c42cf358cac3871, 0xcb76e131de3641bd, 0x16df134f3eb53210,
    0xe2fdaae7b03eb862, 0x3f54589950bdcbcf, 0xcf12e44674d03b7d,
    0x12bb1638945348d0, 0xe699af901ad8c2a2, 0x3b305deefa5bb10f,
    0x9c0473eaa8c1c8c3, 0x41ad81944842bb6e, 0xb58f383cc6c9311c,
    0x6826ca42264a42b1, 0x693fcb1fccf3dc01, 0xb49639612c70afac,
    0x40b480c9a2fb25de, 0x9d1d72b742785673, 0x3a295cb310e22fbf,
    0xe780aecdf0615c12, 0x13a217657eead660, 0xce0be51b9e69a5cd,
    0x119015deab99eb00, 0xcc39e7a04b1a98ad, 0x381b5e08c59112df,
    0xe5b2ac7625126172, 0x42868272778818be, 0x9f2f700c970b6b13,
    0x6b0dc9a41980e161, 0xb6a43bdaf90392cc, 0xb7bd3a8713ba0c7c,
    0x6a14c8f9f3397fd1, 0x9e3671517db2f5a3, 0x439f832f9d31860e,
    0xe4abad2bcfabffc2, 0x39025f552f288c6f, 0xcd20e6fda1a3061d,
    0x10891483412075b0,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹⁷⁵, …, 128 = x¹⁶⁸`).
  // Generated by running `./build_table 13`.
  static inline const uint64_t TABLE_13[256] = {
    0x0000000000000000, 0x0dd9b4240837fd99, 0x1bb36848106ffb32,
    0x166adc6c185806ab, 0x3766d09020dff664, 0x3abf64b428e80bfd,
    0x2cd5b8d830b00d56, 0x210c0cfc3887f0cf, 0x6ecda12041bfecc8,
    0x6314150449881151, 0x757ec96851d017fa, 0x78a77d4c59e7ea63,
    0x59ab71b061601aac, 0x5472c5946957e735, 0x421819f8710fe19e,
    0x4fc1addc79381c07, 0xdd9b4240837fd990, 0xd042f6648b482409,
    0xc6282a08931022a2, 0xcbf19e2c9b27df3b, 0xeafd92d0a3a02ff4,
    0xe72426f4ab97d26d, 0xf14efa98b3cfd4c6, 0xfc974ebcbbf8295f,
    0xb356e360c2c03558, 0xbe8f5744caf7c8c1, 0xa8e58b28d2afce6a,
    0xa53c3f0cda9833f3, 0x843033f0e21fc33c, 0x89e987d4ea283ea5,
    0x9f835bb8f270380e, 0x925aef9cfa47c597, 0x29ee2baaa9f1ada5,
    0x24379f8ea1c6503c, 0x325d43e2b99e5697, 0x3f84f7c6b1a9ab0e,
    0x1e88fb3a892e5bc1, 0x13514f1e8119a658, 0x053b93729941a0f3,
    0x08e2275691765d6a, 0x47238a8ae84e416d, 0x4afa3eaee079bcf4,
    0x5c90e2c2f821ba5f, 0x514956e6f01647c6, 0x70455a1ac891b709,
    0x7d9cee3ec0a64a90, 0x6bf63252d8fe4c3b, 0x662f8676d0c9b1a2,
    0xf47569ea2a8e7435, 0xf9acddce22b989ac, 0xefc601a23ae18f07,
    0xe21fb58632d6729e, 0xc313b97a0a518251, 0xceca0d5e02667fc8,
    0xd8a0d1321a3e7963, 0xd5796516120984fa, 0x9ab8c8ca6b3198fd,
    0x97617cee63066564, 0x810ba0827b5e63cf, 0x8cd214a673699e56,
    0xadde185a4bee6e99, 0xa007ac7e43d99300, 0xb66d70125b8195ab,
    0xbbb4c43653b66832, 0x53dc575553e35b4a, 0x5e05e3715bd4a6d3,
    0x486f3f1d438ca078, 0x45b68b394bbb5de1, 0x64ba87c5733cad2e,
    0x696333e17b0b50b7, 0x7f09ef8d6353561c, 0x72d05ba96b64ab85,
    0x3d11f675125cb782, 0x30c842511a6b4a1b, 0x26a29e3d02334cb0,
    0x2b7b2a190a04b129, 0x0a7726e5328341e6, 0x07ae92c13ab4bc7f,
    0x11c44ead22ecbad4, 0x1c1dfa892adb474d, 0x8e471515d09c82da,
    0x839ea131d8ab7f43, 0x95f47d5dc0f379e8, 0x982dc979c8c48471,
    0xb921c585f04374be, 0xb4f871a1f8748927, 0xa292adcde02c8f8c,
    0xaf4b19e9e81b7215, 0xe08ab43591236e12, 0xed5300119914938b,
    0xfb39dc7d814c9520, 0xf6e06859897b68b9, 0xd7ec64a5b1fc9876,
    0xda35d081b9cb65ef, 0xcc5f0ceda1936344, 0xc186b8c9a9a49edd,
    0x7a327cfffa12f6ef, 0x77ebc8dbf2250b76, 0x618114b7ea7d0ddd,
    0x6c58a093e24af044, 0x4d54ac6fdacd008b, 0x408d184bd2fafd12,
    0x56e7c427caa2fbb9, 0x5b3e7003c2950620, 0x14ffdddfbbad1a27,
    0x192669fbb39ae7be, 0x0f4cb597abc2e115, 0x029501b3a3f51c8c,
    0x23990d4f9b72ec43, 0x2e40b96b934511da, 0x382a65078b1d1771,
    0x35f3d123832aeae8, 0xa7a93ebf796d2f7f, 0xaa708a9b715ad2e6,
    0xbc1a56f76902d44d, 0xb1c3e2d3613529d4, 0x90cfee2f59b2d91b,
    0x9d165a0b51852482, 0x8b7c866749dd2229, 0x86a5324341eadfb0,
    0xc9649f9f38d2c3b7, 0xc4bd2bbb30e53e2e, 0xd2d7f7d728bd3885,
    0xdf0e43f3208ac51c, 0xfe024f0f180d35d3, 0xf3dbfb2b103ac84a,
    0xe5b127470862cee1, 0xe868936300553378, 0xa7b8aeaaa7c6b694,
    0xaa611a8eaff14b0d, 0xbc0bc6e2b7a94da6, 0xb1d272c6bf9eb03f,
    0x90de7e3a871940f0, 0x9d07ca1e8f2ebd69, 0x8b6d16729776bbc2,
    0x86b4a2569f41465b, 0xc9750f8ae6795a5c, 0xc4acbbaeee4ea7c5,
    0xd2c667c2f616a16e, 0xdf1fd3e6fe215cf7, 0xfe13df1ac6a6ac38,
    0xf3ca6b3ece9151a1, 0xe5a0b752d6c9570a, 0xe8790376defeaa93,
    0x7a23ecea24b96f04, 0x77fa58ce2c8e929d, 0x619084a234d69436,
    0x6c4930863ce169af, 0x4d453c7a04669960, 0x409c885e0c5164f9,
    0x56f6543214096252, 0x5b2fe0161c3e9fcb, 0x14ee4dca650683cc,
    0x1937f9ee6d317e55, 0x0f5d2582756978fe, 0x028491a67d5e8567,
    0x23889d5a45d975a8, 0x2e51297e4dee8831, 0x383bf51255b68e9a,
    0x35e241365d817303, 0x8e5685000e371b31, 0x838f31240600e6a8,
    0x95e5ed481e58e003, 0x983c596c166f1d9a, 0xb93055902ee8ed55,
    0xb4e9e1b426df10cc, 0xa2833dd83e871667, 0xaf5a89fc36b0ebfe,
    0xe09b24204f88f7f9, 0xed42900447bf0a60, 0xfb284c685fe70ccb,
    0xf6f1f84c57d0f152, 0xd7fdf4b06f57019d, 0xda2440946760fc04,
    0xcc4e9cf87f38faaf, 0xc19728dc770f0736, 0x53cdc7408d48c2a1,
    0x5e147364857f3f38, 0x487eaf089d273993, 0x45a71b2c9510c40a,
    0x64ab17d0ad9734c5, 0x6972a3f4a5a0c95c, 0x7f187f98bdf8cff7,
    0x72c1cbbcb5cf326e, 0x3d006660ccf72e69, 0x30d9d244c4c0d3f0,
    0x26b30e28dc98d55b, 0x2b6aba0cd4af28c2, 0x0a66b6f0ec28d80d,
    0x07bf02d4e41f2594, 0x11d5deb8fc47233f, 0x1c0c6a9cf470dea6,
    0xf464f9fff425edde, 0xf9bd4ddbfc121047, 0xefd791b7e44a16ec,
    0xe20e2593ec7deb75, 0xc302296fd4fa1bba, 0xcedb9d4bdccde623,
    0xd8b14127c495e088, 0xd568f503cca21d11, 0x9aa958dfb59a0116,
    0x9770ecfbbdadfc8f, 0x811a3097a5f5fa24, 0x8cc384b3adc207bd,
    0xadcf884f9545f772, 0xa0163c6b9d720aeb, 0xb67ce007852a0c40,
    0xbba554238d1df1d9, 0x29ffbbbf775a344e, 0x24260f9b7f6dc9d7,
    0x324cd3f76735cf7c, 0x3f9567d36f0232e5, 0x1e996b2f5785c22a,
    0x1340df0b5fb23fb3, 0x052a036747ea3918, 0x08f3b7434fddc481,
    0x47321a9f36e5d886, 0x4aebaebb3ed2251f, 0x5c8172d7268a23b4,
    0x5158c6f32ebdde2d, 0x7054ca0f163a2ee2, 0x7d8d7e2b1e0dd37b,
    0x6be7a2470655d5d0, 0x663e16630e622849, 0xdd8ad2555dd4407b,
    0xd053667155e3bde2, 0xc639ba1d4dbbbb49, 0xcbe00e39458c46d0,
    0xeaec02c57d0bb61f, 0xe735b6e1753c4b86, 0xf15f6a8d6d644d2d,
    0xfc86dea96553b0b4, 0xb34773751c6bacb3, 0xbe9ec751145c512a,
    0xa8f41b3d0c045781, 0xa52daf190433aa18, 0x8421a3e53cb45ad7,
    0x89f817c13483a74e, 0x9f92cbad2cdba1e5, 0x924b7f8924ec5c7c,
    0x00119015deab99eb, 0x0dc82431d69c6472, 0x1ba2f85dcec462d9,
    0x167b4c79c6f39f40, 0x37774085fe746f8f, 0x3aaef4a1f6439216,
    0x2cc428cdee1b94bd, 0x211d9ce9e62c6924, 0x6edc31359f147523,
    0x63058511972388ba, 0x756f597d8f7b8e11, 0x78b6ed59874c7388,
    0x59bae1a5bfcb8347, 0x54635581b7fc7ede, 0x420989edafa47875,
    0x4fd03dc9a79385ec,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹⁸³, …, 128 = x¹⁷⁶`).
  // Generated by running `./build_table 14`.
  static inline const uint64_t TABLE_14[256] = {
    0x0000000000000000, 0xf075e4ae5e05bdff, 0x723366771305657b,
    0x824682d94d00d884, 0xe466ccee260acaf6, 0x14132840780f7709,
    0x9655aa99350faf8d, 0x66204e376b0a1272, 0x5a1536f7e31b8b69,
    0xaa60d259bd1e3696, 0x28265080f01eee12, 0xd853b42eae1b53ed,
    0xbe73fa19c511419f, 0x4e061eb79b14fc60, 0xcc409c6ed61424e4,
    0x3c3578c08811991b, 0xb42a6defc63716d2, 0x445f89419832ab2d,
    0xc6190b98d53273a9, 0x366cef368b37ce56, 0x504ca101e03ddc24,
    0xa03945afbe3861db, 0x227fc776f338b95f, 0xd20a23d8ad3d04a0,
    0xee3f5b18252c9dbb, 0x1e4abfb67b292044, 0x9c0c3d6f3629f8c0,
    0x6c79d9c1682c453f, 0x0a5997f60326574d, 0xfa2c73585d23eab2,
    0x786af18110233236, 0x881f152f4e268fc9, 0xfa8c74f423603321,
    0x0af9905a7d658ede, 0x88bf12833065565a, 0x78caf62d6e60eba5,
    0x1eeab81a056af9d7, 0xee9f5cb45b6f4428, 0x6cd9de6d166f9cac,
    0x9cac3ac3486a2153, 0xa0994203c07bb848, 0x50eca6ad9e7e05b7,
    0xd2aa2474d37edd33, 0x22dfc0da8d7b60cc, 0x44ff8eede67172be,
    0xb48a6a43b874cf41, 0x36cce89af57417c5, 0xc6b90c34ab71aa3a,
    0x4ea6191be55725f3, 0xbed3fdb5bb52980c, 0x3c957f6cf6524088,
    0xcce09bc2a857fd77, 0xaac0d5f5c35def05, 0x5ab5315b9d5852fa,
    0xd8f3b382d0588a7e, 0x2886572c8e5d3781, 0x14b32fec064cae9a,
    0xe4c6cb4258491365, 0x6680499b1549cbe1, 0x96f5ad354b4c761e,
    0xf0d5e3022046646c, 0x00a007ac7e43d993, 0x82e6857533430117,
    0x729361db6d46bce8, 0x67c046c3e9ce78c7, 0x97b5a26db7cbc538,
    0x15f320b4facb1dbc, 0xe586c41aa4cea043, 0x83a68a2dcfc4b231,
    0x73d36e8391c10fce, 0xf195ec5adcc1d74a, 0x01e008f482c46ab5,
    0x3dd570340ad5f3ae, 0xcda0949a54d04e51, 0x4fe6164319d096d5,
    0xbf93f2ed47d52b2a, 0xd9b3bcda2cdf3958, 0x29c6587472da84a7,
    0xab80daad3fda5c23, 0x5bf53e0361dfe1dc, 0xd3ea2b2c2ff96e15,
    0x239fcf8271fcd3ea, 0xa1d94d5b3cfc0b6e, 0x51aca9f562f9b691,
    0x378ce7c209f3a4e3, 0xc7f9036c57f6191c, 0x45bf81b51af6c198,
    0xb5ca651b44f37c67, 0x89ff1ddbcce2e57c, 0x798af97592e75883,
    0xfbcc7bacdfe78007, 0x0bb99f0281e23df8, 0x6d99d135eae82f8a,
    0x9dec359bb4ed9275, 0x1faab742f9ed4af1, 0xefdf53eca7e8f70e,
    0x9d4c3237caae4be6, 0x6d39d69994abf619, 0xef7f5440d9ab2e9d,
    0x1f0ab0ee87ae9362, 0x792afed9eca48110, 0x895f1a77b2a13cef,
    0x0b1998aeffa1e46b, 0xfb6c7c00a1a45994, 0xc75904c029b5c08f,
    0x372ce06e77b07d70, 0xb56a62b73ab0a5f4, 0x451f861964b5180b,
    0x233fc82e0fbf0a79, 0xd34a2c8051bab786, 0x510cae591cba6f02,
    0xa1794af742bfd2fd, 0x29665fd80c995d34, 0xd913bb76529ce0cb,
    0x5b5539af1f9c384f, 0xab20dd01419985b0, 0xcd0093362a9397c2,
    0x3d75779874962a3d, 0xbf33f5413996f2b9, 0x4f4611ef67934f46,
    0x7373692fef82d65d, 0x83068d81b1876ba2, 0x01400f58fc87b326,
    0xf135ebf6a2820ed9, 0x9715a5c1c9881cab, 0x6760416f978da154,
    0xe526c3b6da8d79d0, 0x155327188488c42f, 0xcf808d87d39cf18e,
    0x3ff569298d994c71, 0xbdb3ebf0c09994f5, 0x4dc60f5e9e9c290a,
    0x2be64169f5963b78, 0xdb93a5c7ab938687, 0x59d5271ee6935e03,
    0xa9a0c3b0b896e3fc, 0x9595bb7030877ae7, 0x65e05fde6e82c718,
    0xe7a6dd0723821f9c, 0x17d339a97d87a263, 0x71f3779e168db011,
    0x8186933048880dee, 0x03c011e90588d56a, 0xf3b5f5475b8d6895,
    0x7baae06815abe75c, 0x8bdf04c64bae5aa3, 0x0999861f06ae8227,
    0xf9ec62b158ab3fd8, 0x9fcc2c8633a12daa, 0x6fb9c8286da49055,
    0xedff4af120a448d1, 0x1d8aae5f7ea1f52e, 0x21bfd69ff6b06c35,
    0xd1ca3231a8b5d1ca, 0x538cb0e8e5b5094e, 0xa3f95446bbb0b4b1,
    0xc5d91a71d0baa6c3, 0x35acfedf8ebf1b3c, 0xb7ea7c06c3bfc3b8,
    0x479f98a89dba7e47, 0x350cf973f0fcc2af, 0xc5791dddaef97f50,
    0x473f9f04e3f9a7d4, 0xb74a7baabdfc1a2b, 0xd16a359dd6f60859,
    0x211fd13388f3b5a6, 0xa35953eac5f36d22, 0x532cb7449bf6d0dd,
    0x6f19cf8413e749c6, 0x9f6c2b2a4de2f439, 0x1d2aa9f300e22cbd,
    0xed5f4d5d5ee79142, 0x8b7f036a35ed8330, 0x7b0ae7c46be83ecf,
    0xf94c651d26e8e64b, 0x093981b378ed5bb4, 0x8126949c36cbd47d,
    0x7153703268ce6982, 0xf315f2eb25ceb106, 0x036016457bcb0cf9,
    0x6540587210c11e8b, 0x9535bcdc4ec4a374, 0x17733e0503c47bf0,
    0xe706daab5dc1c60f, 0xdb33a26bd5d05f14, 0x2b4646c58bd5e2eb,
    0xa900c41cc6d53a6f, 0x597520b298d08790, 0x3f556e85f3da95e2,
    0xcf208a2baddf281d, 0x4d6608f2e0dff099, 0xbd13ec5cbeda4d66,
    0xa840cb443a528949, 0x58352fea645734b6, 0xda73ad332957ec32,
    0x2a06499d775251cd, 0x4c2607aa1c5843bf, 0xbc53e304425dfe40,
    0x3e1561dd0f5d26c4, 0xce60857351589b3b, 0xf255fdb3d9490220,
    0x0220191d874cbfdf, 0x80669bc4ca4c675b, 0x70137f6a9449daa4,
    0x1633315dff43c8d6, 0xe646d5f3a1467529, 0x6400572aec46adad,
    0x9475b384b2431052, 0x1c6aa6abfc659f9b, 0xec1f4205a2602264,
    0x6e59c0dcef60fae0, 0x9e2c2472b165471f, 0xf80c6a45da6f556d,
    0x08798eeb846ae892, 0x8a3f0c32c96a3016, 0x7a4ae89c976f8de9,
    0x467f905c1f7e14f2, 0xb60a74f2417ba90d, 0x344cf62b0c7b7189,
    0xc4391285527ecc76, 0xa2195cb23974de04, 0x526cb81c677163fb,
    0xd02a3ac52a71bb7f, 0x205fde6b74740680, 0x52ccbfb01932ba68,
    0xa2b95b1e47370797, 0x20ffd9c70a37df13, 0xd08a3d69543262ec,
    0xb6aa735e3f38709e, 0x46df97f0613dcd61, 0xc49915292c3d15e5,
    0x34ecf1877238a81a, 0x08d98947fa293101, 0xf8ac6de9a42c8cfe,
    0x7aeaef30e92c547a, 0x8a9f0b9eb729e985, 0xecbf45a9dc23fbf7,
    0x1ccaa10782264608, 0x9e8c23decf269e8c, 0x6ef9c77091232373,
    0xe6e6d25fdf05acba, 0x169336f181001145, 0x94d5b428cc00c9c1,
    0x64a050869205743e, 0x02801eb1f90f664c, 0xf2f5fa1fa70adbb3,
    0x70b378c6ea0a0337, 0x80c69c68b40fbec8, 0xbcf3e4a83c1e27d3,
    0x4c860006621b9a2c, 0xcec082df2f1b42a8, 0x3eb56671711eff57,
    0x589528461a14ed25, 0xa8e0cce8441150da, 0x2aa64e310911885e,
    0xdad3aa9f571435a1,
  };

  // CRC table for the ECMA polynomial in the range (`1 = x¹⁹¹, …, 128 = x¹⁸⁴`).
  // Generated by running `./build_table 15`.
  static inline const uint64_t TABLE_15[256] = {
    0x0000000000000000, 0xe05dd497ca393ae4, 0x526306043b7c6b4d,
    0xb23ed293f14551a9, 0xa4c60c0876f8d69a, 0x449bd89fbcc1ec7e,
    0xf6a50a0c4d84bdd7, 0x16f8de9b87bd8733, 0xdb54b73b42ffb3b1,
    0x3b0963ac88c68955, 0x8937b13f7983d8fc, 0x696a65a8b3bae218,
    0x7f92bb333407652b, 0x9fcf6fa4fe3e5fcf, 0x2df1bd370f7b0e66,
    0xcdac69a0c5423482, 0x2471c15d2af179e7, 0xc42c15cae0c84303,
    0x7612c759118d12aa, 0x964f13cedbb4284e, 0x80b7cd555c09af7d,
    0x60ea19c296309599, 0xd2d4cb516775c430, 0x32891fc6ad4cfed4,
    0xff257666680eca56, 0x1f78a2f1a237f0b2, 0xad4670625372a11b,
    0x4d1ba4f5994b9bff, 0x5be37a6e1ef61ccc, 0xbbbeaef9d4cf2628,
    0x09807c6a258a7781, 0xe9dda8fdefb34d65, 0x48e382ba55e2f3ce,
    0xa8be562d9fdbc92a, 0x1a8084be6e9e9883, 0xfadd5029a4a7a267,
    0xec258eb2231a2554, 0x0c785a25e9231fb0, 0xbe4688b618664e19,
    0x5e1b5c21d25f74fd, 0x93b73581171d407f, 0x73eae116dd247a9b,
    0xc1d433852c612b32, 0x2189e712e65811d6, 0x3771398961e596e5,
    0xd72ced1eabdcac01, 0x65123f8d5a99fda8, 0x854feb1a90a0c74c,
    0x6c9243e77f138a29, 0x8ccf9770b52ab0cd, 0x3ef145e3446fe164,
    0xdeac91748e56db80, 0xc8544fef09eb5cb3, 0x28099b78c3d26657,
    0x9a3749eb329737fe, 0x7a6a9d7cf8ae0d1a, 0xb7c6f4dc3dec3998,
    0x579b204bf7d5037c, 0xe5a5f2d8069052d5, 0x05f8264fcca96831,
    0x1300f8d44b14ef02, 0xf35d2c43812dd5e6, 0x4163fed07068844f,
    0xa13e2a47ba51beab, 0x91c70574abc5e79c, 0x719ad1e361fcdd78,
    0xc3a4037090b98cd1, 0x23f9d7e75a80b635, 0x3501097cdd3d3106,
    0xd55cddeb17040be2, 0x67620f78e6415a4b, 0x873fdbef2c7860af,
    0x4a93b24fe93a542d, 0xaace66d823036ec9, 0x18f0b44bd2463f60,
    0xf8ad60dc187f0584, 0xee55be479fc282b7, 0x0e086ad055fbb853,
    0xbc36b843a4bee9fa, 0x5c6b6cd46e87d31e, 0xb5b6c42981349e7b,
    0x55eb10be4b0da49f, 0xe7d5c22dba48f536, 0x078816ba7071cfd2,
    0x1170c821f7cc48e1, 0xf12d1cb63df57205, 0x4313ce25ccb023ac,
    0xa34e1ab206891948, 0x6ee27312c3cb2dca, 0x8ebfa78509f2172e,
    0x3c817516f8b74687, 0xdcdca181328e7c63, 0xca247f1ab533fb50,
    0x2a79ab8d7f0ac1b4, 0x9847791e8e4f901d, 0x781aad894476aaf9,
    0xd92487cefe271452, 0x39795359341e2eb6, 0x8b4781cac55b7f1f,
    0x6b1a555d0f6245fb, 0x7de28bc688dfc2c8, 0x9dbf5f5142e6f82c,
    0x2f818dc2b3a3a985, 0xcfdc5955799a9361, 0x027030f5bcd8a7e3,
    0xe22de46276e19d07, 0x501336f187a4ccae, 0xb04ee2664d9df64a,
    0xa6b63cfdca207179, 0x46ebe86a00194b9d, 0xf4d53af9f15c1a34,
    0x1488ee6e3b6520d0, 0xfd554693d4d66db5, 0x1d0892041eef5751,
    0xaf364097efaa06f8, 0x4f6b940025933c1c, 0x59934a9ba22ebb2f,
    0xb9ce9e0c681781cb, 0x0bf04c9f9952d062, 0xebad9808536bea86,
    0x2601f1a89629de04, 0xc65c253f5c10e4e0, 0x7462f7acad55b549,
    0x943f233b676c8fad, 0x82c7fda0e0d1089e, 0x629a29372ae8327a,
    0xd0a4fba4dbad63d3, 0x30f92f3311945937, 0xb156a5c2f885d1bd,
    0x510b715532bceb59, 0xe335a3c6c3f9baf0, 0x0368775109c08014,
    0x1590a9ca8e7d0727, 0xf5cd7d5d44443dc3, 0x47f3afceb5016c6a,
    0xa7ae7b597f38568e, 0x6a0212f9ba7a620c, 0x8a5fc66e704358e8,
    0x386114fd81060941, 0xd83cc06a4b3f33a5, 0xcec41ef1cc82b496,
    0x2e99ca6606bb8e72, 0x9ca718f5f7fedfdb, 0x7cfacc623dc7e53f,
    0x9527649fd274a85a, 0x757ab008184d92be, 0xc744629be908c317,
    0x2719b60c2331f9f3, 0x31e16897a48c7ec0, 0xd1bcbc006eb54424,
    0x63826e939ff0158d, 0x83dfba0455c92f69, 0x4e73d3a4908b1beb,
    0xae2e07335ab2210f, 0x1c10d5a0abf770a6, 0xfc4d013761ce4a42,
    0xeab5dface673cd71, 0x0ae80b3b2c4af795, 0xb8d6d9a8dd0fa63c,
    0x588b0d3f17369cd8, 0xf9b52778ad672273, 0x19e8f3ef675e1897,
    0xabd6217c961b493e, 0x4b8bf5eb5c2273da, 0x5d732b70db9ff4e9,
    0xbd2effe711a6ce0d, 0x0f102d74e0e39fa4, 0xef4df9e32adaa540,
    0x22e19043ef9891c2, 0xc2bc44d425a1ab26, 0x70829647d4e4fa8f,
    0x90df42d01eddc06b, 0x86279c4b99604758, 0x667a48dc53597dbc,
    0xd4449a4fa21c2c15, 0x34194ed8682516f1, 0xddc4e62587965b94,
    0x3d9932b24daf6170, 0x8fa7e021bcea30d9, 0x6ffa34b676d30a3d,
    0x7902ea2df16e8d0e, 0x995f3eba3b57b7ea, 0x2b61ec29ca12e643,
    0xcb3c38be002bdca7, 0x0690511ec569e825, 0xe6cd85890f50d2c1,
    0x54f3571afe158368, 0xb4ae838d342cb98c, 0xa2565d16b3913ebf,
    0x420b898179a8045b, 0xf0355b1288ed55f2, 0x10688f8542d46f16,
    0x2091a0b653403621, 0xc0cc742199790cc5, 0x72f2a6b2683c5d6c,
    0x92af7225a2056788, 0x8457acbe25b8e0bb, 0x640a7829ef81da5f,
    0xd634aaba1ec48bf6, 0x36697e2dd4fdb112, 0xfbc5178d11bf8590,
    0x1b98c31adb86bf74, 0xa9a611892ac3eedd, 0x49fbc51ee0fad439,
    0x5f031b856747530a, 0xbf5ecf12ad7e69ee, 0x0d601d815c3b3847,
    0xed3dc916960202a3, 0x04e061eb79b14fc6, 0xe4bdb57cb3887522,
    0x568367ef42cd248b, 0xb6deb37888f41e6f, 0xa0266de30f49995c,
    0x407bb974c570a3b8, 0xf2456be73435f211, 0x1218bf70fe0cc8f5,
    0xdfb4d6d03b4efc77, 0x3fe90247f177c693, 0x8dd7d0d40032973a,
    0x6d8a0443ca0badde, 0x7b72dad84db62aed, 0x9b2f0e4f878f1009,
    0x2911dcdc76ca41a0, 0xc94c084bbcf37b44, 0x6872220c06a2c5ef,
    0x882ff69bcc9bff0b, 0x3a1124083ddeaea2, 0xda4cf09ff7e79446,
    0xccb42e04705a1375, 0x2ce9fa93ba632991, 0x9ed728004b267838,
    0x7e8afc97811f42dc, 0xb3269537445d765e, 0x537b41a08e644cba,
    0xe14593337f211d13, 0x011847a4b51827f7, 0x17e0993f32a5a0c4,
    0xf7bd4da8f89c9a20, 0x45839f3b09d9cb89, 0xa5de4bacc3e0f16d,
    0x4c03e3512c53bc08, 0xac5e37c6e66a86ec, 0x1e60e555172fd745,
    0xfe3d31c2dd16eda1, 0xe8c5ef595aab6a92, 0x08983bce90925076,
    0xbaa6e95d61d701df, 0x5afb3dcaabee3b3b, 0x9757546a6eac0fb9,
    0x770a80fda495355d, 0xc534526e55d064f4, 0x256986f99fe95e10,
    0x339158621854d923, 0xd3cc8cf5d26de3c7, 0x61f25e662328b26e,
    0x81af8af1e911888a,
  };

  static inline uint64_t update1(uint64_t state, uint8_t x)
  {
    return (state >> 8) ^ TABLE_0[x ^ (static_cast<uint8_t>(state))];
  }

  static inline uint64_t
  update16(uint64_t state, const std::array<uint8_t, 16>& slice)
  {
    union
    {
      uint64_t num{};
      uint8_t s[8];
    };

#if defined(__BIG_ENDIAN__)
    num = __builtin_bswap64(state);
#else
    num = state;
#endif

    return TABLE_0[slice[15]] ^ TABLE_1[slice[14]] ^ TABLE_2[slice[13]] ^
      TABLE_3[slice[12]] ^ TABLE_4[slice[11]] ^ TABLE_5[slice[10]] ^
      TABLE_6[slice[9]] ^ TABLE_7[slice[8]] ^ TABLE_8[slice[7] ^ s[7]] ^
      TABLE_9[slice[6] ^ s[6]] ^ TABLE_10[slice[5] ^ s[5]] ^
      TABLE_11[slice[4] ^ s[4]] ^ TABLE_12[slice[3] ^ s[3]] ^
      TABLE_13[slice[2] ^ s[2]] ^ TABLE_14[slice[1] ^ s[1]] ^
      TABLE_15[slice[0] ^ s[0]];
  }

  static inline uint64_t
  update_table(uint64_t state, const void* src, size_t length)
  {
    auto address = reinterpret_cast<uintptr_t>(src);
    auto ptr = reinterpret_cast<const uint8_t*>(src);
    auto prefix = (-address) & 15;
    if (prefix >= length)
    {
      for (size_t i = 0; i < prefix; ++i, ++ptr)
      {
        state = update1(state, *ptr);
      };
      return state;
    }
    auto suffix = (length - prefix) & 15;
    auto middle = length - prefix - suffix;
    for (size_t i = 0; i < prefix; ++i, ++ptr)
    {
      state = update1(state, *ptr);
    };
    for (size_t i = 0; i < middle; i += 16, ptr += 16)
    {
      auto aligned =
        reinterpret_cast<const uint8_t*>(__builtin_assume_aligned(ptr, 16));
      std::array<uint8_t, 16> slice{
        aligned[0],
        aligned[1],
        aligned[2],
        aligned[3],
        aligned[4],
        aligned[5],
        aligned[6],
        aligned[7],
        aligned[8],
        aligned[9],
        aligned[10],
        aligned[11],
        aligned[12],
        aligned[13],
        aligned[14],
        aligned[15],
      };
      state = update16(state, slice);
    };
    for (size_t i = 0; i < suffix; ++i, ++ptr)
    {
      state = update1(state, *ptr);
    };
    return state;
  }

} // namespace crc64::detail

#endif // CRC64_TABLE_HPP
