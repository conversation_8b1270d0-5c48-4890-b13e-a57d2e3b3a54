# 项目生产
## 配置Python环境
从samba的
\\CreatorSamba\XverseCreator\开发常用工具\UEPackageTools\Python310
拷贝Python310到项目的Tools目录内，结果如下(C:\work\xverse\XVerseStudio为项目路径):
C:\work\xverse\XVerseStudio\Tools\Python310

## 拷贝脚本generateProject.bat到Script的项目目录下
从项目的Tools目录复制generateProject.bat到Script内，最后拷贝结果如下：
C:\work\xverse\XVerseStudio\Script\Projects\XStudio\generateProject.bat

## 生成项目uproject
在项目的Script下双击generateProject.bat