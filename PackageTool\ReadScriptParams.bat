@chcp 65001>nul
@cls
@echo off
set AllParam=%*
set CurrentParam=%AllParam%
echo all: %AllParam%
set "CurrentIndex=1"

:ReadParams
set QCmd="tokens=%CurrentIndex% delims= "
set "CurrentParam="
::echo QCmd.%QCmd%
for /f %QCmd% %%b  in ("%AllParam%") do ( 
	set ALL_BUT_FIRST=%%b
	set CurrentParam=%%b
	echo param.%%b
)
set /a "CurrentIndex=%CurrentIndex% + 1"
::echo CurrentParam.%CurrentParam%
if "%CurrentParam%"=="" (
 goto :eof
)
goto :ReadInputParams


:ReadParam
set str=%1
echo str=%str%
if "%str%"=="" (
	echo no params
    goto :end
)
set allparam=%allparam% %str%
shift /0
goto :ReadParam

:end
echo end
if "%allparam%"=="" (
    goto :eof
)

:exit
echo exit
pause
goto :eof