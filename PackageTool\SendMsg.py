#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import requests

def sendWeixinTipsMessage(Title, BuildTime, OutPutPath, GameConfiguration, Version, LogPath, BranchName, BuildLogUrl = ""):
    print("Echo Start", Title, OutPutPath)
    MsgContent = {}
    Content = "标题:" + Title + "\n"
    Content += "时间:" + BuildTime + "\n"
    Content += "地址:" + OutPutPath + "\n"
    Content += "类型:" + GameConfiguration + "\n"
    Content += "版本:" + Version + "\n"
    Content += "分支:" + BranchName + "\n"
    Content += "日志:" + BuildLogUrl + "\n"
    MsgContent["content"] = Content
    
    MsgMap = {}
    MsgMap["msgtype"] = "text"
    MsgMap["text"] = MsgContent
    
    OutMsg = json.dumps(MsgMap, sort_keys=False, ensure_ascii=False, indent=4, separators=(',', ': '))
    print(OutMsg)
    headers = {
        'charset': 'utf-8',
        'content-type': 'application/json',
    }
    #data = '{\t"appId": appId,\t"appSecret": "appSecret"}'
    NotifyUrl = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e26e016b-bc57-4c04-9c09-cce9939d091a'
    response = requests.post(NotifyUrl, headers=headers, data=OutMsg.encode("utf-8"))
    print(response)
    
if __name__ == "__main__":
    print("Start SendMsg",sys.argv)
    BuildLogUrl = ""
    if len(sys.argv) > 8:
        BuildLogUrl = sys.argv[8]
    sendWeixinTipsMessage(sys.argv[1], sys.argv[2], sys.argv[3], sys.argv[4], sys.argv[5], sys.argv[6], sys.argv[7], BuildLogUrl)
