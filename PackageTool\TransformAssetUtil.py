import os
import shutil

def copy_files(source_dir, destination_dir):
    # 检查目录是否存在，如果不存在则创建目标目录
    if not os.path.exists(destination_dir):
        os.makedirs(destination_dir)

    # 获取源目录下的所有文件和文件夹
    files = os.listdir(source_dir)

    for file_name in files:
        source_file = os.path.join(source_dir, file_name)
        destination_file = os.path.join(destination_dir, file_name)

        # 拷贝文件到目标目录
        shutil.copy2(source_file, destination_file)
        print(f"Copy '{file_name}' to '{destination_dir}'")

# 设置源目录和目标目录的路径
# source_directory = 'DirA'  # 用实际的目录路径替换这里
# destination_directory = 'DirB'  # 用实际的目录路径替换这里

# 调用函数拷贝文件
# copy_files(source_directory, destination_directory)
