:: UnrealProjectMenuRegistration.cmd
::
:: This batch file will try to find where the Unreal Engine is installed, then update your registry
:: to register the Unreal Project File type (and its right click menu for things such as generating
:: Visual Studio project files, and launching)
::
:: Run as an Administrator, as it's required for writing to HKLM.
::
:: Author: <PERSON> <<EMAIL>>

@ECHO OFF

echo.
echo Locating where Unreal Engine is installed by checking the Windows registry...

pushd %~dp0
set UnrealEngineDir=%cd%\
echo.
echo Using Unreal Engine directory: %UnrealEngineDir%

set LauncherPath=%UnrealEngineDir%Engine\Binaries\Win64\UnrealVersionSelector.exe

echo LauncherPath=%LauncherPath%
if exist %LauncherPath% (
	echo  start RegisterMenu...
	goto RegisterMenu
) else (
    echo Launcher exe not exist..
	goto TheEnd
)

:RegisterMenu
echo.
echo Adding registry keys for the Unreal Project right click menu...

::goto TheEnd

:: HKLM\SOFTWARE\Classes\.uproject
reg ADD "HKLM\Software\Classes\.uproject" /ve /d Unreal.ProjectFile /f
IF ERRORLEVEL 1 (
    echo Couldn't write to registry. Did you forget to run this batch file as an administrator?
    goto TheEnd
)

:: HKLM\SOFTWARE\Classes\Unreal.ProjectFile
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile" /ve /d "Unreal Engine Project File" /f

:: HKLM\SOFTWARE\Classes\Unreal.ProjectFile\DefaultIcon
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\DefaultIcon" /ve /d "\"%LauncherPath%\"" /f

:: HKLM\SOFTWARE\Classes\Unreal.ProjectFile\shell\open
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\open" /ve /d "Open" /f
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\open\command" /ve /d "\"%LauncherPath%\" /editor \"%%1\"" /f

:: HKLM\SOFTWARE\Classes\Unreal.ProjectFile\shell\run
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\run" /ve /d "Launch game" /f
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\run" /v Icon /t REG_SZ /d "\"%LauncherPath%\"" /f
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\run\command" /ve /d "\"%LauncherPath%\" /game \"%%1\"" /f

:: HKLM\SOFTWARE\Classes\Unreal.ProjectFile\shell\rungenproj
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\rungenproj" /ve /d "Generate Visual Studio project files" /f
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\rungenproj" /v Icon /t REG_SZ /d "\"%LauncherPath%\"" /f
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\rungenproj\command" /ve /d "\"%LauncherPath%\" /projectfiles \"%%1\"" /f

:: HKLM\SOFTWARE\Classes\Unreal.ProjectFile\shell\switchversion
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\switchversion" /ve /d "Switch Unreal Engine version..." /f
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\switchversion" /v Icon /t REG_SZ /d "\"%LauncherPath%\"" /f
reg ADD "HKLM\Software\Classes\Unreal.ProjectFile\shell\switchversion\command" /ve /d "\"%LauncherPath%\" /switchversion \"%%1\"" /f

:Done
echo.
echo Done.
echo.
goto TheEnd

:CannotFindUnrealEngine
echo.
echo DOH! Can't find the Unreal Engine.
echo.
goto TheEnd

:TheEnd
pause