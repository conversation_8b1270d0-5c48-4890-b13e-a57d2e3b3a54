import os
import sys
import argparse
import httpx
import asyncio
import sqlite3
import shutil
import time
import crcmod
import orjson
import re
import psutil
from fastpycrc64 import get_file_crc64
from httpx import HTTPError
from joblib import Parallel, delayed
from dataclasses import dataclass
from tqdm.asyncio import tqdm
from pathlib import Path
from loguru import logger
from xverseUtil import IniConfigHelper


cache_path = Path(os.getcwd()) / 'tmp'
os.makedirs(cache_path, mode=0o777, exist_ok=True)

logger.remove()

logger.add(f"{cache_path}/logs/assetMgr_{time.strftime('%Y-%m')}.log", format="{time:YYYY-MM-DD HH:mm:ss,SSS} {level} {message}", rotation="50MB", encoding="utf-8", enqueue=True, backtrace=True, diagnose=True, level='INFO')
logger.add(sys.stdout, level="SUCCESS")
crc64_func = crcmod.mkCrcFun(0x142F0E1EBA9EA3693, initCrc=0, xorOut=0xffffffffffffffff, rev=True)

@dataclass
class Asset:
    key: str = ""
    remote_url: str = ""
    local_path: str = ""
    version: str = ""
    crc64: int = 0
    chunk_size: int = 16 * 1024
    http: bool = True

def getCrc64(file_path):
    crc64 = 0
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(16384), b''):
            crc64 = crc64_func(chunk, crc64)
    return crc64

def getFileSize(file_path):
    file_size_bytes = os.path.getsize(file_path)  # 文件大小（字节）
    file_size_mb = file_size_bytes / (1024 * 1024)  # 转换为 MB
    return round(file_size_mb, 2)  # 保留两位小数

def isVerified(asset: Asset) -> bool:
    crc64, msg = 0, ''
    ibrSize, pbrSize = 0, 0
    if not asset.local_path.exists():
        crc64 = 1
        msg = f"Download {asset.remote_url} failed."
        
    if asset.key:
        crc64 = getCrc64(asset.local_path)
        if re.search('IBRAssets', asset.key):
            ibrSize = getFileSize(asset.local_path)
        elif asset.key != 'panoramic':
            pbrSize = getFileSize(asset.local_path)
        if crc64 != asset.crc64:
            msg = f"version {asset.version}, {asset.key} crc64 doesn't match: {crc64}, {asset.crc64}"
    return crc64 == asset.crc64, ibrSize, pbrSize, msg

class AssetMgr:
    def __init__(self, app_id: str, release_id: str, xstudio_path: str, mock_app_id: str = '', pano_video_path: str = '', b_pre_release: bool = False, b_custom_pak: bool = False, b_need_lowmodel = False, b_only_lowmodel : bool = False, b_collect_pso: bool = False, sdk_env: str = 'sit', b_generate_pso_world: bool = False, b_only_video: bool = False, concurrent_num: int = 20, retry_num: int = 10) -> None:
        self.app_id = app_id
        self.mock_app_id = mock_app_id
        self.release_id = release_id
        self.pipeline_cache_path = Path(xstudio_path) / "Build/Android/PipelineCaches"
        self.build_cfg_path = Path(xstudio_path) / "config"
        self.asset_path = Path(xstudio_path) / "Content"
        self.pano_video_path = Path(pano_video_path) if pano_video_path else None
        self.concurrent_num = int(psutil.cpu_count() / 2)
        # self.concurrent_num = concurrent_num
        self.retry_num = retry_num
        self.b_pre_release = b_pre_release
        self.b_custom_pak = b_custom_pak
        self.b_need_lowmodel = b_need_lowmodel
        self.b_only_lowmodel = b_only_lowmodel
        self.b_only_video = b_only_video
        self.b_collect_pso = b_collect_pso
        self.b_generate_pso_world = b_generate_pso_world
        self.b_lbvr = True
        self.sdk_env = sdk_env.lower()
        self.loop = asyncio.get_event_loop()

        self.cache_path = Path(os.getcwd()) / 'tmp'
        os.makedirs(self.cache_path, mode=0o777, exist_ok=True)
        os.makedirs(self.asset_path / "Files", mode=0o777, exist_ok=True)  # Ensure the asset path exists
        
        self.lbvr_movie_cfg_path = self.cache_path / "LBVRProjectMovie.toml"
        self.async_client = httpx.AsyncClient(timeout=httpx.Timeout(timeout=30.0))
        self.sync_client = httpx.Client(timeout=httpx.Timeout(timeout=30.0))
        self.semaphore = asyncio.Semaphore(self.concurrent_num)

        self.asset_db_path = None
        if self.pano_video_path:
            if os.path.exists(self.pano_video_path):
                shutil.rmtree(self.pano_video_path)
            os.makedirs(self.pano_video_path, mode=0o777)
        
        if self.pipeline_cache_path.exists():
            shutil.rmtree(self.pipeline_cache_path)
        # os.makedirs(self.pipeline_cache_path, mode=0o777)
        self.pipeline_cache_path.mkdir(parents=True, exist_ok=True)
       
        self.all_assets = []
        self.all_asset_keys = []
        self.asset_global_version = None
        self.native_load_info = {"RoomsInfo":[]}
        self.native_repo_name = None
        self.native_app_name = None
        self._build_all_assets()
    
    def _wait_release_id_ready(self):
        status = False
        try:
            while not status:
                releaseInfoResp = self.sync_client.get(f"https://console-api.xverse.cn/release/console/release_mgt/v1/apps/{self.app_id}/releases/{self.release_id}/status", headers={"Authorization":"Bearer 4e3fa26e64d24869ac691578b149a7b7"})
                releaseInfoResp.raise_for_status()
                releaseInfo = releaseInfoResp.json()
                if releaseInfo['code'] != 0:
                    raise httpx.RequestError(releaseInfo['msg'])
                status = (releaseInfo['data']['status'] == 'SUCCEEDED' and releaseInfo['data']['isOver'])
                if not status:
                    time.sleep(30)
        except Exception as e:
            raise ValueError("wait release id ready failed: {}".format(e))

    def _parse_release_id(self) -> list[str]:
        retryCnt = 0
        while True:
            try:
                
                appInfoResp = self.sync_client.get(f"https://console-api.xverse.cn/release/console/app_mgt/v1/apps/{self.app_id}", headers={"Authorization":"Bearer 4e3fa26e64d24869ac691578b149a7b7"})
                # appInfoResp = self.sync_client.post("https://console-api.xverse.cn/release/console/app_mgt/v1/app_info", json={'appIdList':[self.app_id]})
                appInfoResp.raise_for_status()
                appInfo = appInfoResp.json()
                if appInfo['code'] != 0:
                    raise httpx.RequestError(appInfo['msg'])

                if appInfo['data']['appInfo']:
                    for tag in appInfo['data']['appInfo']['appTagList']:
                        if tag['key'] == 'OfflineSpace':
                            self.b_lbvr = True
                            break
                else:
                    if self.app_id == '11142':
                        self.b_lbvr = False
                self.native_repo_name = appInfo['data']['appInfo']['repoData']['writableRepoList'][0]['alias']
                self.native_app_name = appInfo['data']['appInfo']['name']

                if os.path.exists("//DDCache/DDC/LBVRProjectMovie.toml"):
                    self.all_assets.append(Asset(remote_url='//DDCache/DDC/LBVRProjectMovie.toml', local_path=self.lbvr_movie_cfg_path, http=False))
                
                isBind, asset_list = False, []
                if self.release_id:
                    # 检查release_id状态
                    self._wait_release_id_ready()
                    self.asset_db_path = self.cache_path / f"{self.app_id}_{self.release_id}.db3"
                    # with self.sync_client:
                    ridConfigResp = self.sync_client.get(f"https://static.xverse.cn/console/config/{self.app_id}/{self.release_id}/SOURCE/config.json")
                    
                    ridConfigResp.raise_for_status()

                    ridConfigInfo = ridConfigResp.json()

                    # 全景视频资产
                    if self.pano_video_path:
                        for roomInfo in ridConfigInfo['config']['roomList']:
                            for skinInfo in roomInfo['skinList']:
                                if 'panoramicVideo' in list(skinInfo.keys()) and skinInfo['panoramicVideo']:
                                    panoramic_video_asset = Asset()
                                    panoramic_video_asset.remote_url = re.sub("//10.0.26.27", 'http://10.0.26.27:8080', skinInfo['panoramicVideo']['url'].replace("\\", "/"))
                                    panoramic_video_asset.local_path = self.pano_video_path / os.path.basename(skinInfo['panoramicVideo']['url'])
                                    panoramic_video_asset.http = True
                                    panoramic_video_asset.chunk_size = 1024 * 1024
                                    panoramic_video_asset.crc64 = get_file_crc64(panoramic_video_asset.remote_url.replace("http://10.0.26.27:8080", "//DDCache"))
                                    panoramic_video_asset.key = 'panoramic'
                                    self.all_assets.append(panoramic_video_asset)
                    
                    # 覆盖overrideConfig.json
                    if not self.b_pre_release:
                        ridOverrideConfigResp = self.sync_client.get(f"https://static.xverse.cn/console/config/{self.app_id}/{self.release_id}/ANDROID/config.json")
                        ridOverrideConfigResp.raise_for_status()

                        ridOverrideConfig = ridOverrideConfigResp.json()
                        if self.mock_app_id:
                            ridOverrideConfig['appId'] = self.mock_app_id

                        with open(self.asset_path / "Files/OverrideConfig.json", 'wb') as f:
                            f.write(orjson.dumps(ridOverrideConfig, option=orjson.OPT_INDENT_2))
                    else:
                        preAssets = [
                            "/Game/XDevCommon/LBE/BestZone/GA_BestZoneBase", "/Game/XDevCommon/LBE/GA/Stage/GA_StageOnBeginPlay", 
                            "/Game/XDevCommon/LBE/GA/Stage/GA_StageOnLocateInited", "/Game/XDevCommon/LBE/Locate/GA/GA_UpdateStatusToLocateService", 
                            "/Game/XDevCommon/GA/AudioController/GA_SetBaseSoundMix", "/Game/XDevCommon/LBE/Calibrate/Anchor/UMG/BP_AnchorStatusError",
                            "/Game/XDevCommon/LBE/PhyScene/UMG/BP_LoadPhySceneErrActor", "/Game/XDevCommon/Gameplay/BP_VRCameraPlayerController",
                            "/Game/XDevCommon/LBE/Debug/DebugMenu/WBP_LBERuntimeMenu", "/Game/XDevCommon/LBE/Debug/DebugMenu/IMC_LBEDebugMenu",
                            "/Game/XDevCommon/LBE/Interaction/HandsV3/BP_XVHandRightV3"
                        ]
                        self._collect_dependency(preAssets)
                        for roomInfo in ridConfigInfo['config']['roomList']:
                            room_native_info = {'name': roomInfo['name']}
                            skinInfo = roomInfo['skinList'][0]
                            if roomInfo['mode'] == 'HYBRID':
                                if skinInfo['pathList'][0]['assetList']:
                                    room_native_info["RoamingPathIndex"] = skinInfo['pathList'][0]['assetList'][0]['assetPathId']
                                player_position = skinInfo['pathList'][0]['birthPointList'][0]['player']['position']
                                player_rotation = skinInfo['pathList'][0]['birthPointList'][0]['player']['rotation']

                                room_native_info['PlayerStartLocation'] = {"X": 0, "Y": 0, "Z": 0}
                                room_native_info['PlayerStartRotator'] = {"Pitch": 0, "Yaw": 0, "Roll": 0}
                                for p in player_position.items():
                                    if p[0] == 'x':
                                        room_native_info['PlayerStartLocation']["X"] = p[1]
                                    elif p[0] == 'y':
                                        room_native_info['PlayerStartLocation']["Y"] = p[1]
                                    elif p[0] == 'z':
                                        room_native_info['PlayerStartLocation']["Z"] = p[1]
                                for r in player_rotation.items():
                                    if r[0] == 'pitch':
                                        room_native_info['PlayerStartRotator']["Pitch"] = r[1]
                                    elif r[0] == 'yaw':
                                        room_native_info['PlayerStartRotator']["Yaw"] = r[1]
                                    elif r[0] == 'roll':
                                        room_native_info['PlayerStartRotator']["Roll"] = r[1]
                                for ibr_asset in skinInfo['assetList']:
                                    if ibr_asset['assetType'] == 'IbrLevel':
                                        room_native_info['FrontScenePath'] = ibr_asset['assetPathId']
                                    elif ibr_asset['assetType'] == 'IbrLowModelIndex':
                                        room_native_info['LowModelIndex'] = ibr_asset['assetPathId']
                                    elif ibr_asset['assetType'] == 'IbrBaseColorIndex':
                                        room_native_info['BaseColorIndex'] = ibr_asset['assetPathId']
                            
                            if skinInfo['levelMapList']:
                                self._collect_dependency(skinInfo['levelMapList'])
                                room_native_info['LBVRArtScenePath'] = skinInfo['levelMapList']
                            self.native_load_info['RoomsInfo'].append(room_native_info)
                    
                    if not self.b_only_video:
                        if self.b_custom_pak:
                            self._gen_customPak_ini(ridConfigInfo)
                                    
                        # pso资产
                        if 'psoInfo' in ridConfigInfo.keys() and not self.b_collect_pso:
                            if ridConfigInfo['psoInfo']:
                                pso_asset = Asset()
                                pso_asset.remote_url = ridConfigInfo['psoInfo']['pso_url']
                                pso_asset.local_path = self.pipeline_cache_path / "XVerseVR_Oculus_SF_VULKAN_ES31_ANDROID.spc"
                                self.all_assets.append(pso_asset)
                                isBind = True
                            else:
                                if self.sdk_env == 'prd':
                                    raise Exception("prd包未绑定pso！")
                    # 下载asset_db
                    if ridConfigInfo['resourceSnapshotDB']:
                        with self.sync_client.stream("get", ridConfigInfo['resourceSnapshotDB']['url']) as resp:
                            with open(self.asset_db_path, 'wb') as f:
                                for chunk in resp.iter_bytes():
                                    f.write(chunk)
                    self.asset_global_version = ridConfigInfo['resourceVersion']
                    
                    # get asset list
                    if not self.b_lbvr and not self.b_pre_release:
                        assetInfoResp = self.sync_client.get(f"https://static.xverse.cn/console/config/{self.app_id}/{self.release_id}/asset_info.json")
                    
                        assetInfoResp.raise_for_status()
                        assetInfo = assetInfoResp.json()
                        if not assetInfo['assetList']:
                            raise ValueError(f"{self.release_id} is pre-release!")
                        asset_list = list(set([asset['pathId'] for asset in assetInfo['assetList']]))
                
                # 启用pso打包/收集pso/更新sdk设置
                if not self.b_only_video:
                    self._update_engine_config(isBind)
            
                return asset_list

            except Exception as e:
                if retryCnt < 3:
                    retryCnt += 1
                else:
                    logger.error(f"parse release id error occurred: {e}")
                    raise
        
    def _gen_customPak_ini(self, ridConfigInfo):
        customPakCfg = IniConfigHelper()
        customPakCfgPath = os.path.join(self.build_cfg_path, 'DefaultXBizToolsConfig.ini')
        
        customPakSection = "/Script/XBizTools.CustomPak"
        
        customPakCfg.read(customPakCfgPath)
        key2Append = "MapsToCook"
        existingMaps = []
        for line in customPakCfg.content:
            if re.search(key2Append, line):
                existingMaps.append(line.strip().replace("\"",""))

        app_entry_path = ridConfigInfo['appEntryPath']
        
        if app_entry_path:
            if len(existingMaps) == 0:
                customPakCfg.appendList(customPakSection, key2Append, app_entry_path)
            else:
                maps = f"+{key2Append}={app_entry_path}"
                if maps not in existingMaps and f"+{key2Append}=\"{app_entry_path}\"" not in existingMaps:
                    customPakCfg.content.append(maps)

        for roomInfo in ridConfigInfo['config']['roomList']:
            for skinInfo in roomInfo['skinList']:
                if skinInfo['assetList']:
                    for skin_asset in skinInfo['assetList']:
                        if skin_asset['assetType'] == 'IbrLevel':
                            maps = f"+{key2Append}={skin_asset['assetPathId']}"
                            if maps not in existingMaps:
                                customPakCfg.content.append(maps)
        
        customPakCfg.write(customPakCfgPath)
        
    
    def _update_engine_config(self, isBind):
        androidEngineIniPath, engineIniPath = self.build_cfg_path / "Android/AndroidEngine.ini", self.build_cfg_path / "DefaultEngine.ini"
        engineConfig = IniConfigHelper()
        def _rewriteIni(ini_path):
            engineConfig.content.clear()
            engineConfig.read(ini_path)
            engineConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "SDKPath", "(Path=\"C:/Users/<USER>/AppData/Local/Android/Sdk\")")
            engineConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "NDKPath", "(Path=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393\")")
            engineConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "JavePath", "(Path=\"C:/Program Files/Java/jdk-11\")")
            engineConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "SDKAPILevel", "android-29")
            engineConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "NDKAPILevel", "android-25")
            engineConfig.set("/Script/UdpMessaging.UdpMessagingSettings", "EnableTransport", 'false')
            engineConfig.set("/Script/TcpMessaging.TcpMessagingSettings", "EnableTransport", 'false')
            if self.b_custom_pak:
                engineConfig.set('ConsoleVariables', 'r.ShaderPipelineCache.Enabled', 0)
            else:
                engineConfig.set('ConsoleVariables', 'r.ShaderPipelineCache.Enabled', 1)
            engineConfig.set('DevOptions.Shaders', 'NeedsShaderStableKeys', 'false')
            if self.b_collect_pso or isBind:
                engineConfig.set('DevOptions.Shaders', 'NeedsShaderStableKeys', 'true')
            engineConfig.set('ConsoleVariables', 'r.ShaderPipelineCache.LogPSO', 0)
            engineConfig.set('ConsoleVariables', 'r.ShaderPipelineCache.SaveBoundPSOLog', 0) 
            os.environ["JAVA_HOME"] = "C:\\Program Files\\Java\\jdk-11"
            if not self.b_lbvr:
                engineConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "JavePath", "(Path=\"C:/Program Files/Java/jdk-1.8\")")
                os.environ["JAVA_HOME"] = "C:\\Program Files\\Java\\jdk-1.8"
                engineConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "SDKAPILevel", "matchndk")
                engineConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "NDKAPILevel", "android-32")
            
            if self.b_collect_pso:
                
                engineConfig.set('ConsoleVariables', 'r.ShaderPipelineCache.LogPSO', 1)
                engineConfig.set('ConsoleVariables', 'r.ShaderPipelineCache.SaveBoundPSOLog', 1)
            
            if self.b_generate_pso_world:
                engineConfig.set('ConsoleVariables', 'r.ShaderPipelineCache.Enabled', 1)
                engineConfig.set('/Script/EngineSettings.GameMapsSettings', 'GameDefaultMap', '/Game/PSOCollect/PSOWorld.PSOWorld')
            
            engineConfig.write(ini_path)
        
        # shutil.rmtree(self.pipeline_cache_path)
        # self.pipeline_cache_path.mkdir(parents=True, exist_ok=True)
        _rewriteIni(androidEngineIniPath)
        _rewriteIni(engineIniPath)
    
    
    def _collect_dependency(self, asset_path_list):
        if len(asset_path_list) == 0:
            return
        # with self.sync_client:
        for attempt in range(self.retry_num):
            for path_id in asset_path_list:
                if path_id in self.all_asset_keys:
                    continue
                try:
                    # 批量查询接口有bug
                    assetInfoListResp = self.sync_client.post(
                        "https://native-asset-server-api.xverse.cn/release/prod/asset_mgt/v1/assets/platform/query_all", json={
                            "pathIdList": [path_id],
                            "fileType": "SOURCE",
                            "ueVersion": "5_2_1"
                        })
                    assetInfoListResp.raise_for_status()
                    assetInfoList = assetInfoListResp.json()
                    if assetInfoList['code'] != 0:
                        raise httpx.RequestError(assetInfoList['msg'])
                    
                    if len(assetInfoList['data']['assetList']) == 0:
                        raise Exception(f"未在资产库中查找到asset {path_id}，请检查")
                    
                    for asset in assetInfoList['data']['assetList']:
                        if asset["fileInfo"]['pathId'] in self.all_asset_keys:
                            continue
                        assetSelf = Asset()
                        assetSelf.key = asset["fileInfo"]['pathId']
                        assetSelf.remote_url = asset["fileInfo"]['url']
                        assetSelf.local_path = self.asset_path / asset["fileInfo"]['localPath'][1:]
                        if not assetSelf.local_path.parent.exists():
                            assetSelf.local_path.parent.mkdir(parents=True)
                        assetSelf.crc64 = int(asset["fileInfo"]['crc64'])
                        
                        # if assetSelf not in self.all_assets:
                        self.all_assets.append(assetSelf)
                        self.all_asset_keys.append(assetSelf.key)
                        # else:
                        #     continue
              
                        self._collect_dependency(asset["fileInfo"]['dependencyList']+asset["fileInfo"]['softDependencyList'])
                        
                except Exception as e:
                    if attempt == self.retry_num - 1:
                        logger.error("get {} dependency error: {}", path_id, e)
                        raise
                    else:
                        logger.warning("get {} dependency error:{} retry", path_id, e)               
                
    def _get_physical_scene_asset(self):
        xsdkCfg = IniConfigHelper()
        phySceneSection = "/Script/XBizUtil.XVersePhySceneConfig"
        xsdkCfgPath = os.path.join(self.build_cfg_path, 'DefaultXSDKConfig.ini')
        xsdkCfg.read(xsdkCfgPath)
        phySceneModelPath = xsdkCfg.get(phySceneSection, 'LowModelPath')
        xsdkCfg.set("/Script/XBizUtil.XVerseSDKConfig", "PackedAssetVersion", self.asset_global_version)
        xsdkCfg.write(xsdkCfgPath)
        # if not phySceneModelPath:
        xsdkCfg.content.clear()
        xphyCfgPath = os.path.join(self.build_cfg_path, "DefaultXPhySceneConfig.ini")
        if os.path.exists(xphyCfgPath):
            xsdkCfg.read(xphyCfgPath)
            phySceneModelPath = xsdkCfg.get(phySceneSection, 'LowModelPath')
                
        if phySceneModelPath:
            logger.info("start collect {} dependency", phySceneModelPath)
            self._collect_dependency([phySceneModelPath])
        else:
            logger.info("no phy scene low model")
            if self.b_only_lowmodel:  
                raise Exception("未配置物理场景低模")
        
    def _build_all_assets(self):
        self.all_asset_keys = self._parse_release_id()
        # sys.exit(0)
        all_asset_info_row = []
        try:
            if not self.b_only_lowmodel and not self.b_only_video and os.path.exists(self.asset_db_path):
                with sqlite3.connect(self.asset_db_path) as conn:
                    cursor = conn.cursor()
                    if not self.b_pre_release and not self.b_lbvr:
                        placehoders = ', '.join('?'*len(self.all_asset_keys))
                        query = f'SELECT PathId, Version, FileSize, MetaInfo FROM AssetCacheTbl WHERE PathId IN ({placehoders})'    
                        cursor.execute(query, self.all_asset_keys)
                        
                    else:
                        query = f'SELECT PathId, Version, FileSize, MetaInfo FROM AssetCacheTbl'
                        cursor.execute(query)
                        
                    all_asset_info_row = cursor.fetchall()
                    
                for r in all_asset_info_row:
                    path_id, version, file_size, meta_info = r[0], r[1], r[2], orjson.loads(r[3])
                    app_asset = Asset()
                    app_asset.key = path_id
                    app_asset.remote_url = meta_info['url']
                    app_asset.local_path = self.asset_path / meta_info['localPath'][1:]
                    if not app_asset.local_path.parent.exists():
                        app_asset.local_path.parent.mkdir(parents=True)
                    app_asset.version = version
                    app_asset.crc64 = int(meta_info['crc64'])
                    if file_size > 1024 * 1024 * 100:
                        app_asset.chunk_size = 32 * 1024
                    if file_size > 1024 * 1024 * 200:
                        app_asset.chunk_size = 64 * 1024
                    self.all_assets.append(app_asset)
                    if path_id not in self.all_asset_keys:
                        self.all_asset_keys.append(path_id)
            if not self.b_only_video and self.b_need_lowmodel:
                self._get_physical_scene_asset()

        except Exception as e:
            logger.error("{} query asset info from db error occurred: {}", self.release_id, e)
            raise Exception(f"从数据库查询资产时发生错误:{str(e)}")
    
    async def download_task(self, asset, progress):
        async with self.semaphore:
            if asset.http:
                for attempt in range(self.retry_num):
                    try:
                        downloadedSize = os.path.getsize(asset.local_path) if os.path.exists(asset.local_path) and attempt >= 1 else 0
                        headers = {"Range": f"bytes={downloadedSize}-"} if downloadedSize > 0 else {}
                        wmode = 'ab' if attempt >= 1 else 'wb'
                        # if os.path.exists(asset.local_path) and attempt >= 1:
                        #     downloadedSize = os.path.getsize(asset.local_path)
                        async with self.async_client.stream("GET", asset.remote_url, headers=headers) as resp:
                            if resp.status_code not in (200, 206):
                                raise HTTPError(f"Failed to download file: {resp.status_code}")
                            with open(asset.local_path, mode=wmode) as f:
                                async for chunk in resp.aiter_bytes(chunk_size=asset.chunk_size):
                                    f.write(chunk)

                            logger.info("{} download from {} to {} success", asset.key, asset.remote_url, asset.local_path)
                            progress.update(1)
                            return
                    except Exception as e:
                        if attempt == self.retry_num - 1:
                            logger.error("Failed to download {}: {}", asset.remote_url, e)
                            raise Exception(f"Failed to download {asset.remote_url}: {e}")
                        else:
                            logger.warning("Error downloading {}: {}", asset.remote_url, e)  
            else:
                try:
                    # if os.path.exists(asset.local_path):
                    #     raise FileExistsError
                    shutil.copy2(asset.remote_url, asset.local_path)
                    progress.update(1)
                except FileExistsError as fee:
                    logger.error("Copy cube panoramicVideo from {} to {} failed: {}", asset.remote_url, asset.local_path, fee)
                    raise Exception(f"Failed to download {asset.remote_url}: {fee}")
                except Exception as e:
                    logger.error("Fail to copy cube panoramicVideo from {} to {}: {}", asset.remote_url, asset.local_path, e)
                    raise Exception(f"Failed to download {asset.remote_url}: {e}")

    async def download_all_assets(self):
        async with self.async_client:
            tasks = []
            with tqdm(total=len(self.all_assets), unit='file', desc="Downloading assets") as progress:
                for asset in self.all_assets:
                    task = asyncio.create_task(self.download_task(asset, progress))
                    tasks.append(task)
                await asyncio.gather(*tasks)
    
    def write_xsdk_anchor(self, anchor_param : str):
        markerFile = ''
        try:
            if not anchor_param.endswith('.json'):
                anchorInfoResp = self.sync_client.post(
                    "https://lbvr-api.xverse.cn/app/batch_find_xverse_prod_vr_land", json={
                        "appId": self.app_id,
                        "vrLandIdList": [anchor_param] 
                    }
                )
                anchorInfoResp.raise_for_status()
                anchorInfo = anchorInfoResp.json()
                if anchorInfo['code'] != 0:
                    raise httpx.RequestError(anchorInfo['msg'])
                
                anchorConfigResp = self.sync_client.get(anchorInfo['data']['vrLandList'][0]['anchorConfigUrl'])
                anchorConfigResp.raise_for_status()
                anchorConfig = anchorConfigResp.json()
                markerFile = os.path.basename(anchorInfo['data']['vrLandList'][0]['anchorConfigUrl'])
                with open(self.asset_path / f"Files/{markerFile}", 'wb') as f:
                    f.write(orjson.dumps(anchorConfig))
            else:
                markerFile = anchor_param
            
            markerFilePath = self.asset_path / f"Files/{markerFile}"
            if not markerFilePath.exists():
                raise FileNotFoundError(f"{markerFilePath} 路线文件不存在")
        
            xsdkCfg = IniConfigHelper()
            phySceneSection = "/Script/XBizUtil.XVersePhySceneConfig"
            xsdkCfgPath = os.path.join(self.build_cfg_path, 'DefaultXSDKConfig.ini')
            xsdkCfg.read(xsdkCfgPath)
            xsdkCfg.set(phySceneSection, "MarkerFile", markerFile)
            xsdkCfg.write(xsdkCfgPath)
            xphyCfgPath = os.path.join(self.build_cfg_path, "DefaultXPhySceneConfig.ini")
            if os.path.exists(xphyCfgPath):
                xsdkCfg.content.clear()
                xsdkCfg.read(xphyCfgPath)
                xsdkCfg.set(phySceneSection, "MarkerFile", markerFile)
                xsdkCfg.write(xphyCfgPath)
        except Exception as e:
            raise Exception(f"路线文件写入失败：{str(e)}")

    def start(self):
        start_time = time.perf_counter()
        logger.success("start downloading {} total {} assets", self.release_id, len(self.all_assets))
        self.loop.run_until_complete(self.download_all_assets())
        logger.success("download time cost: {}", time.perf_counter() - start_time)
        verifyRes = Parallel(n_jobs=-1)(delayed(isVerified)(asset) for asset in self.all_assets)

        allPBRAssetSize, allIBRAssetSize = 0, 0
        validNum = 0
        for r in verifyRes:
            if r[0]:
                validNum += 1
                allIBRAssetSize += r[1]
                allPBRAssetSize += r[2]
            else:
                logger.error(r[3])
        assert validNum == len(verifyRes), "{} failed: {}/{}, time cost: {}".format(self.release_id, validNum, len(verifyRes), time.perf_counter() - start_time)
        logger.success("{} finish: {}/{}, time cost: {}", self.release_id, validNum, len(verifyRes), time.perf_counter() - start_time)
        return allIBRAssetSize, allPBRAssetSize

if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='assetMgr', description='')
    parser.add_argument('--app_id', type=str, help='应用id', required=True)
    parser.add_argument('--release_id', type=str, help='预发布id/正式发布id', required=True)
    parser.add_argument('--xstudio_path', type=str, help='xstudio代码仓库路径', required=True)
    parser.add_argument('--pano_video_path', default = '', type=str, help='全景视频存放地址')
    parser.add_argument('--concurrent_num', type=int, default=20, help='最大同时并发下载数')
    parser.add_argument('--retry_num', type=int, default=5, help='下载重试次数')
    parser.add_argument('--bPreRelease', type=bool, default=False, help="是否预发布rid")
    parser.add_argument('--bCustomPak', type=bool, default=False, help='是否使用自定义pak')
    parser.add_argument('--xSdkEnv', type=str, default='sit', help='发布环境')
    parser.add_argument('--bCollectPso', type=bool, default=False, help="是否采集pso")
    parser.add_argument('--bNeedLowmodel', type=bool, default=True, help="是否需要低模")
    parser.add_argument('--bOnlyLowmodel', type=bool, default=False, help="是否只需要低模")
    
    args = parser.parse_args()
    assetMgr = AssetMgr(args.app_id, args.release_id, args.xstudio_path, '', args.pano_video_path, args.bPreRelease, args.bCustomPak, args.bNeedLowmodel, args.bOnlyLowmodel, args.bCollectPso, args.xSdkEnv)
    assetMgr.start()
    nativeLoadInfoPath = assetMgr.cache_path / f"{assetMgr.app_id}_{assetMgr.release_id}_loadInfo.json"
    with open(nativeLoadInfoPath, 'wb') as f:
        f.write(orjson.dumps(assetMgr.native_load_info, option=orjson.OPT_INDENT_2))



