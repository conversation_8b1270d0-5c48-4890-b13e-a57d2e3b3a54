#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流水线处理器 - 通过HTTP接口调用完成应用发布流水线
"""

import httpx
import orjson
import time
from rich import print
from typing import Optional, Dict, Any
from urllib.parse import urljoin



class AutoReleasePipeline:
    """自动发布流水线"""

    def __init__(self, base_url: str = "https://console-api.xverse.cn/release/console", b_only_pre_release: bool = False, timeout: int = 30):
        """
        初始化流水线处理器

        Args:
            base_url: API基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.b_only_pre_release = b_only_pre_release
        self.timeout = timeout
        self.client = httpx.Client(
            timeout=timeout,
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'PipelineProcessor/1.0'
            }
        )

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，确保客户端正确关闭"""
        self.close()
        return False

    def close(self):
        """关闭HTTP客户端"""
        if hasattr(self, 'client'):
            self.client.close()

    def set_auth_token(self, token: str):
        """设置认证令牌"""
        self.client.headers.update({'Authorization': f'Bearer {token}'})

    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """
        发送HTTP请求的通用方法

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE等)
            endpoint: API端点
            **kwargs: 其他请求参数

        Returns:
            响应的JSON数据

        Raises:
            httpx.HTTPError: 请求失败时抛出异常
        """
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))

        try:
            # print(f"发送 {method} 请求到: {url}")
            response = self.client.request(
                method=method,
                url=url,
                **kwargs
            )
            response.raise_for_status()

            result = response.json()
            # print(f"请求成功，响应: {orjson.dumps(result, option=orjson.OPT_INDENT_2).decode()}")
            return result

        except httpx.HTTPError as e:
            print(f"请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    print(f"错误详情: {orjson.dumps(error_detail, option=orjson.OPT_INDENT_2).decode()}")
                except Exception:
                    print(f"错误响应: {e.response.text}")
            raise
    
    def process_pipeline(self, app_id: str) -> str:
        """
        处理完整的流水线任务

        Args:
            app_id: 应用ID

        Returns:
            release_id: 发布ID
        """
        print(f"开始处理应用 {app_id} 的流水线任务")

        try:
            # 步骤1: 获取分支信息
            branch_info = self._get_app_branches(app_id)
            # print(f"获取到分支信息: {branch_info}")

            # 步骤2: 创建预发布任务
            pre_release_id = self._create_pre_release(app_id, branch_info)
            print(f"创建预发布任务，pre_release_id: {pre_release_id}")
            if self.b_only_pre_release:
                return pre_release_id

            # 步骤3: 获取标签绑定信息
            tag_bind_info = self._get_tag_bind_list(app_id)
            # print(f"获取标签绑定信息: {tag_bind_info}")

            # 步骤4: 获取bundle列表
            bundle_info = self._get_bundle_list(app_id, tag_bind_info)
            # print(f"获取bundle列表信息: {bundle_info}")

            # 步骤5: 获取最新releaseId和psoInfo
            latest_release_pso_info = self._get_latest_release_and_pso_info(app_id)
            # print(f"获取最新发布信息和psoInfo: {latest_release_pso_info}")

            # 步骤6: 创建正式发布
            final_release_id = self._create_native_release(
                app_id,
                pre_release_id,
                tag_bind_info,
                bundle_info,
                latest_release_pso_info
            )
            print(f"创建正式发布完成，final_release_id: {final_release_id}")

            return final_release_id

        except Exception as e:
            print(f"流水线处理失败: {e}")
            raise
    
    def _get_app_branches(self, app_id: str, release_type: str = "Normal") -> Dict[str, Any]:
        """
        获取应用的分支信息

        Args:
            app_id: 应用ID
            release_type: 发布类型，默认为"Normal"

        Returns:
            包含roomId、skinId、branchId信息的字典
        """
        endpoint = f'/release_mgt/v1/apps/{app_id}/branches'
        data = {'type': release_type}

        # print(f"获取分支信息，请求体: {orjson.dumps(data, option=orjson.OPT_INDENT_2).decode()}")

        response = self._make_request('GET', endpoint, json=data)

        # 解析响应，提取roomId、skinId、branchId
        branch_info = self._parse_branch_response(response)
        return branch_info

    def _parse_branch_response(self, response: Dict[Any, Any]) -> Dict[str, Any]:
        """
        解析分支响应，提取roomId、skinId、branchId
        如果一个skin下有多个branch，默认取第一个

        Args:
            response: API响应数据

        Returns:
            包含roomId、skinId、branchId的字典
        """
        result = {
            'rooms': []
        }

        # 检查响应结构，可能的字段名
        data = response.get('data', response)

        # 尝试不同的字段名
        rooms_data = data.get('roomList', {})

        if not rooms_data:
            print(f"未找到rooms数据，完整响应: {orjson.dumps(response, option=orjson.OPT_INDENT_2).decode()}")
            return result

        for room in rooms_data:
            room_id = room.get('roomId')
            room_info = {
                'roomId': room_id,
                'skins': []
            }

            # 查找skins数据
            skins_data = room.get('skinList', [])

            for skin in skins_data:
                skin_id = skin.get('skinId')
                skin_info = {
                    'skinId': skin_id,
                    'branches': []
                }

                # 查找branches数据
                branches_data = skin.get('branchList', [])

                if branches_data:
                    # 如果有多个branch，取第一个
                    first_branch = branches_data[0]
                    branch_id = first_branch.get('branchId')
                    skin_info['selectedBranchId'] = branch_id
                    skin_info['branches'] = branches_data

                room_info['skins'].append(skin_info)

            result['rooms'].append(room_info)

        # print(f"解析得到的分支信息: {orjson.dumps(result, option=orjson.OPT_INDENT_2).decode()}")
        return result

    def _create_pre_release(self, app_id: str, branch_info: Dict[str, Any]) -> str:
        """
        创建预发布任务

        Args:
            app_id: 应用ID
            branch_info: 从步骤1获取的分支信息

        Returns:
            pre_release_id: 预发布ID
        """
        endpoint = f'/release_mgt/v1/apps/{app_id}/pre_release/native'

        # 构建branchList
        branch_list = self._build_branch_list(branch_info)

        # 构建请求体
        data = {
            "appId": app_id,
            "name": "CIAutoBuild",
            "desc": "CIAutoBuild",
            "nativeAsset": {
                "ueVersion": "5_2_1"
            },
            "object": {
                "branchList": branch_list
            },
            "platformList": [
                "SOURCE",
                "ANDROID"
            ],
            "isSkipCook": True
        }

        print(f"创建预发布任务请求体: {orjson.dumps(data, option=orjson.OPT_INDENT_2).decode()}")

        response = self._make_request('POST', endpoint, json=data)

        # 从响应中提取pre_release_id
        pre_release_id = response['data']['releaseId']
        return pre_release_id

    def _build_branch_list(self, branch_info: Dict[str, Any]) -> list:
        """
        根据分支信息构建branchList

        Args:
            branch_info: 分支信息

        Returns:
            branchList: 分支列表
        """
        branch_list = []

        if 'rooms' in branch_info:
            for room in branch_info['rooms']:
                room_id = room.get('roomId')

                if 'skins' in room:
                    for skin in room['skins']:
                        skin_id = skin.get('skinId')
                        selected_branch_id = skin.get('selectedBranchId')

                        if room_id and skin_id and selected_branch_id:
                            branch_item = {
                                "roomId": str(room_id),
                                "skinId": str(skin_id),
                                "branchId": str(selected_branch_id)
                            }
                            branch_list.append(branch_item)

        # print(f"构建的branchList: {orjson.dumps(branch_list, option=orjson.OPT_INDENT_2).decode()}")
        return branch_list

    def _get_tag_bind_list(self, app_id: str, ue_version: str = "5_2_1", store_tag_id: str = "common") -> Dict[str, Any]:
        """
        获取标签绑定列表

        Args:
            app_id: 应用ID
            ue_version: UE版本，默认为"5_2_1"
            store_tag_id: 存储标签ID，默认为"common"

        Returns:
            包含pathId和tagList的字典
        """
        endpoint = f'/asset_tag_mgt/v1/apps/{app_id}/get_tag_bind_list'
        params = {
            'ueVersion': ue_version,
            'storeTagId': store_tag_id
        }

        # print(f"获取标签绑定列表，参数: {params}")

        response = self._make_request('GET', endpoint, params=params)

        # 解析响应，提取pathId和tagList
        tag_bind_info = self._parse_tag_bind_response(response)
        return tag_bind_info

    def _parse_tag_bind_response(self, response: Dict[Any, Any]) -> Dict[str, Any]:
        """
        解析标签绑定响应，提取pathId和tagList

        Args:
            response: API响应数据

        Returns:
            包含pathId和tagList的字典
        """
        result = {
            'pathId': [],
            'tagList': []
        }

        # 检查是否有data字段包装
        data = response.get('data', response)
        for tag_bind_info in data['pathList']:
            result['pathId'].append(tag_bind_info['pathId'])
            result['tagList'].append(tag_bind_info['tagList'][0])

        # print(f"解析得到的标签绑定信息: {orjson.dumps(result, option=orjson.OPT_INDENT_2).decode()}")

        return result

    def _get_bundle_list(self, app_id: str, tag_bind_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取bundle列表

        Args:
            app_id: 应用ID
            tag_bind_info: 从步骤3获取的标签绑定信息

        Returns:
            包含bundleList信息的字典
        """
        endpoint = f'/release_mgt/v1/apps/{app_id}/bundle_list'

        # 构建tagList查询参数
        tag_list = tag_bind_info.get('tagList', [])
        if not tag_list:
            print("tagList为空，无法获取bundle列表")
            return {'bundleList': []}

        # 构建查询参数，每个tag作为一个tagList参数
        params = []
        for tag in tag_list:
            params.append(('tagList', tag))

        if not params:
            print("没有有效的tagList参数，无法获取bundle列表")
            return {'bundleList': []}

        # print(f"获取bundle列表，tagList参数: {[param[1] for param in params]}")

        # 使用httpx发送带有重复参数名的GET请求
        response = self._make_request_with_repeated_params('GET', endpoint, params)

        # 解析响应，提取bundleList
        bundle_info = self._parse_bundle_list_response(response)
        return bundle_info

    def _make_request_with_repeated_params(self, method: str, endpoint: str, params: list) -> Dict[Any, Any]:
        """
        发送带有重复参数名的HTTP请求

        Args:
            method: HTTP方法
            endpoint: API端点
            params: 参数列表，格式为[(key, value), (key, value), ...]

        Returns:
            响应的JSON数据
        """
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))

        # 手动构建查询字符串以支持重复参数名
        from urllib.parse import urlencode
        query_string = urlencode(params)
        full_url = f"{url}?{query_string}"

        try:
            # print(f"发送 {method} 请求到: {full_url}")
            response = self.client.request(method=method, url=full_url)
            response.raise_for_status()

            result = response.json()
            # print(f"请求成功，响应: {orjson.dumps(result, option=orjson.OPT_INDENT_2).decode()}")
            return result

        except httpx.HTTPError as e:
            print(f"请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    print(f"错误详情: {orjson.dumps(error_detail, option=orjson.OPT_INDENT_2).decode()}")
                except Exception:
                    print(f"错误响应: {e.response.text}")
            raise

    def _parse_bundle_list_response(self, response: Dict[Any, Any]) -> Dict[str, Any]:
        """
        解析bundle列表响应，提取bundleList中的id

        Args:
            response: API响应数据

        Returns:
            包含bundleList和提取的id列表的字典
        """
        result = {
            'bundleIds': []
        }

        # 检查是否有data字段包装
        data = response.get('data', response)


        # 从bundleList中提取id
        for bundle in data['bundleList']:
            result['bundleIds'].append(bundle['id'])

        # print(f"解析得到的bundle信息: bundleIds={result['bundleIds']}")

        if not result['bundleIds']:
            print("未能从bundleList中提取到任何id")

        return result

    def _get_latest_release_and_pso_info(self, app_id: str) -> Dict[str, Any]:
        """
        获取最新的releaseId和psoInfo

        Args:
            app_id: 应用ID

        Returns:
            包含releaseId和psoInfo的字典
        """
        # 步骤5.1: 获取最新的releaseId
        latest_release_id = self._get_latest_release_id(app_id)

        # 步骤5.2: 获取psoInfo
        if latest_release_id:
            pso_info = self._get_pso_info(app_id, latest_release_id)
        else:
            pso_info = {
                "psoUrl": "",
                "uploader": "",
                "updatedAt": ""
            }

        return pso_info

    def _get_latest_release_id(self, app_id: str) -> str:
        """
        获取最新的releaseId

        Args:
            app_id: 应用ID

        Returns:
            最新的releaseId
        """
        endpoint = f'/release_mgt/v1/apps/{app_id}/page_read'

        # 构建请求体
        data = {
            "page": 0,
            "pageSize": 1,
            "tagList": [],
            "envList": [],
            "statusList": [],
            "creatorList": [],
            "typeList": [],
            "filter": {
                "key": "ID",
                "value": ""
            },
            "classList": [
                "NATIVE_RELEASE"
            ]
        }

        # print(f"获取最新releaseId，请求体: {orjson.dumps(data, option=orjson.OPT_INDENT_2).decode()}")

        response = self._make_request('POST', endpoint, json=data)

        # 解析响应，提取最新的releaseId
        latest_release_id = self._extract_latest_release_id(response)
        return latest_release_id

    def _extract_latest_release_id(self, response: Dict[Any, Any]) -> str:
        """
        从page_read响应中提取最新的releaseId

        Args:
            response: API响应数据

        Returns:
            最新的releaseId
        """
        # 检查是否有data字段包装
        data = response.get('data', response)

        release_list = data['releaseList']
        

        # 获取第一个（最新的）发布记录
        if len(release_list) == 0:
            print("发布列表为空，没有可用的发布记录")
            return ""

        latest_release = release_list[0]['id']
        print(f"最新releaseId:{latest_release}")
        return latest_release


    def _get_pso_info(self, app_id: str, release_id: str, platform: str = "SOURCE") -> Dict[Any, Any]:
        """
        获取psoInfo

        Args:
            app_id: 应用ID
            release_id: 发布ID
            platform: 平台，默认为"SOURCE"

        Returns:
            psoInfo数据
        """
        # 构建config.json的URL
        config_url = f"https://static.xverse.cn/console/config/{app_id}/{release_id}/{platform}/config.json"

        try:
            print(f"获取psoInfo，URL: {config_url}")

            # 使用GET请求获取config.json
            response = self.client.get(config_url)
            response.raise_for_status()

            config_data = response.json()
            print("成功获取config.json")

            # 从config.json中提取psoInfo
            pso_info = config_data['psoInfo']
            return pso_info

        except httpx.HTTPError as e:
            print(f"获取config.json失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"错误响应状态码: {e.response.status_code}")
                print(f"错误响应内容: {e.response.text}")
            raise
        except Exception as e:
            print(f"解析config.json失败: {e}")
            raise

    def _create_native_release(
        self,
        app_id: str,
        pre_release_id: str,
        tag_bind_info: Dict[str, Any],
        bundle_info: Dict[str, Any],
        latest_release_pso_info: Dict[str, Any]
    ) -> str:
        """
        创建正式的native发布

        Args:
            app_id: 应用ID
            pre_release_id: 预发布ID
            tag_bind_info: 标签绑定信息
            bundle_info: bundle信息
            latest_release_pso_info: 最新发布信息

        Returns:
            最终的releaseId
        """
        endpoint = f'/release_mgt/v2/apps/{app_id}/release/native'

        # 构建请求体
        data = self._build_native_release_data(
            app_id,
            pre_release_id,
            tag_bind_info,
            bundle_info,
            latest_release_pso_info
        )

        print(f"创建正式发布，请求体: {orjson.dumps(data, option=orjson.OPT_INDENT_2).decode()}")

        response = self._make_request('POST', endpoint, json=data)

        # 从响应中提取最终的releaseId
        final_release_id = response['data']['releaseId']
        return final_release_id

    def _build_native_release_data(
        self,
        app_id: str,
        pre_release_id: str,
        tag_bind_info: Dict[str, Any],
        bundle_info: Dict[str, Any],
        latest_release_pso_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        构建native发布的请求体

        Args:
            app_id: 应用ID
            pre_release_id: 预发布ID
            tag_bind_info: 标签绑定信息
            bundle_info: bundle信息
            latest_release_pso_info: 最新发布信息

        Returns:
            请求体数据
        """


        # 构建tagPathList
        tag_path_list = self._build_tag_path_list(tag_bind_info)

        # 构建bundleList
        bundle_list = bundle_info.get('bundleIds', [])

        # 生成当前时间戳作为globalVersion
        global_version = str(int(time.time()))

        # 构建请求体
        data = {
            "appId": app_id,
            "name": "CIAutoBuild",
            "desc": "CIAutoBuild",
            "releaseId": pre_release_id,
            "nativeAsset": {
                "globalVersion": global_version,
                "tagPathList": tag_path_list,
                "extra": "",
                "ueVersion": "5_2_1",
                "assetInfoList": [],
                "lockedAssetInfoList": [],
                "psoInfo": latest_release_pso_info,
                "storeTagId": "common"
            },
            "platformList": [
                "SOURCE",
                "ANDROID"
            ],
            "isSkipCook": True,
            "bundleList": bundle_list
        }

        return data

    def _build_tag_path_list(self, tag_bind_info: Dict[str, Any]) -> list:
        """
        构建tagPathList

        Args:
            tag_bind_info: 标签绑定信息

        Returns:
            tagPathList列表
        """
        tag_path_list = []

        # 从tag_bind_info中获取pathId
        path_id = tag_bind_info.get('pathId')

        if path_id:
            # 如果pathId是字符串，直接添加
            if isinstance(path_id, str):
                tag_path_list.append(path_id)
            # 如果pathId是列表，添加所有元素
            elif isinstance(path_id, list):
                tag_path_list.extend([str(p) for p in path_id if p])
            else:
                tag_path_list.append(str(path_id))
        else:
            raise ValueError("无法构建tagPathList")

        # print(f"构建的tagPathList: {tag_path_list}")
        return tag_path_list


def pipeline_task(app_id: str, auth_token: Optional[str] = None, base_url: str = "https://console-api.xverse.cn/release/console") -> str:
    """
    流水线任务主函数

    Args:
        app_id: 应用ID
        auth_token: 认证令牌（可选）
        base_url: API基础URL

    Returns:
        release_id: 发布ID
    """
    with AutoReleasePipeline(base_url=base_url) as processor:
        if auth_token:
            processor.set_auth_token(auth_token)

        return processor.process_pipeline(app_id)


if __name__ == "__main__":
    # 测试示例
    test_app_id = "11391"
    try:
        release_id = pipeline_task(test_app_id, auth_token = '4e3fa26e64d24869ac691578b149a7b7')
        print(f"流水线任务完成，release_id: {release_id}")
    except Exception as e:
        print(f"流水线任务失败: {e}")
