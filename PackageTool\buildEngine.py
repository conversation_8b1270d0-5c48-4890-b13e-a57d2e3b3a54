#!/usr/bin/python
# -*- coding: utf-8
import sys
import json
import os
from pathlib import Path
import subprocess
import json
import codecs
import shutil
import io

import ciTools
from ciTools import BuildEngineParam
from xverseUtil import IniConfigHelper

sys.path.append(".")

logTag = "BuildEngine"


def readParams(argv, buildParam: BuildEngineParam):
    ciTools.readBuildParams(argv, buildParam)


def settingParams(buildParam: BuildEngineParam):
    printLog("settingParams")
    if buildParam.engineForTest == True:
        buildParam.projectOutPutDir = ciTools.XCiGlobalConfig.engineOutPutDirForTest
    currentYMDHMS = ciTools.currentYMDHMS()
    buildParam.buildTime = currentYMDHMS
    buildParam.taskStartTime = ciTools.getCurrentDateTimeStr1()
    buildParam.outputFileName = "XVerseEngine-{}".format(currentYMDHMS)
    buildParam.fullProjectOutPutDir = os.path.join(buildParam.projectOutPutDir, buildParam.outputFileName)
    buildParam.projectOutPutZipName = buildParam.outputFileName + ".zip"
    buildParam.fullProjectOutPutZipPath = os.path.join(buildParam.projectOutPutDir, buildParam.projectOutPutZipName)
    if buildParam.projectNameForNotify is None or len(buildParam.projectNameForNotify) < 1:
        buildParam.projectNameForNotify = f"UE{buildParam.engineMajorVersion}引擎"
    platformStr = ""
    if buildParam.withWin64 == True:
        platformStr += ".Win64"
    if buildParam.withLinux == True:
        platformStr += ".Linux"
    if buildParam.withAndroid == True:
        platformStr += ".Android"
    if buildParam.withIOS == True:
        platformStr += ".IOS"
    if buildParam.withMac == True:
        platformStr += ".Mac"
    if len(platformStr) < 1:
        platformStr = "UNKNOWN"
    else:
        platformStr = platformStr[1:]
    buildParam.projectPlatformForNotify = platformStr


def settingSystemCompileEnv(buildParam: BuildEngineParam):
    # setting clang env
    if buildParam.engineMajorVersion < 5:
        os.environ["LINUX_MULTIARCH_ROOT"] = "C:\\UnrealToolchains\\v17_clang-10.0.1-centos7"
        os.environ["NDK_ROOT"] = "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.4.7075529"
        os.environ["NDKROOT"] = "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\21.4.7075529"

    else:
        os.environ["LINUX_MULTIARCH_ROOT"] = "C:\\UnrealToolchains\\v21_clang-15.0.1-centos7"
        os.environ["NDK_ROOT"] = "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393"
        os.environ["NDKROOT"] = "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393"


def printParam(buildParam: BuildEngineParam, reason="None"):
    printLog("printParam = " + reason)
    attr = [a for a in dir(buildParam) if not a.startswith('__')]
    for a in attr:
        printLog("{0} = {1}".format(a, getattr(buildParam, a)))


def getEngineCommitInfo(buildParam: BuildEngineParam):
    printLog("getEngineCommitInfo")
    ret = ciTools.getEngineCommitInfo(buildParam, buildParam.projectSourceDir, buildParam.branch)
    if ret == True:
        buildParam.commitId = buildParam.engineCommitId
        buildParam.commitVersion = buildParam.engineCommitVersion
        buildParam.commitInfo = buildParam.engineCommitInfo
        buildParam.commitUser = buildParam.engineCommitUser
        buildParam.commitDate = buildParam.engineCommitDate
        buildParam.author = buildParam.engineAuthor
        buildParam.authorDate = buildParam.engineAuthorDate


def createOutPutDir(buildParam: BuildEngineParam):
    printLog("createOutPutDir " + buildParam.fullProjectOutPutDir)
    if not os.path.exists(buildParam.fullProjectOutPutDir):
        os.makedirs(buildParam.fullProjectOutPutDir)


def doBuildEngine(buildParam: BuildEngineParam):
    print("doBuildEngine={0}".format(buildParam))
    buildEngineScriptXml = os.path.join(buildParam.packageToolDir, "InstalledEngineBuild.xml")
    if buildParam.engineMajorVersion >= 5:
        buildEngineScriptXml = os.path.join(buildParam.packageToolDir, "InstalledEngineBuild5.xml")
    buildScriptCmd = "{0}\Engine\Build\BatchFiles\RunUAT.bat BuildGraph -target=\"Make Installed Build Win64\" -script=\"{1}\""
    buildPlatformCmd1 = "-set:WithWin64={0} -set:WithAndroid={1} -set:WithMac={2} -set:WithIOS={3} -set:WithLinux={4}"
    buildPlatformCmd2 = "-set:WithTVOS={0} -set:WithLinuxAArch64={1} -set:WithLumin={2} -set:WithHoloLens={3} -set:WithWin32={4}"
    buildBuildTypeCmd = "-set:WithFullDebugInfo={} -set:WithDDC={} -set:VS2019=true -set:GameConfigurations={} -set:EmbedSymStore={} -set:EmbedRemoveSymbol={} -set:LocalInstalledDirOverride={}"
    buildScriptCmd = buildScriptCmd.format(buildParam.projectSourceDir, buildEngineScriptXml)
    buildPlatformCmd1 = buildPlatformCmd1.format(buildParam.withWin64, buildParam.withAndroid, buildParam.withMac,
                                                 buildParam.withIOS, buildParam.withLinux)
    buildPlatformCmd2 = buildPlatformCmd2.format(buildParam.withTVOS, buildParam.withLinuxAArch64, buildParam.withLumin,
                                                 buildParam.withHoloLens, buildParam.withWin32)
    buildBuildTypeCmd = buildBuildTypeCmd.format(buildParam.withFullDebugInfo, buildParam.withDDC,
                                                 buildParam.gameConfigurations, buildParam.embedSymStore,
                                                 buildParam.embedRemoveSymbol, buildParam.fullProjectOutPutDir)

    extendCmd = ""
    if buildParam.engineMajorVersion >= 5:
        extendCmd += " -set:WithLinuxArm64={}".format(buildParam.withLinuxArm64)

    buildCmd = "{0} {1} {2} {3} {4}".format(buildScriptCmd, buildPlatformCmd1, buildPlatformCmd2, buildBuildTypeCmd,
                                            extendCmd)
    buildCmd = buildCmd.replace("True", "true")
    buildCmd = buildCmd.replace("False", "false")
    printLog("doBuildEngine buildCmd={}".format(buildCmd))
    return ciTools.runScript(buildCmd)[0]


def writeBranchInfo(buildParam: BuildEngineParam):
    printLog("writeBranchInfo " + buildParam.fullProjectOutPutDir)
    versionRootDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Version")
    os.mkdir(versionRootDir)

    # EngineVersion.ini
    engineVersionFile = os.path.join(versionRootDir, "EngineVersion.ini")
    file = open(engineVersionFile, "a+", encoding="utf-8")
    file.write("Engine:" + buildParam.commitId)
    file.write("\n")
    file.write("BuildTime:" + buildParam.buildTime)
    file.write("\n")
    file.write("Branch:{0}".format(buildParam.branch))
    file.write("\n")
    file.write("CommitId:" + buildParam.commitId)
    file.write("\n")
    file.write("CommitVersion:{0}".format(buildParam.commitVersion))
    file.write("\n")
    file.write("CommitInfo:{0}".format(buildParam.commitInfo))
    file.write("\n")
    file.write("Version:{0}.{1}".format(buildParam.mainVersion, buildParam.commitVersion))
    file.write("\n")
    file.write("Remarks:{0}".format(buildParam.remarks))
    file.write("\n")
    file.write("Executor:{0}".format(buildParam.executor))
    file.write("\n")
    file.write("EngineMajorVersion:{0}".format(buildParam.engineMajorVersion))
    file.write("\n")
    file.write("EngineMinorVersion:{0}".format(buildParam.engineMinorVersion))
    file.write("\n")
    file.write("EngineBuildVersion:{0}".format(buildParam.engineBuildVersion))
    file.close()

    # EngineInstalled.ini
    engineInstalledFile = os.path.join(versionRootDir, "EngineInstalled.ini")
    file = open(engineInstalledFile, "a+")
    file.write("Branch:{0}".format(buildParam.branch))
    file.close()

    # EngineBranch.ini
    engineBranchFile = os.path.join(versionRootDir, "EngineBranch.ini")
    file = open(engineBranchFile, "a+")
    file.write("Branch:{0}".format(buildParam.branch))
    file.close()

    # after build and upload samba


def writeEngineRunInfo(buildParam: BuildEngineParam):
    printLog("writeEngineRunInfo " + buildParam.fullProjectOutPutDir)
    versionRootDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Version")
    # EngineLock
    if buildParam.embedEngineLock:
        engineLockFile = os.path.join(versionRootDir, "EngineLock.ini")
        file = open(engineLockFile, "a+")
        file.write("Branch:{0}".format(buildParam.branch))
        file.close()
    # EngineWork
    if buildParam.embedEngineWork == False:
        engineWorkFile = os.path.join(versionRootDir, "EngineNoWork.ini")
        file = open(engineWorkFile, "a+")
        file.write("Branch:{0}".format(buildParam.branch))
        file.close()


def copyRuntimeDLL(buildParam: BuildEngineParam):
    printLog("copyRuntimeDLL {}".format(buildParam.fullProjectOutPutDir))
    src = os.path.join(buildParam.projectSourceDir, "Engine", "Binaries", "ThirdParty", "AppLocalDependencies", "Win64",
                       "Microsoft.VC.CRT")
    dest = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Binaries", "Win64")
    if not os.path.exists(dest):
        os.makedirs(dest)
    if os.path.exists(src):
        ciTools.copy_dir(src, dest)


def removeUnusedEngineFiles(buildParam: BuildEngineParam):
    printLog("removeUnusedEngineFiles {}".format(buildParam.fullProjectOutPutDir))
    holoslensFile = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Intermediate", "ScriptModules",
                                 "Hololens.Automation.json")
    if os.path.exists(holoslensFile) and buildParam.withHoloLens == False:
        ciTools.removeFile(holoslensFile)


def copyEngineForceStageFile(buildParam: BuildEngineParam):
    printLog("copyEngineForceStageFile {}".format(buildParam.projectSourceDir))
    src = os.path.join(buildParam.projectSourceDir, "Engine", "Binaries", "Win64", "tbb.pdb")

    dest = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Binaries", "Win64", "tbb.pdb")
    if os.path.exists(dest):
        return False
    if os.path.exists(src):
        ciTools.copyFileByShutil(src, dest)
        return True
    else:
        printLog("copyEngineForceStageFile error not exist {}".format(src))
    return False


def updateEnginePlugin(buildParam: BuildEngineParam):
    printLog("updateEnginePlugin {}".format(buildParam.engineMajorVersion))
    if buildParam.engineMajorVersion >= 5:
        # enbale bridge plugin by default with artplugin
        bridgePluginPath = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "Bridge",
                                        "Bridge.uplugin")
        if buildParam.embedEngineBridgePlugin or buildParam.copyEngineForArtPlugin == True:
            ciTools.enablePluginDefaultState(bridgePluginPath, True)
        else:
            ciTools.enablePluginDefaultState(bridgePluginPath, False)
        # copy index
        bridgeThirdPartySrc = os.path.join(buildParam.projectSourceDir, "Engine", "Plugins", "Bridge", "ThirdParty")

        bridgeThirdPartyDest = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "Bridge",
                                            "ThirdParty")
        ciTools.copyDirByShutil(bridgeThirdPartySrc, bridgeThirdPartyDest)


def updateEngineIni(buildParam: BuildEngineParam):
    printLog("updateEngineIni " + buildParam.fullProjectOutPutDir)

    config = IniConfigHelper()
    src = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Config", "BaseEngine.ini")
    if os.path.exists(src):
        config.read(src)
    # fastbuild
    # [/Script/Engine.RendererSettings]
    config.set("/Script/Engine.RendererSettings", "r.FASTBuildShaderCompile", 0)
    # deivedata
    if buildParam.engineMajorVersion < 5:
        deriveData = os.path.join(buildParam.packageToolDir, "Config", "EngineDeriveData.ini")
        if os.path.exists(deriveData):
            config.append(deriveData)

    # write to file
    dest = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Config", "BaseEngine.ini")
    config.write(dest)


def updateGameIni(buildParam: BuildEngineParam):
    printLog("updateGameIni " + buildParam.fullProjectOutPutDir)
    if buildParam.engineMajorVersion >= 5:
        config = IniConfigHelper()
        src = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Config", "BaseGame.ini")
        if os.path.exists(src):
            config.read(src)
        # disble use io store
        config.set("/Script/UnrealEd.ProjectPackagingSettings", "bUseIoStore", False)
        # write to file
        dest = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Config", "BaseGame.ini")
        config.write(dest)


def copyTools(buildParam: BuildEngineParam):
    printLog("copyTools " + buildParam.fullProjectOutPutDir)
    srcFile = os.path.join(buildParam.packageToolDir, "MountSambaCache.bat")
    destFile = os.path.join(buildParam.fullProjectOutPutDir)
    shutil.copy(srcFile, destFile)

    unrealExe = "UE4Editor.exe"
    if buildParam.engineMajorVersion >= 5:
        unrealExe = "UnrealEditor.exe"
    filePath = os.path.join(buildParam.fullProjectOutPutDir, "UnrealEngine.bat")
    file = open(filePath, "a+")
    file.write("call MountSambaCache.bat")
    file.write("\n")
    file.write(f"start %~dp0Engine\\Binaries\\Win64\\{unrealExe}")
    file.close()

    # registerMenu
    src = os.path.join(buildParam.packageToolDir, "UnrealProjectMenuRegistration.cmd")
    if buildParam.engineMajorVersion >= 5:
        src = os.path.join(buildParam.packageToolDir, "UnrealProjectMenuRegistration_UE5.cmd")
    destRegFile = os.path.join(buildParam.fullProjectOutPutDir, "UnrealProjectMenuRegistration.cmd")
    shutil.copyfile(src, destRegFile)

    # UnrealVersionSelector
    destDir = os.path.join(buildParam.fullProjectOutPutDir, "Launcher")
    if not os.path.exists(destDir):
        os.makedirs(destDir)
    dest = os.path.join(destDir, "UnrealVersionSelector.exe")
    src = os.path.join(buildParam.packageToolDir, "UnrealVersionSelector.exe")
    if buildParam.engineMajorVersion >= 5:
        src = os.path.join(buildParam.packageToolDir, "UnrealVersionSelector-UE5.exe")
    if os.path.exists(src):
        shutil.copyfile(src, dest)
        if buildParam.engineMajorVersion >= 5:
            dest = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Binaries", "Win64",
                                "UnrealVersionSelector.exe")
            if not os.path.exists(dest):
                shutil.copyfile(src, dest)


def copyEngineBuildLog(buildParam: BuildEngineParam):
    if buildParam.projectLocalBuildLogPath is None or len(buildParam.projectLocalBuildLogPath) < 1:
        buildParam.projectLocalBuildLogPath = os.path.join(buildParam.projectOutPutDir, "Logs", buildParam.projectName,
                                                           buildParam.outputFileName)
    engineBuildLogPath = os.path.join(buildParam.engineDir, "Engine", "Programs", "AutomationTool", "Saved", "Logs")
    printLog("copyProjectBuildLog With Source={}".format(engineBuildLogPath))
    if not os.path.exists(buildParam.projectLocalBuildLogPath):
        os.makedirs(buildParam.projectLocalBuildLogPath)
    if os.path.exists(engineBuildLogPath):
        ciTools.copyDirByShutil(engineBuildLogPath, buildParam.projectLocalBuildLogPath)


def compressEngine(buildParam: BuildEngineParam):
    printLog("compressEngine " + buildParam.fullProjectOutPutDir)

    os.chdir(buildParam.projectOutPutDir)
    script = "bz c {0}".format(buildParam.projectOutPutZipName)
    script += " {0}\Engine".format(buildParam.fullProjectOutPutDir)
    script += " {0}\FeaturePacks".format(buildParam.fullProjectOutPutDir)
    script += " {0}\Launcher".format(buildParam.fullProjectOutPutDir)
    script += " {0}\Samples".format(buildParam.fullProjectOutPutDir)
    script += " {0}\Templates".format(buildParam.fullProjectOutPutDir)
    script += " {0}\\UnrealEngine.bat".format(buildParam.fullProjectOutPutDir)
    script += " {0}\\MountSambaCache.bat".format(buildParam.fullProjectOutPutDir)
    script += " {0}\\UnrealProjectMenuRegistration.cmd".format(buildParam.fullProjectOutPutDir)
    print("script=" + script)
    os.system(script)


def uploadToSamba(buildParam: BuildEngineParam):
    ciTools.uploadToSamba(buildParam.projectSambaDir, buildParam.fullProjectOutPutZipPath)


def insertEngineCosTask(build_param: BuildEngineParam, cos_state=ciTools.CosUploadStatus.StateUnLoad):
    printLog("insertEngineCosTask {}".format(build_param.engine_cos_upload_path))
    # 开发分支
    branch = build_param.branch.replace("/", "_")
    branch_dir_name = "{}".format(branch)
    # 引擎上传使用独立的文件夹保存，全为异步上传，无需额外储存一次记录文件
    # 上传文件初始化
    if not os.path.exists(ciTools.CIConfig.x_engine_tasks_dir):
        os.makedirs(ciTools.CIConfig.x_engine_tasks_dir)
    # 格式化输出任务名， ex: xstudio-ue5.2_188_20240222-161731.task
    file_name = "{}_{}_{}.json".format(branch, build_param.commitVersion, build_param.buildTime)
    file_path = os.path.join(ciTools.CIConfig.x_engine_tasks_dir, file_name)

    # 提取文件名，并在赋值的最后增加时间戳
    output_file_name = os.path.basename(build_param.fullProjectOutPutZipPath)

    # 构建 任务信息
    task_info = ciTools.EngineCosTaskInfo()
    task_info.commit_version = build_param.commitVersion
    task_info.cos_path = "{}/{}".format(build_param.engine_cos_upload_path, output_file_name)
    task_info.upload_file_path = build_param.fullProjectOutPutZipPath
    task_info.project_branch = build_param.branch
    task_info.cos_state = cos_state
    task_info.cos_region = ciTools.CIConfig.cosRegoin
    task_info.task_file_path = file_path

    # 插入任务
    return ciTools.writeEngineCosTask(file_path, task_info)


def deleteEngineCache(buildParam: BuildEngineParam):
    intermediateDir = os.path.join(buildParam.projectSourceDir, "Engine", "Intermediate")
    printLog("deleteEngineCache {}".format(intermediateDir))
    if os.path.exists(intermediateDir):
        ciTools.removeDir(intermediateDir)


def prepareBuildEngine(buildParam: BuildEngineParam):
    buildParam.fullVersion = "{}.{}".format(buildParam.mainVersion, buildParam.commitVersion)
    branchName = buildParam.branch.replace("/", "_")
    sambaPath = "{0}\{1}".format(ciTools.XCiGlobalConfig.engineSambaDir, branchName)
    buildParam.projectSambaDir = sambaPath


def printLog(msg):
    ciTools.printLogTag(logTag, msg)
    # print(msg)


def writeIOSRemoteConfig(buildParam: BuildEngineParam):
    printLog("writeIOSRemoteConfig {}".format(buildParam.withIOS))
    if buildParam.withIOS == True:
        baseEngineIniConfig = os.path.join(buildParam.projectSourceDir, "Engine", "Config", "BaseEngine.ini")
        config = IniConfigHelper()
        config.read(baseEngineIniConfig, encoding="utf-8")
        config.set("/Script/IOSRuntimeSettings.IOSRuntimeSettings", "RemoteServerName", "***********")
        config.set("/Script/IOSRuntimeSettings.IOSRuntimeSettings", "RSyncUsername", "H")
        config.set("/Script/IOSRuntimeSettings.IOSRuntimeSettings", "BundleIdentifier", "com.xverse.traveldog")
        config.write(baseEngineIniConfig)


def resetIOSRemoteConfig(buildParam: BuildEngineParam):
    printLog("resetIOSRemoteConfig {}".format(buildParam.withIOS))
    if buildParam.withIOS == True:
        baseEngineIniConfig = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Config", "BaseEngine.ini")
        config = IniConfigHelper()
        config.read(baseEngineIniConfig, encoding="utf-8")
        config.set("/Script/IOSRuntimeSettings.IOSRuntimeSettings", "RemoteServerName", "")
        config.set("/Script/IOSRuntimeSettings.IOSRuntimeSettings", "RSyncUsername", "")
        config.set("/Script/IOSRuntimeSettings.IOSRuntimeSettings", "BundleIdentifier", "")
        config.write(baseEngineIniConfig)


def buildEngineMain(buildParam: BuildEngineParam):
    ciTools.updateBuildParams(buildParam)
    ciTools.updataParamsAfterUserSetting(buildParam)
    settingSystemCompileEnv(buildParam)
    settingParams(buildParam)
    printParam(buildParam, "Before Switch Branch")
    buildResult = buildEngineInner(buildParam)
    if buildResult:
        buildParam.buildResultCode = 0
        buildParam.buildResultMsg = "OK"
    ciTools.sendBuildResultMessage(buildParam)
    if not buildResult and buildParam.clearDataWhenBuildFail:
        ciTools.removeDir(buildParam.fullProjectOutPutDir)
        ciTools.removeFile(buildParam.fullProjectOutPutZipPath)
    if not buildResult and buildParam.embedExitWhenBuildFail:
        ciTools.stopProcess(buildParam.buildResultMsg)
    return buildResult


def buildEngineInner(buildParam: BuildEngineParam):
    hasUbtRunning = ciTools.checkUBTProcess()
    if hasUbtRunning:
        buildParam.buildResultCode = 11
        buildParam.buildResultMsg = "UBT had already running"
        return False
    if buildParam.copyEngineForArtPlugin:
        if not buildParam.withWin64 or not buildParam.withLinux:
            buildParam.buildResultCode = 71
            # buildParam.buildResultMsg = "art plugin engine must be include win64 and linux"
            # return False
    if buildParam.forceResetLocalBranch:
        ciTools.resetProjectBranch(buildParam.projectSourceDir)
    if buildParam.embedGitClean:
        ciTools.gitClean(buildParam.projectSourceDir)
    if buildParam.embedCleanOldCache:
        deleteEngineCache(buildParam)
    if buildParam.embedSwitchBranch:
        switchBranchRet, msg = ciTools.switchBranch(buildParam.projectSourceDir, buildParam.branch)
        if not switchBranchRet:
            buildParam.buildResultCode = 12
            buildParam.buildResultMsg = "Switch Branch Error"
            return False
    else:
        branchInfo = ciTools.getBranch(buildParam.projectSourceDir)
        buildParam.branch = branchInfo.branch
    if buildParam.embedPullLatestCode:
        pullCodeResult = ciTools.pullBranch(buildParam.projectSourceDir)
        if not pullCodeResult:
            buildParam.buildResultCode = 13
            buildParam.buildResultMsg = "Pull Branch Error"
            return False
    ciTools.printBranchLog(buildParam.projectSourceDir)
    getEngineCommitInfo(buildParam)
    prepareBuildEngine(buildParam)
    createOutPutDir(buildParam)
    writeIOSRemoteConfig(buildParam)
    printParam(buildParam, "Before Build Engine")
    bBuildSuccess = doBuildEngine(buildParam)
    copyEngineBuildLog(buildParam)
    installedFlagPath = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Build", "InstalledBuild.txt")
    if not os.path.exists(installedFlagPath) or bBuildSuccess == False:
        buildParam.buildResultCode = 14
        buildParam.buildResultMsg = "Build Engine Error"
        return False
    writeBranchInfo(buildParam)
    updateEngineIni(buildParam)
    resetIOSRemoteConfig(buildParam)
    copyTools(buildParam)
    copyRuntimeDLL(buildParam)
    removeUnusedEngineFiles(buildParam)
    if buildParam.embedRemoveSymbol:
        ciTools.removeSymbol(buildParam.fullProjectOutPutDir, ["tbb.pdb"])
    copyEngineForceStageFile(buildParam)
    updateEnginePlugin(buildParam)
    writeEngineRunInfo(buildParam)

    if buildParam.embedUploadSamba or buildParam.embedUploadCos:
        compressEngine(buildParam)

    if buildParam.embedUploadSamba:
        uploadToSamba(buildParam)

    # 240305 注入 COS 上传逻辑
    if buildParam.embedUploadCos:
        # 240306 CR 意见: 需要单独使用线程并发上传，防止阻塞 Jenkins
        insertEngineCosTask(buildParam)

    if buildParam.copyEngineForArtPlugin:
        artPluginEngineDir = os.path.join(buildParam.artPluginEngineOutputDir, buildParam.outputFileName)
        if not os.path.exists(artPluginEngineDir):
            os.makedirs(artPluginEngineDir)
        ciTools.copyDirByShutil(buildParam.fullProjectOutPutDir, artPluginEngineDir)

    if buildParam.engineOnlyForArtPlugin:
        ciTools.removeDir(buildParam.fullProjectOutPutDir)
        ciTools.removeFile(buildParam.fullProjectOutPutZipPath)
    return True


if __name__ == "__main__":
    # sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    # sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    printLog("buildEngine {}".format(sys.argv))
    cwd = os.getcwd()
    printLog("cwd=" + cwd)
    buildParam = BuildEngineParam()
    readParams(sys.argv, buildParam)
    if buildParam.embedLocalTest:
        buildParam.embedGitClean = False
        buildParam.embedPullLatestCode = False
        buildParam.embedSwitchBranch = False
        buildParam.branch = "xstudio"
        buildParam.forceResetLocalBranch = False
        buildParam.embedCleanOldCache = False
        buildParam.embedUploadSamba = False
        buildParam.withWin64 = False
        buildParam.withIOS = False
        buildParam.withAndroid = False
        buildParam.withLinux = True
        buildParam.withMac = False
        buildParam.engineForTest = True
        buildParam.embedEngineWork = False
        buildParam.embedSymStore = False
    buildResult = buildEngineMain(buildParam)
    printLog(f"buildResult={buildResult}")
