#!/usr/bin/python
# -*- coding: utf-8
import sys

import ciTools
from ciTools import BuildEngineParam
import buildEngine

sys.path.append(".")

logTag = "BuildEngine5"


def printLog(msg):
    ciTools.printLogTag(logTag, msg)
    # print(msg)
    pass


if __name__ == "__main__":
    # sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    # sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    buildParam = BuildEngineParam()
    buildEngine.readParams(sys.argv, buildParam)
    buildParam.projectNameForNotify = "UE5.2引擎"
    buildParam.engineMajorVersion = 5
    buildParam.engineMinorVersion = 2
    buildParam.engineBuildVersion = 0
    if buildParam.embedLocalTest:
        buildParam.notifyForTest = True
        buildParam.branch = "xstudio-ue5.2"
        buildParam.embedGitClean = False
        buildParam.projectSourceDir = "C:\\work\\xverse\\UnrealEngine5.2"
        buildParam.projectOutPutDir = "C:\\work\\xverse\\Engine5OutPut"
        buildParam.projectSambaDir = "\\\\CreatorSamba\\XverseCreator\\XCreatorEngine"
        buildParam.embedUploadSamba = False
        buildParam.embedSwitchBranch = False
        buildParam.forceResetLocalBranch = False
        buildParam.embedPullLatestCode = False
        buildParam.embedCleanOldCache = False
        buildParam.embedSymStore = False
        buildParam.embedRemoveSymbol = False
        buildParam.gameConfigurations = "Development"
        buildParam.withWin64 = False
        buildParam.withLinux = False
        buildParam.withHoloLens = False
        buildParam.withAndroid = True
        buildParam.engineForTest = True
        buildParam.fullProjectOutPutDir = "C:\\work\\xverse\\ArtEngine5LocalDir\\XVerseEngine-20240308-145410"
        buildParam.fullProjectOutPutZipPath = "C:\\work\\xverse\\Engine5Output\\XVerseEngine-20240308-145410.zip"
        buildParam.projectSourceDir = "C:\\work\\xverse\\UnrealEngine5.2"
        buildParam.embedEngineBridgePlugin = True
        # buildParam.buildTime = "20240308-145410"
        # buildParam.engine_cos_upload_path = "XVerseEngine"
        # buildParam.commitVersion = 190
        # buildEngine.insertEngineCosTask(buildParam)
        # sys.exit(0)
    buildResult = buildEngine.buildEngineMain(buildParam)
    printLog(f"buildResult={buildResult}")
