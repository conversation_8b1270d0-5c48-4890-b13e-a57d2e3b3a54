#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import os
from pathlib import Path
import subprocess
import json
import codecs
import ciTools
import BuildHelper
import shutil
import io
from ciTools import BuildPluginParam
from xverseUtil import XVerseProjectManager
from xverseUtil import PluginInfo


LogTag = "BuildPlugin"


def readParams(argv, buildParam: BuildPluginParam):
    ciTools.readBuildParams(argv, buildParam)

    if buildParam.plugins is not None and len(buildParam.plugins) > 0:
        plgs = buildParam.plugins.split(";")
        for n in plgs:
            buildParam.pluginList.append(n)

def settingConfigs(buildParam: BuildPluginParam):
    currentYMDHMS = ciTools.currentYMDHMS()
    buildParam.buildTime = currentYMDHMS
    buildParam.taskStartTime = ciTools.getCurrentDateTimeStr1()
    buildParam.outputFileName = "{0}-{1}".format("XStudioPlugin", currentYMDHMS)
    buildParam.fullProjectOutPutDir = os.path.join(buildParam.projectOutPutDir, buildParam.outputFileName)
    buildParam.projectOutPutZipName = "{}.zip".format(buildParam.outputFileName)
    buildParam.fullProjectOutPutZipPath = os.path.join(buildParam.projectOutPutDir, buildParam.projectOutPutZipName)
    buildParam.engineName = ciTools.getFileName(buildParam.engineDir)
    buildParam.engineZipFile = os.path.join(ciTools.getPrevDirPath(buildParam.engineDir), "{}.zip".format(buildParam.engineName))
    buildParam.fullVersion = "{}.{}".format(buildParam.mainVersion, buildParam.commitVersion)
    
def DoBuildPlugin(buildParam: BuildPluginParam, engineDir, rootPluginPath, pluginName, pluginOutput):
    printLog("DoBuildPlugin start={0}, {1}".format(pluginName, rootPluginPath))
    bResult = False
    xProjMgr = XVerseProjectManager()
    xProjMgr.init(buildParam.projectSourceDir)
    plugin = xProjMgr.getProjectPluginInfo(pluginName)
    if plugin is None:
        return bResult
    pluginFullPath = os.path.join(plugin.path, "{}.uplugin".format(plugin.name))
    printLog("Start Read UPlugin File {}".format(pluginFullPath))
    dict = xProjMgr.getPluginDepsForDict(pluginName)
    deps = ""
    for key in dict:
        item : PluginInfo = dict[key]
        if item.type == 1 and item.path is not None:
            pluginFullPath = os.path.join(item.path, "{}.uplugin".format(item.name))
            deps += " -Dependency={}".format(pluginFullPath)
    printLog("DoBuildPlugin {} -Dependency = {}".format(pluginName, deps))
    buildCmdFormat = "{0}\Engine\Build\BatchFiles\RunUAT.bat BuildPlugin -Plugin={1} {2} -CppStd=Cpp17 -Package={3} -VS2019 -NoDeleteHostProject -TargetPlatforms={4}"
    buildCmd = buildCmdFormat.format(engineDir, pluginFullPath, deps, pluginOutput, buildParam.targetPlatform)
    printLog("buildCmd {}".format(buildCmd))
    #list_files = os.system(buildCmd)
    return ciTools.runScript(buildCmd)[0]

def copyPlugins(buildParam: BuildPluginParam):
    printLog("copyPlugins start")
    for pluginName in buildParam.pluginList:
        pluginsDir= os.path.join(buildParam.pluginBuildTempDir, pluginName, "HostProject", "Plugins", pluginName)
        printLog("copyPlugins start Plugins = {}".format(pluginsDir))
        destPluginDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", pluginName)
        if os.path.exists(pluginsDir):

            ciTools.copy_dir(pluginsDir, destPluginDir)

            pluginConfigFile = os.path.join(destPluginDir, pluginName + ".uplugin")
            if os.path.exists(pluginConfigFile):
                pjson = json.load(codecs.open(pluginConfigFile, 'r', 'utf-8-sig'))
                pjson["EnabledByDefault"] = True;
                with open(pluginConfigFile, "w") as outfile:
                    json_object = json.dumps(pjson, indent=4)
                    outfile.write(json_object)
            
            xversePluginFlagFile = os.path.join(destPluginDir, "Plugin.xverse")
            if not os.path.exists(xversePluginFlagFile):
                xfile = open(xversePluginFlagFile, "a+")
                xfile.write(pluginName)
                xfile.close()

def deletePluginsSource(buildParam: BuildPluginParam):
    printLog("deletePluginsSource start")
    for pluginName in buildParam.pluginList:
        sourceDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", pluginName, "Source")
        if os.path.exists(sourceDir):
            try:
                shutil.rmtree(sourceDir)
            except:
                printLog("deletePluginsSource remove source error {}".format(sourceDir))

        intermediateDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", pluginName, "Intermediate")
        if os.path.exists(intermediateDir):
            try:
                shutil.rmtree(intermediateDir)
            except:
                printLog("deletePluginsSource remove intermediateDir error {}".format(sourceDir))
def copyEngine(buildParam: BuildPluginParam):
    printLog("copyEngine start {}".format(buildParam.engineDir))
    srcDir = os.path.join(buildParam.engineDir)
    destDir = os.path.join(buildParam.fullProjectOutPutDir)
    if not os.path.exists(destDir):
        os.makedirs(destDir)
    if os.path.exists(srcDir):
        ciTools.copyDirByShutil(srcDir, destDir)

def copyConfig(buildParam: BuildPluginParam):
    printLog("copyConfig {} ".format(buildParam.projectName))
    destDirPath = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Config")
    if not os.path.exists(destDirPath):
        os.makedirs(destDirPath)

    destFile = os.path.join(destDirPath, "BaseXConsole.ini")
    srcFile = os.path.join(buildParam.packageToolDir, "BaseXConsole_Dev.ini")
    if buildParam.consoleEnv.find("Release") != -1:
        srcFile = os.path.join(buildParam.packageToolDir, "BaseXConsole.ini")
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)
    
    # BaseXSkinSambaCache.ini
    destFile = os.path.join(destDirPath, "BaseXSkinSambaCache.ini")
    srcFile = os.path.join(buildParam.packageToolDir, "BaseXSkinSambaCache.ini")
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)

def copyRuntimeDLL(buildParam : BuildPluginParam):
    printLog("copyRuntimeDLL " + buildParam.fullProjectOutPutDir)
    src = os.path.join(buildParam.engineDir, "Engine", "Binaries", "ThirdParty", "AppLocalDependencies", "Win64", "Microsoft.VC.CRT")
    dest = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Binaries", "Win64")
    if not os.path.exists(dest):
        os.makedirs(dest)
    if os.path.exists(src):
        ciTools.copy_dir(src, dest)

def copyTools(buildParam: BuildPluginParam):
    printLog("copyTools start")
    srcDir = "\\\\CreatorSamba\\XverseCreator\lowpolyApplication"
    destDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "lowpolyApplication")
    if not os.path.exists(destDir):
        os.makedirs(destDir)
    if os.path.exists(srcDir):
        ciTools.copy_dir(srcDir, destDir)

def copyDeletePluginTool(buildParam: BuildPluginParam):
    printLog("copyDeletePluginTool start")
    destDirPath = os.path.join(buildParam.fullProjectOutPutDir, "Tools")
    if not os.path.exists(destDirPath):
        os.makedirs(destDirPath)
    destFile = os.path.join(destDirPath, "DeleteOldXVersePlugins.bat")
    srcFile = os.path.join(buildParam.packageToolDir, "..", "ExTools", "DeleteOldXVersePlugins.bat")
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)

def modifyEnginePlugins(buildParam: BuildPluginParam):
    printLog("modifyPlugins start")
    # ModelingToolsEditorMode
    destPluginDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "Experimental", "ModelingToolsEditorMode")
    toolPluginFilr = os.path.join(buildParam.packageToolDir, "Plugins", "ModelingToolsEditorMode.uplugin")
    if os.path.exists(destPluginDir):
        if os.path.exists(toolPluginFilr):
            shutil.copy(toolPluginFilr, destPluginDir)

def genLocalSrcVersionConfig(buildParam: BuildPluginParam):
    printLog("genLocalSrcVersionConfig start")
    if "XUE" in buildParam.pluginList:
        srcVersionDirPath = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", "XUE", "Config")
        if not os.path.exists(srcVersionDirPath):
            os.makedirs(srcVersionDirPath)
        srcVersionPath = os.path.join(srcVersionDirPath, "DefaultLocalSrcVersion.ini")
        file = open(srcVersionPath, "a+")
        file.write("[/Script/XverseAssetUploader.LocalSrcVersion]")
        file.write("\n")
        file.write("SourceVersion={}".format(buildParam.commitVersion))
        file.write("\n")
        file.write("SourceCommitId={}".format(buildParam.commitId))
        file.write("\n")

def genXVerseProjectConfig(buildParam: BuildPluginParam):
    printLog("genXVerseProjectConfig start")
    if "XBaseLib" in buildParam.pluginList:
        srcVersionDirPath = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", "XBaseLib", "Config")
        os.makedirs(srcVersionDirPath, exist_ok=True)
        srcVersionPath = os.path.join(srcVersionDirPath, "XVerseProjectConfig.ini")
        file = open(srcVersionPath, "a+")
        file.write("[/Script/ProjectItem.XVerseProjectConfigMng]")
        file.write("\n")
        file.write("ProjectName={}\n".format(buildParam.projectName))
        file.write("BuildTime={}\n".format(buildParam.buildTime))
        file.write("ProjectVersion={}\n".format(buildParam.mainVersion))
        file.write("ConsoleEnv={}\n".format(buildParam.consoleEnv))
        file.write("PackageType={}\n".format(buildParam.gameConfigurations))
        file.write("Branch={}\n".format(buildParam.branch))
        file.write("CommitId={}\n".format(buildParam.commitId))
        file.write("CommitVersion={}\n".format(buildParam.commitVersion))
        file.write("CommitInfo={}\n".format(buildParam.commitInfo))
        file.write("EnginePkgVersion={}\n".format(buildParam.engineName))
        file.write("EngineBranch={}\n".format(buildParam.engineBranch))
        file.write("EngineCommitId={}\n".format(buildParam.engineCommitId))
        file.write("EngineVersion={}\n".format(buildParam.engineCommitVersion))
        file.write("Remarks={}\n".format(buildParam.remarks))
        file.write("Executor={}\n".format(buildParam.executor))
        file.write("UProjectName={}\n".format(buildParam.projectName))
        file.close()

def genXversePluginToolsVersion(buildParam: BuildPluginParam):
    printLog("XversePluginToolsVersion start")
    versionDirPath = os.path.join(buildParam.fullProjectOutPutDir, "Version")
    if not os.path.exists(versionDirPath):
        os.makedirs(versionDirPath)
    srcVersionPath = os.path.join(versionDirPath, "XversePluginToolsVersion.ini")
    file = open(srcVersionPath, "a+")
    file.write("ProjectName={}\n".format(buildParam.projectName))
    file.write("BuildTime={}\n".format(buildParam.buildTime))
    file.write("EnginePkgVersion={}\n".format(buildParam.engineName))
    file.write("Branch={}\n".format(buildParam.branch))
    file.write("CommitId={}\n".format(buildParam.commitId))
    file.write("CommitVersion={}\n".format(buildParam.commitVersion))
    file.write("CommitInfo={}\n".format(buildParam.commitInfo))
    file.write("Remarks={}\n".format(buildParam.remarks))
    file.write("Executor={}\n".format(buildParam.executor))
    file.close()

def compressPlugins(buildParam : BuildPluginParam):
    printLog("compressPlugins " + buildParam.fullProjectOutPutDir)
    os.chdir(buildParam.projectOutPutDir)
    script = "bz c {0}".format(buildParam.projectOutPutZipName)
    files = os.listdir(buildParam.fullProjectOutPutDir)
    for f in files:
        path = os.path.join(buildParam.fullProjectOutPutDir, f)
        script += " {0}".format(path)
    print("script=" + script)
    os.system(script)

def uploadToSamba(buildParam : BuildPluginParam):
    branchName = buildParam.branch.replace("/", "_")
    buildParam.projectSambaDir = "\\\\CreatorSamba\\XverseCreator\\{}\\{}-Incr\\{}-{}".format(buildParam.projectName, buildParam.projectDescribeName, buildParam.engineName, branchName)
    if buildParam.includeEngine == True:
        buildParam.projectSambaDir = "\\\\CreatorSamba\\XverseCreator\\{}\\{}-Full\\{}-{}".format(buildParam.projectName, buildParam.projectDescribeName, buildParam.engineName, branchName)
    ciTools.uploadToSamba(buildParam.projectSambaDir, buildParam.fullProjectOutPutZipPath)

def deletePluginsTemp(buildParam : BuildPluginParam):
    if os.path.exists(buildParam.pluginBuildTempDir):
        shutil.rmtree(buildParam.pluginBuildTempDir)

def genAllDependsPlugins(buildParam : BuildPluginParam):
    tempPluginList = buildParam.pluginList.copy()
    xProjMgr = XVerseProjectManager()
    xProjMgr.init(buildParam.projectSourceDir)
    for name in tempPluginList:
        pluginInfo = xProjMgr.getPluginDepsForDict(name)
        if pluginInfo is not None:
            for key in pluginInfo:
                item = pluginInfo[key]
                if item.type == 1 and item.name not in buildParam.pluginList:
                    buildParam.pluginList.append(item.name)

def printLog(msg):
    ciTools.printLogTag(LogTag, msg)

if __name__ == "__main__":
    #sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    #sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    cwd = os.getcwd()
    printLog("buildplugins cwd=" + cwd)
    BinPath = sys.argv[0]
    printLog("BinPath=" + BinPath)
    buildParam = BuildPluginParam()
    readParams(sys.argv, buildParam)
    ciTools.printBuildParam(buildParam, "after read")
    ciTools.updateBuildParams(buildParam)
    hasUbtRunning = ciTools.checkUBTProcess()
    if hasUbtRunning:
        buildParam.buildResultCode = 1
        buildParam.buildResultMsg = "UBT had already running, pls try later"
        ciTools.sendBuildResultMessage(buildParam)
        ciTools.stopProcess(buildParam.buildResultMsg)

    if buildParam.projectOutPutDir is None or len(buildParam.projectOutPutDir) < 1:
        buildParam.projectOutPutDir = os.path.join(buildParam.projectSourceDir, "PluginOutPut")

    buildParam.pluginBuildTempDir = os.path.join(buildParam.projectOutPutDir, "PluginsBuildCache")
    if (buildParam.engineDir is None or len(buildParam.engineDir) < 1) and len(buildParam.engineSearchDir) > 0:
        if buildParam.engineForTest == True:
            buildParam.engineSearchDir = ciTools.XCiGlobalConfig.engineOutPutDirForTest
        buildParam.engineDir = BuildHelper.searchEngineDir(buildParam.engineSearchDir, buildParam.engineBranch)
    ciTools.printBuildParam(buildParam, "after updateBuildParams")
    if buildParam.embedGitClean == True:
        ciTools.gitClean(buildParam.projectSourceDir)
    if buildParam.forceResetLocalBranch == True:
        ciTools.resetProjectBranch(buildParam.projectSourceDir)
    if buildParam.embedSwitchBranch == True:
        result, msg = ciTools.switchBranch(buildParam.projectSourceDir, buildParam.branch)
        if result == False:
            buildParam.buildResultMsg = "{} switch branch error".format(buildParam.projectSourceDir)
            ciTools.sendBuildResultMessage(buildParam)
            ciTools.stopProcess("switch branch error")
    if buildParam.embedPullLatestCode == True:        
        result = ciTools.pullBranch(buildParam.projectSourceDir)
        if result == False:
            buildParam.buildResultMsg = "{} pull code error".format(buildParam.projectSourceDir)
            ciTools.sendBuildResultMessage(buildParam)
            ciTools.stopProcess("pull error")
    ciTools.printBranchLog(buildParam.projectSourceDir)
    if  len(buildParam.engineDir) < 1 or not os.path.exists(buildParam.engineDir):
        buildParam.buildResultMsg = "engine not exist searchDir={}, engineDir={}".format(buildParam.engineSearchDir, buildParam.engineDir)
        ciTools.sendBuildResultMessage(buildParam)
        ciTools.stopProcess("engine not exist searchDir={}, engineDir={}".format(buildParam.engineSearchDir, buildParam.engineDir))
    ciTools.getEngineCommitInfo(buildParam, buildParam.engineDir, buildParam.engineBranch)
    ciTools.getProjectCommitInfo(buildParam)
    settingConfigs(buildParam)
    ciTools.printBuildParam(buildParam, "GetCommitInfo End")
    deletePluginsTemp(buildParam)
    if buildParam.pluginList is None or len(buildParam.pluginList) < 1:
        ciTools.stopProcess("no plugins input")
    result = False
    genAllDependsPlugins(buildParam)
    printLog("DoBuildPlugin All Plugins {}".format(','.join(buildParam.pluginList)))
    ciTools.printBuildParam(buildParam, "after genAllDependsPlugins")
    
    rootPluginPath = os.path.join(buildParam.projectSourceDir, "Plugins")
    for pName in buildParam.pluginList:
        pluginOutPutDir = os.path.join(buildParam.pluginBuildTempDir, pName)
        result = DoBuildPlugin(buildParam, buildParam.engineDir, rootPluginPath, pName, pluginOutPutDir)
        printLog("DoBuildPlugin Compile Plugin end {}".format(pName))
        if result == False:
            printLog("DoBuildPlugin Compile Plugin {} Error".format(pName))
            buildParam.buildResultMsg = "Plugin {} Compile Error".format(pName)
            ciTools.sendBuildResultMessage(buildParam)
            ciTools.stopProcess("compile plugin error")

    copyPlugins(buildParam)
    #copyConfig(buildParam)
    copyDeletePluginTool(buildParam)
    genXversePluginToolsVersion(buildParam)
    genLocalSrcVersionConfig(buildParam)
    genXVerseProjectConfig(buildParam)
    deletePluginsSource(buildParam)
    compressPlugins(buildParam)
    if buildParam.embedUploadSamba:
        uploadToSamba(buildParam)
    buildParam.buildResultCode = 0
    buildParam.buildResultMsg = "OK"
    ciTools.sendBuildResultMessage(buildParam)