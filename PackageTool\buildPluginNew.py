#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import os
from pathlib import Path
import subprocess
import json
import codecs
import ciTools
import BuildHelper
import shutil
import io
from ciTools import BuildPluginParam
import projectGenerateUtil
from xverseUtil import XVerseProjectManager


LogTag = "BuildPluginNew"


def readParams(argv, buildParam: BuildPluginParam):
    ciTools.readBuildParams(argv, buildParam)
    if buildParam.plugins is not None and len(buildParam.plugins) > 0:
        plgs = buildParam.plugins.split(";")
        for n in plgs:
            buildParam.pluginList.append(n)

def settingConfigs(buildParam: BuildPluginParam):
    currentYMDHMS = ciTools.currentYMDHMS()
    buildParam.buildTime = currentYMDHMS
    buildParam.taskStartTime = ciTools.getCurrentDateTimeStr1()
    buildParam.outputFileName = "{0}-{1}".format("XStudioPlugin", currentYMDHMS)
    buildParam.fullProjectOutPutDir = os.path.join(buildParam.projectOutPutDir, buildParam.outputFileName)
    buildParam.engineName = ciTools.getFileName(buildParam.engineDir)
    buildParam.engineZipFile = os.path.join(ciTools.getPrevDirPath(buildParam.engineDir), "{}.zip".format(buildParam.engineName))
    buildParam.fullVersion = "{}.{}".format(buildParam.mainVersion, buildParam.commitVersion)
    buildParam.projectOutPutZipName = "{}-{}-{}.zip".format(buildParam.outputFileName, buildParam.branch ,buildParam.fullVersion)
    buildParam.fullProjectOutPutZipPath = os.path.join(buildParam.projectOutPutDir, buildParam.projectOutPutZipName)

def copyPlugins(buildParam: BuildPluginParam, xProjectMgr : XVerseProjectManager):
    printLog("copyPlugins start")
    for pluginName in buildParam.pluginList:
        pluginInfo = xProjectMgr.getProjectPluginInfo(pluginName)
        if pluginInfo is None or pluginInfo.path is None:
            printLog("copyPlugins error not Plugins = {}".format(pluginName))
            continue
        srcPluginDir= os.path.join(pluginInfo.path)

        destPluginDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", pluginName)
        printLog("copyPlugins star {} to {}".format(srcPluginDir, destPluginDir))
        if os.path.exists(srcPluginDir):

            ciTools.copy_dir(srcPluginDir, destPluginDir)

            pluginConfigFile = os.path.join(destPluginDir, pluginName + ".uplugin")
            if os.path.exists(pluginConfigFile):
                pjson = json.load(codecs.open(pluginConfigFile, 'r', 'utf-8-sig'))
                pjson["EnabledByDefault"] = True
                with open(pluginConfigFile, "w") as outfile:
                    json_object = json.dumps(pjson, indent=4)
                    outfile.write(json_object)
            
            xversePluginFlagFile = os.path.join(destPluginDir, "Plugin.xverse")
            if not os.path.exists(xversePluginFlagFile):
                xfile = open(xversePluginFlagFile, "a+")
                xfile.write(pluginName)
                xfile.close()
        else:
            printLog("copyPlugins error not exist Plugins = {}".format(srcPluginDir))

def deleteUnUsedFilesPlugins(buildParam: BuildPluginParam):
    printLog("deleteUnUsedFilesPlugins start")
    for pluginName in buildParam.pluginList:
        if buildParam.pluginIncludeSource == False:
            sourceDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", pluginName, "Source")
            if os.path.exists(sourceDir):
                try:
                    shutil.rmtree(sourceDir)
                except:
                    printLog("deleteUnUsedFilesPlugins remove source error {}".format(sourceDir))
        if buildParam.pluginIncludeIntermediate == False:
            intermediateDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", pluginName, "Intermediate")
            if os.path.exists(intermediateDir):
                try:
                    shutil.rmtree(intermediateDir)
                except:
                    printLog("deleteUnUsedFilesPlugins remove intermediateDir error {}".format(sourceDir))
        if buildParam.pluginIncludePdb == False:
            pdbDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", pluginName, "Binaries", "Win64", "*.pdb")
            ciTools.removeFiles(pdbDir)
def copyEngine(buildParam: BuildPluginParam):
    printLog("copyEngine start {}".format(buildParam.engineDir))
    srcDir = os.path.join(buildParam.engineDir)
    destDir = os.path.join(buildParam.fullProjectOutPutDir)
    if not os.path.exists(destDir):
        os.makedirs(destDir)
    if os.path.exists(srcDir):
        ciTools.copyDirByShutil(srcDir, destDir)

def copyConfig(buildParam: BuildPluginParam):
    printLog("copyConfig start {}".format(buildParam.fullProjectOutPutDir))
    destDirPath = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Config")
    if not os.path.exists(destDirPath):
        os.makedirs(destDirPath)
    # XConsole.ini
    destFile = os.path.join(destDirPath, "BaseXConsole.ini")
    srcFile = os.path.join(buildParam.projectSourceDir, "Config", "DefaultXConsole.ini")
    if os.path.exists(srcFile):
        # art plugin
        shutil.copyfile(srcFile, destFile)
    
    # BaseXSkinSambaCache.ini
    destFile = os.path.join(destDirPath, "BaseXSkinSambaCache.ini")
    srcFile = os.path.join(buildParam.packageToolDir, "BaseXSkinSambaCache.ini")
    if os.path.exists(srcFile):
        # art pugin no need
        # shutil.copyfile(srcFile, destFile)
        pass

def copyTools(buildParam: BuildPluginParam):
    printLog("copyTools start")
    srcDir = "\\\\CreatorSamba\\XverseCreator\lowpolyApplication"
    destDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "lowpolyApplication")
    if not os.path.exists(destDir):
        os.makedirs(destDir)
    if os.path.exists(srcDir):
        ciTools.copy_dir(srcDir, destDir)

def copyDeletePluginTool(buildParam: BuildPluginParam):
    printLog("copyDeletePluginTool start")
    destDirPath = os.path.join(buildParam.fullProjectOutPutDir, "Tools")
    if not os.path.exists(destDirPath):
        os.makedirs(destDirPath)
    destFile = os.path.join(destDirPath, "DeleteOldXVersePlugins.bat")
    srcFile = os.path.join(buildParam.packageToolDir, "..", "ExTools", "DeleteOldXVersePlugins.bat")
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)

def modifyEnginePlugins(buildParam: BuildPluginParam):
    printLog("modifyPlugins start")
    # ModelingToolsEditorMode
    destPluginDir = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "Experimental", "ModelingToolsEditorMode")
    toolPluginFilr = os.path.join(buildParam.packageToolDir, "Plugins", "ModelingToolsEditorMode.uplugin")
    if os.path.exists(destPluginDir):
        if os.path.exists(toolPluginFilr):
            shutil.copy(toolPluginFilr, destPluginDir)

def genLocalSrcVersionConfig(buildParam: BuildPluginParam):
    printLog("genLocalSrcVersionConfig start")
    if "XUE" in buildParam.pluginList:
        srcVersionDirPath = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", "XUE", "Config")
        if not os.path.exists(srcVersionDirPath):
            os.makedirs(srcVersionDirPath)
        srcVersionPath = os.path.join(srcVersionDirPath, "DefaultLocalSrcVersion.ini")
        file = open(srcVersionPath, "a+")
        file.write("[/Script/XverseAssetUploader.LocalSrcVersion]")
        file.write("\n")
        file.write("SourceVersion={}".format(buildParam.commitVersion))
        file.write("\n")
        file.write("SourceCommitId={}".format(buildParam.commitId))
        file.write("\n")

def genXVerseProjectConfig(buildParam: BuildPluginParam):
    printLog("genXVerseProjectConfig start")
    srcVersionDirPath = os.path.join(buildParam.fullProjectOutPutDir, "Engine", "Plugins", "XVerse", "XBaseLib", "Config")
    os.makedirs(srcVersionDirPath, exist_ok=True)
    srcVersionPath = os.path.join(srcVersionDirPath, "XVerseProjectConfig.ini")
    file = open(srcVersionPath, "a+")
    file.write("[/Script/ProjectItem.XVerseProjectConfigMng]")
    file.write("\n")
    file.write("ProjectName={}\n".format(buildParam.projectName))
    file.write("BuildTime={}\n".format(buildParam.buildTime))
    file.write("ProjectVersion={}\n".format(buildParam.mainVersion))
    file.write("ConsoleEnv={}\n".format(buildParam.consoleEnv))
    file.write("PackageType={}\n".format(buildParam.gameConfigurations))
    file.write("Branch={}\n".format(buildParam.branch))
    file.write("CommitId={}\n".format(buildParam.commitId))
    file.write("CommitVersion={}\n".format(buildParam.commitVersion))
    file.write("CommitInfo={}\n".format(buildParam.commitInfo))
    file.write("EnginePkgVersion={}\n".format(buildParam.engineName))
    file.write("EngineBranch={}\n".format(buildParam.engineBranch))
    file.write("EngineCommitId={}\n".format(buildParam.engineCommitId))
    file.write("EngineVersion={}\n".format(buildParam.engineCommitVersion))
    file.write("Remarks={}\n".format(buildParam.remarks))
    file.write("Executor={}\n".format(buildParam.executor))
    file.write("UProjectName={}\n".format(buildParam.projectName))
    file.write("ProjectBundleId={}\n".format("XVerse.Art.Plugin"))
    file.write("bForPlugin={}\n".format(True))
    file.close()

def genXversePluginToolsVersion(buildParam: BuildPluginParam):
    printLog("XversePluginToolsVersion start")
    versionDirPath = os.path.join(buildParam.fullProjectOutPutDir, "Version")
    if not os.path.exists(versionDirPath):
        os.makedirs(versionDirPath)
    srcVersionPath = os.path.join(versionDirPath, "XversePluginToolsVersion.ini")
    file = open(srcVersionPath, "a+")
    file.write("ProjectName={}\n".format(buildParam.projectName))
    file.write("BuildTime={}\n".format(buildParam.buildTime))
    file.write("EnginePkgVersion={}\n".format(buildParam.engineName))
    file.write("EngineBranch={}\n".format(buildParam.engineBranch))
    file.write("EngineCommitId={}\n".format(buildParam.engineCommitId))
    file.write("EngineCommitInfo={}\n".format(buildParam.engineCommitInfo))
    file.write("Branch={}\n".format(buildParam.branch))
    file.write("CommitId={}\n".format(buildParam.commitId))
    file.write("CommitVersion={}\n".format(buildParam.commitVersion))
    file.write("CommitInfo={}\n".format(buildParam.commitInfo))
    file.write("Remarks={}\n".format(buildParam.remarks))
    file.write("Executor={}\n".format(buildParam.executor))
    file.close()

def compressPlugins(buildParam : BuildPluginParam):
    printLog("compressPlugins " + buildParam.fullProjectOutPutDir)
    os.chdir(buildParam.projectOutPutDir)
    script = "bz c {0}".format(buildParam.projectOutPutZipName)
    files = os.listdir(buildParam.fullProjectOutPutDir)
    for f in files:
        path = os.path.join(buildParam.fullProjectOutPutDir, f)
        script += " {0}".format(path)
    printLog("script=" + script)
    os.system(script)

def uploadToSamba(buildParam : BuildPluginParam):
    branchName = buildParam.branch.replace("/", "_")
    buildParam.projectSambaDir = "\\\\CreatorSamba\\XverseCreator\\{}\\{}-Incr\\{}-{}".format(buildParam.projectName, buildParam.projectDescribeName, buildParam.engineName, branchName)
    if buildParam.includeEngine == True:
        buildParam.projectSambaDir = "\\\\CreatorSamba\\XverseCreator\\{}\\{}-Full\\{}-{}".format(buildParam.projectName, buildParam.projectDescribeName, buildParam.engineName, branchName)
    ciTools.uploadToSamba(buildParam.projectSambaDir, buildParam.fullProjectOutPutZipPath)

def createArtPluginProject(buildParam: BuildPluginParam, xProjectManager : XVerseProjectManager):
    artProjectName = buildParam.pluginProjectName
    printLog("createArtPluginProject {}".format(artProjectName))
    projectGenerateUtil.removeOldProject(buildParam.projectSourceDir, artProjectName)
    projectGenerateUtil.generateProjectUProject(buildParam.projectSourceDir, artProjectName, inEnablePlugins=buildParam.pluginList, InEnableAutoGenDeps=True, inXProjectMgr=xProjectManager)
    projectGenerateUtil.generateProjectSource(buildParam.projectSourceDir, artProjectName)

def getProjectInfo(buildParam: BuildPluginParam, xProjectManager : XVerseProjectManager):
    printLog("getProjectInfo start %d, %s"%(len(buildParam.pluginList), buildParam.pluginList))
    enablePlugins = projectGenerateUtil.getProjectPlugins(buildParam.projectSourceDir,buildParam.pluginProjectName, True)
    disablePlugins = projectGenerateUtil.getProjectPlugins(buildParam.projectSourceDir,buildParam.pluginProjectName, False)
    buildParam.enablePluginList = enablePlugins
    buildParam.disablePluginList = disablePlugins
    bakPluginList = buildParam.pluginList.copy()
    #remove engine plugin
    for name in buildParam.enablePluginList:
        if name not in buildParam.pluginList:
            pluginInfo = xProjectManager.getProjectPluginInfo(name)
            if pluginInfo is not None:
                buildParam.pluginList.append(name)
    subList = [item for item in buildParam.pluginList if item not in bakPluginList]
    printLog("getProjectInfo end subList %d, %s"%(len(subList), subList))
    printLog("getProjectInfo end total plugins %d, %s"%(len(buildParam.pluginList), buildParam.pluginList))
    
def buildPluginsByProject(buildParam: BuildPluginParam):
    printLog("buildPluginsByProject {} ".format(buildParam.pluginProjectName))
    bResult = buildPluginsInner(buildParam)
    return bResult

def buildPluginsInner(buildParam: BuildPluginParam, clientconfig = "Development"):
    printLog("buildPlugins {},clientconfig={} ".format(buildParam.pluginProjectName, clientconfig))
    projectPath = "{0}\{1}.uproject".format(buildParam.projectSourceDir, buildParam.pluginProjectName)
    exeName = "UE4Editor-Cmd.exe"
    if buildParam.engineMajorVersion >= 5:
        exeName = "UnrealEditor-Cmd.exe"
    unralexe = os.path.join(buildParam.engineDir,"Engine", "Binaries", "Win64", exeName)
    ueRunUat = os.path.join(buildParam.engineDir, "Engine", "Build", "BatchFiles", "RunUAT.bat")
    buildCmdFormat1 = "{0} BuildCookRun -ScriptsForProject={1} -noP4 -project={2}".format(ueRunUat, projectPath, projectPath)
    buildCmdFormat2 = "-ue4exe={0}".format(unralexe)
    if buildParam.engineMajorVersion >= 5:
        buildCmdFormat2 = "-unrealexe={0}".format(unralexe)
    buildCmdFormat3 = "-build -targetplatform={0} -clientconfig={1} -utf8output".format(buildParam.targetPlatform, clientconfig)
    buildCmd = "{0} {1} {2}".format(buildCmdFormat1, buildCmdFormat2, buildCmdFormat3)
    printLog("buildPlugins buildCmd script {}".format(buildCmd))
    bResult, msg = ciTools.runScript(buildCmd)
    return bResult

def printLog(msg):
    ciTools.printLogTag(LogTag, msg)

def buildPluginMain(buildParam : BuildPluginParam):
    ciTools.updateBuildParams(buildParam)
    ciTools.updataParamsAfterUserSetting(buildParam)
    hasUbtRunning = ciTools.checkUBTProcess()
    if hasUbtRunning:
        buildParam.buildResultCode = 1
        buildParam.buildResultMsg = "UBT had already running, pls try later"
        ciTools.sendBuildResultMessage(buildParam)
        ciTools.stopProcess(buildParam.buildResultMsg)

    if buildParam.projectOutPutDir is None or len(buildParam.projectOutPutDir) < 1:
        buildParam.projectOutPutDir = os.path.join(buildParam.projectSourceDir, "PluginOutPut")

    if (buildParam.engineDir is None or len(buildParam.engineDir) < 1) and len(buildParam.engineSearchDir) > 0:
        if buildParam.engineForTest == True:
            buildParam.engineSearchDir = ciTools.XCiGlobalConfig.engineOutPutDirForTest
        buildParam.engineDir = BuildHelper.searchEngineDir(buildParam.engineSearchDir, buildParam.engineBranch)
    ciTools.printBuildParam(buildParam, "after updateBuildParams")

    if buildParam.embedGitClean == True:
        ciTools.gitClean(buildParam.projectSourceDir)
    if buildParam.forceResetLocalBranch == True:
        ciTools.resetProjectBranch(buildParam.projectSourceDir)
    if buildParam.embedSwitchBranch == True:
        result, msg = ciTools.switchBranch(buildParam.projectSourceDir, buildParam.branch)
        if result == False:
            buildParam.buildResultMsg = "{} switch branch error".format(buildParam.projectSourceDir)
            ciTools.sendBuildResultMessage(buildParam)
            ciTools.stopProcess("switch branch error")
    if buildParam.embedPullLatestCode == True:        
        result = ciTools.pullBranch(buildParam.projectSourceDir)
        if result == False:
            buildParam.buildResultMsg = "{} pull code error".format(buildParam.projectSourceDir)
            ciTools.sendBuildResultMessage(buildParam)
            ciTools.stopProcess("pull error")
    ciTools.printBranchLog(buildParam.projectSourceDir)
    if  len(buildParam.engineDir) < 1 or not os.path.exists(buildParam.engineDir):
        buildParam.buildResultMsg = "engine not exist searchDir={}, engineDir={}".format(buildParam.engineSearchDir, buildParam.engineDir)
        ciTools.sendBuildResultMessage(buildParam)
        ciTools.stopProcess("engine not exist searchDir={}, engineDir={}".format(buildParam.engineSearchDir, buildParam.engineDir))
    ciTools.getEngineCommitInfo(buildParam, buildParam.engineDir, buildParam.engineBranch)
    ciTools.getProjectCommitInfo(buildParam)
    settingConfigs(buildParam)
    # init xverseProjectManager
    xProjectMgr = XVerseProjectManager()
    xProjectMgr.init(buildParam.projectSourceDir)
    ciTools.printBuildParam(buildParam, "GetCommitInfo End")
    createArtPluginProject(buildParam, xProjectMgr)
    getProjectInfo(buildParam, xProjectMgr)
    #ciTools.stopProcess("", withFail=False)
    buildRet = buildPluginsByProject(buildParam)
    if buildRet == False:
        buildParam.buildResultCode = 21
        buildParam.buildResultMsg = "build plugin error"
        ciTools.sendBuildResultMessage(buildParam)
        ciTools.stopProcess(buildParam.buildResultMsg)
    copyPlugins(buildParam, xProjectMgr)
    copyConfig(buildParam)
    copyDeletePluginTool(buildParam)
    genXversePluginToolsVersion(buildParam)
    genLocalSrcVersionConfig(buildParam)
    genXVerseProjectConfig(buildParam)
    deleteUnUsedFilesPlugins(buildParam)
    compressPlugins(buildParam)
    if buildParam.embedUploadSamba:
        uploadToSamba(buildParam)
    buildParam.buildResultCode = 0
    buildParam.buildResultMsg = "OK"
    ciTools.sendBuildResultMessage(buildParam)
if __name__ == "__main__":
    #sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    #sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    cwd = os.getcwd()
    printLog("buildplugins cwd=" + cwd)
    BinPath = sys.argv[0]
    printLog("BinPath=" + BinPath)
    buildParam = BuildPluginParam()
    readParams(sys.argv, buildParam)
    if buildParam.embedLocalTest:
        buildParam.embedGitClean = False
        buildParam.embedCleanOldCache = False
        buildParam.embedSwitchBranch = False
        buildParam.embedPullLatestCode = False
        buildParam.forceResetLocalBranch = False
        buildParam.pluginIncludeShipping = False
        buildParam.embedUploadSamba = False
        buildParam.embedUploadSymbol = False
        buildParam.targetPlatform = "Win64"
        buildParam.gameConfigurations = "Development"
        buildParam.engineBranch = "xstudio-ue5.2"
        buildParam.branch = "dev-ue5"
        buildParam.projectSourceDir = "C:\\work\\xverse\\XVerseStudio5.2"
        buildParam.projectOutPutDir = "C:\\work\\xverse\\ArtProject5OutPut"
        buildParam.projectSambaDir = "\\\\CreatorSamba\\XverseCreator\\XCreatorProject"
        buildParam.engineSearchDir = "C:\\work\\xverse\\ArtEngine5OutPut"
        buildParam.engineMajorVersion = 5
    ciTools.printBuildParam(buildParam, "after readParams")
    buildPluginRet = buildPluginMain(buildParam)
    printLog("buildPluginRet {}".format(buildPluginRet))