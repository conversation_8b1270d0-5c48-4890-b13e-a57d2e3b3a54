#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import os
from pathlib import Path
import subprocess
import json
import codecs
from  ciTools import BuildProjectParam
import ciTools
import cosUtil
import BuildHelper
import shutil
import io
import copy
import projectGenerateUtil
from xverseUtil import IniConfigHelper
from xverseUtil import UProjectHelper
from resourceHelper import DownloadAssetMgr
from resourceHelper import ResouceDownlaodParam
from assetMgr import AssetMgr, getFileSize
import configparser
import orjson
from apscheduler.schedulers.background import BackgroundScheduler
import time
import requests
import glob
import tempfile
import pickle
from otaUploader import OTAStorage
from requests_toolbelt import MultipartEncoder
from datetime import datetime
import rtoml
import re


LogTag = "BuildProject"

locateTypeMap = {"锚点定位":0, "自研Aruco定位": 1, "基于安全边界定位": 2}


def readParams(argv, buildParam: BuildProjectParam):
    ciTools.readBuildParams(argv, buildParam)

#config project info after switch branch and before compile
def getBuildOutputDirInfo(buildParam: BuildProjectParam):

    buildParam.outputFileName = "{0}-{1}".format(buildParam.projectName, buildParam.buildTime)
    buildParam.fullProjectOutPutDir = os.path.join(buildParam.projectOutPutDir, buildParam.outputFileName)
    buildParam.projectOutPutZipName = "{}.zip".format(buildParam.outputFileName)
    buildParam.fullProjectOutPutZipPath = os.path.join(buildParam.projectOutPutDir, buildParam.projectOutPutZipName)

def getProjectUploadPath(buildParam: BuildProjectParam):
    ciTools.setProjectUploadPath(buildParam)


def getProjectBuildInfo(buildParam: BuildProjectParam):
    lbvrProjectConfigPath = Path(os.path.join(buildParam.packageToolDir, "config", "LBVRProject.toml"))
    if not lbvrProjectConfigPath.exists():
        buildParam.bForceClearAllLocalCache = True
        buildParam.bUseSharedDDC = False
        return
    
    allLBVRProjectInfo = rtoml.load(lbvrProjectConfigPath)
    
    matched = False
    for name in allLBVRProjectInfo['projectName']:
        if re.search(name, buildParam.branch, re.I) or (buildParam.branch == 'feat/lbvr' and buildParam.packageName and len(buildParam.packageName) > 0 and re.search(name, buildParam.packageName, re.I)):
            buildParam.projectSourceDir = allLBVRProjectInfo[name]['sourceDir']
            matched = True
            buildParam.lastBuildEngineInfo = f"{allLBVRProjectInfo[name]['lastBuildEngineBranch']}_{allLBVRProjectInfo[name]['lastBuildEngineCommitId']}"
            buildParam.sharedDDCPathPrefix = allLBVRProjectInfo[name]['sharedDDCPathPrefix']
            buildParam.lbvrProjectName = name
            break
    if not matched:
        buildParam.bUseSharedDDC = False
        buildParam.projectSourceDir = allLBVRProjectInfo['default']['sourceDir']
    
    return


def updateProjectBuildInfo(buildParam: BuildProjectParam):
    lbvrProjectConfigPath = Path(os.path.join(buildParam.packageToolDir, "config", "LBVRProject.toml"))
    if lbvrProjectConfigPath.exists():
        allLBVRProjectInfo = rtoml.load(lbvrProjectConfigPath)
        if buildParam.lbvrProjectName:
            allLBVRProjectInfo[buildParam.lbvrProjectName]['lastBuildEngineBranch'] = buildParam.engineBranch
            allLBVRProjectInfo[buildParam.lbvrProjectName]['lastBuildEngineCommitId'] = buildParam.engineCommitId
            rtoml.dump(allLBVRProjectInfo, lbvrProjectConfigPath)


def fileFilter(dstPath, fileList, parent):
    filteredList = []
    targetPath = Path(dstPath) / parent if parent != "" else Path(dstPath)
    if not os.path.exists(targetPath):
        raise Exception(f"{targetPath}不存在")
    for f in fileList:
        fPath = targetPath / f
        if not os.path.exists(fPath):
            raise Exception(f"{fPath}不存在")
        
    if parent == "":
        for p in Path(dstPath).iterdir():
            if p.name not in fileList:
                filteredList.append(p.absolute())
    else:
        if len(fileList) > 0:
            for p in targetPath.iterdir():
                if p.name not in fileList:
                    filteredList.append(p.absolute())
        for p in Path(dstPath).rglob("*"):
            if p.is_file():
                pp = p.parent
                absp = p.absolute()
                if not pp.match(str(targetPath)) and absp not in filteredList:
                    filteredList.append(absp)
                # elif pp.match(dstPath) and p not in filteredList:
                #     filteredList.append(p.absolute())
            # else:
            #     if not p.match(str(targetPath)) and p not in filteredList:
            #         filteredList.append(p.absolute())
    
    for fp in filteredList:
        print(f"remove {fp}")
        if fp.is_file():
            fp.unlink()
        else:
            shutil.rmtree(fp)
            

def enableSharedDDC(buildParam: BuildProjectParam):
    buildParam.sharedDDCPath = buildParam.sharedDDCPath.replace("/","\\")
    ddcConfigPath = os.path.join(buildParam.projectSourceDir, "Config", "DefaultEngine.ini")
    ddcConfig = IniConfigHelper()
    ddcConfig.read(ddcConfigPath)

    ddcConfig.remove('DerivedDataBackendGraph', 'Boot')
    ddcConfig.remove('DerivedDataBackendGraph', 'Local')
    ddcConfig.remove('DerivedDataBackendGraph', 'Shared')
    ddcConfig.remove('InstalledDerivedDataBackendGraph', 'Boot')
    ddcConfig.remove('InstalledDerivedDataBackendGraph', 'Local')
    ddcConfig.remove('InstalledDerivedDataBackendGraph', 'Shared')

    ddcConfig.set('DerivedDataBackendGraph', 'MinimumDaysToKeepFile', 7)
    ddcConfig.set('DerivedDataBackendGraph', 'Root', '(Type=KeyLength, Length=166, Inner=AsyncPut)')
    ddcConfig.set('DerivedDataBackendGraph', 'AsyncPut', '(Type=AsyncPut, Inner=Hierarchy)')
    ddcConfig.set('DerivedDataBackendGraph', 'Hierarchy', '(Type=Hierarchical, Inner=Local, Inner=Shared)')
    # ddcConfig.set('DerivedDataBackendGraph', 'Boot', '(Type=Boot, Filename="%GAMEDIR%DerivedDataCache/Boot.ddc", MaxCacheSize=512)')
    ddcConfig.set('DerivedDataBackendGraph', 'Shared', f'(Type=FileSystem, UnusedFileAge=90, PromptIfMissing=true, ConsiderSlowAt=35, Path={buildParam.sharedDDCPath}, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)')
    ddcConfig.set('DerivedDataBackendGraph', 'Local', '(Type=FileSystem, UnusedFileAge=31, Path="%GAMEDIR%//DerivedDataCache", EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache)')
    

    ddcConfig.set('DerivedDataBackendGraph', 'MinimumDaysToKeepFile', 7)
    ddcConfig.set('InstalledDerivedDataBackendGraph', 'Root', '(Type=KeyLength, Length=166, Inner=AsyncPut)')
    ddcConfig.set('InstalledDerivedDataBackendGraph', 'AsyncPut', '(Type=AsyncPut, Inner=Hierarchy)')
    ddcConfig.set('InstalledDerivedDataBackendGraph', 'Hierarchy', '(Type=Hierarchical, Inner=Local, Inner=Shared)')
    # ddcConfig.set('InstalledDerivedDataBackendGraph', 'Boot', '(Type=Boot, Filename="%GAMEDIR%DerivedDataCache/Boot.ddc", MaxCacheSize=512)')
    ddcConfig.set('InstalledDerivedDataBackendGraph', 'Shared', f'(Type=FileSystem, UnusedFileAge=90, PromptIfMissing=true, ConsiderSlowAt=35, Path={buildParam.sharedDDCPath}, EnvPathOverride=UE-SharedDataCachePath, EditorOverrideSetting=SharedDerivedDataCache, CommandLineOverride=SharedDataCachePath)')
    ddcConfig.set('InstalledDerivedDataBackendGraph', 'Local', '(Type=FileSystem, UnusedFileAge=31, Path="%GAMEDIR%//DerivedDataCache", EnvPathOverride=UE-LocalDataCachePath, EditorOverrideSetting=LocalDerivedDataCache)')
    ddcConfig.write(ddcConfigPath)


def setEngineInfo(buildParam: BuildProjectParam):
    buildParam.engineName = ciTools.getFileName(buildParam.engineDir)
    buildParam.engineZipFile = os.path.join(ciTools.getPrevDirPath(buildParam.engineDir), "{}.zip".format(buildParam.engineName))

def createProject(buildParam: BuildProjectParam):
    printLog("createProject {},projectWithGen={} ".format(buildParam.projectName, buildParam.createBuildProjectWithGen))
    if buildParam.createBuildProjectWithGen == True:
        return createBuildProjectWithGenerate(buildParam)
    else:
        return createBuildProjectWithCopy(buildParam)
def createBuildProjectWithGenerate(buildParam: BuildProjectParam):
    printLog("createBuildProjectWithGenerate {} ".format(buildParam.projectName))
    if buildParam.bVRAndroidCi == True:
        projectGenerateUtil.removeOldProject(buildParam.projectSourceDir, buildParam.projectName, withConfig=False)
        projectGenerateUtil.generateProjectUProject(buildParam.projectSourceDir, buildParam.projectName, InEnableAutoGenDeps=True)
        projectGenerateUtil.generateProjectSource(buildParam.projectSourceDir, buildParam.projectName, True)
        projectGenerateUtil.copyProjectBuild(buildParam.projectSourceDir, buildParam.projectName)
        return True
    projectGenerateUtil.removeOldProject(buildParam.projectSourceDir, buildParam.projectName)
    projectGenerateUtil.generateProjectUProject(buildParam.projectSourceDir, buildParam.projectName, InEnableAutoGenDeps=True)
    projectGenerateUtil.generateProjectSource(buildParam.projectSourceDir, buildParam.projectName, True)
    projectGenerateUtil.copyProjectConfig(buildParam.projectSourceDir, buildParam.projectName)
    projectGenerateUtil.copyProjectBuild(buildParam.projectSourceDir, buildParam.projectName)
    return True
def createBuildProjectWithCopy(buildParam: BuildProjectParam):
    printLog("createBuildProjectWithCopy {} ".format(buildParam.projectName))
    # remove old project files
    # projectGenerateUtil.removeOldProject(buildParam.projectSourceDir, buildParam.projectName)
    # copy new project
    if buildParam.deviceType == 'pico':
        projectGenerateUtil.copyUProjectFromScript(buildParam.projectSourceDir, "XVerseVR_Pico")
    else:
        projectGenerateUtil.copyUProjectFromScript(buildParam.projectSourceDir, buildParam.projectName)
    return True

def downloadAssetsWithEditorCmd(buildParam: BuildProjectParam, pathFile, version):
    printLog("downloadAssetsWithEditorCmd {}".format(buildParam.projectName))
    exeName = "UE4Editor-Cmd.exe"
    if buildParam.engineMajorVersion >= 5:
        exeName = "UnrealEditor-Cmd.exe"
    unrealexe = os.path.join(buildParam.engineDir,"Engine", "Binaries", "Win64", exeName)
    projectExe = os.path.join(buildParam.projectSourceDir, "{}.uproject".format(buildParam.projectName))
    param = " --- z.Res.DownloadAssetsWithVersion @{} {} --- z.PipelineExit".format(pathFile, version)
    buildCmd = "{0} {1} -skipcompile {2}".format(unrealexe, projectExe, param)
    printLog("downloadAssetsWithEditorCmd buildCmd script {}".format(buildCmd))
    bResult, msg = ciTools.runScript(buildCmd)
    return bResult

def buildProjectNoArchiveAnNoCook(buildParam: BuildProjectParam):
    printLog("buildProjectNoArchiveAnNoCook {}".format(buildParam.projectName))
    buildInfo = copy.deepcopy(buildParam)
    buildInfo.embedProjectArchive = False
    buildInfo.embedProjectCook = False
    buildInfo.embedProjectPackage = False
    buildInfo.embedProjectPak = False
    buildInfo.embedProjectStage = False
    buildInfo.embedProjectDDC = False
    return buildAndCookProject(buildInfo)

def buildAndCookProject(buildParam: BuildProjectParam):
    printLog("buildProject {} ".format(buildParam.projectName))
    projectPath = "{0}\{1}.uproject".format(buildParam.projectSourceDir, buildParam.projectName)
    exeName = "UE4Editor-Cmd.exe"
    if buildParam.engineMajorVersion >= 5:
        exeName = "UnrealEditor-Cmd.exe"
    unrealExe = os.path.join(buildParam.engineDir,"Engine", "Binaries", "Win64", exeName)
    runUat = os.path.join(buildParam.engineDir, "Engine", "Build", "BatchFiles", "RunUAT.bat")
    buildCmdFormat1 = "{0} BuildCookRun -ScriptsForProject={1} -installed -noP4 -project={2}".format(runUat, projectPath, projectPath)
    
    buildCmdFormat2 = "-ue4exe={0}".format(unrealExe)
    if buildParam.engineMajorVersion >= 5:
        buildCmdFormat2 = "-unrealexe={0}".format(unrealExe)

    if buildParam.embedProjectCook:
        buildCmdFormat2 += " -cook"
    if buildParam.embedProjectStage:
        buildCmdFormat2 += " -stage"
    if buildParam.embedProjectArchive:
        buildCmdFormat2 += " -archive -archivedirectory={0}".format(buildParam.fullProjectOutPutDir)
    if buildParam.embedProjectPackage:
        buildCmdFormat2 += " -package"
    if buildParam.embedProjectDDC:
        buildCmdFormat2 += " -ddc=InstalledDerivedDataBackendGraph"
    if buildParam.embedProjectPak:
        buildCmdFormat2 += " -pak"

    buildCmdFormat3 = "-build -targetplatform={0} -clientconfig={1} -utf8output".format(buildParam.targetPlatform, buildParam.gameConfigurations)

    buildCmdExt = ""
    if buildParam.targetPlatform.startswith("Android"):
        buildCmdExt = "-cookflavor={0}".format(buildParam.cookflavor)
    if buildParam.includeCrashReporter == True:
        buildCmdExt += " -CrashReporter"
    if buildParam.includeDebugFiles == False:
        buildCmdExt += " -nodebuginfo"
    if buildParam.includePrerequisites == True:
        buildCmdExt += " -prereqs"
    if buildParam.distribution == True:
        buildCmdExt += " -distribution"
    if buildParam.useDeploy == True:
        buildCmdExt += " -deploy"
    if buildParam.compressed == True:
        buildCmdExt += " -compressed"
    buildCmd = "{0} {1} {2} {3}".format(buildCmdFormat1, buildCmdFormat2, buildCmdFormat3, buildCmdExt.lstrip())
    printLog("buildProject buildCmd script {}".format(buildCmd))
    bResult, msg = ciTools.runScript(buildCmd)
    if not bResult:
        buildParam.buildResultMsg = msg
        return False
    if buildParam.embedProjectArchive == True and os.path.exists(buildParam.fullProjectOutPutDir) and bResult == True:
        return True
    elif buildParam.embedProjectArchive == True and not os.path.exists(buildParam.fullProjectOutPutDir) and bResult == True:
        buildParam.buildResultMsg = "编译失败"
        return False
    return bResult

def cookProject(buildParam: BuildProjectParam):
    printLog("cookProject {} ".format(buildParam.projectName))
    projectPath = "{0}\{1}.uproject".format(buildParam.projectSourceDir, buildParam.projectName)
    exeName = "UE4Editor-Cmd.exe"
    if buildParam.engineMajorVersion >= 5:
        exeName = "UnrealEditor-Cmd.exe"
    unrealExe = os.path.join(buildParam.engineDir,"Engine", "Binaries", "Win64", exeName)
    runUat = os.path.join(buildParam.engineDir, "Engine", "Build", "BatchFiles", "RunUAT.bat")
    cookCmdFormat1 = "{0} BuildCookRun -ScriptsForProject={1} -installed -noP4 -project={2}".format(runUat, projectPath, projectPath)
    
    cookCmdFormat2 = "-ue4exe={0}".format(unrealExe)
    if buildParam.engineMajorVersion >= 5:
        cookCmdFormat2 = "-unrealexe={0}".format(unrealExe)

    if buildParam.embedProjectDDC:
        cookCmdFormat2 += " -ddc=InstalledDerivedDataBackendGraph"
    
    cookCmdFormat2 += " -NoShaderDDC -NoDev"

    cookCmdFormat3 = "-cook -targetplatform={0} -clientconfig={1} -utf8output".format(buildParam.targetPlatform, buildParam.gameConfigurations)

    cookCmdExt = ""
    if buildParam.targetPlatform.startswith("Android"):
        cookCmdExt = "-cookflavor={0}".format(buildParam.cookflavor)
    if buildParam.includeCrashReporter == True:
        cookCmdExt += " -CrashReporter"
    if buildParam.includeDebugFiles == False:
        cookCmdExt += " -nodebuginfo"
    
    cookCmd = "{0} {1} {2} {3}".format(cookCmdFormat1, cookCmdFormat2, cookCmdFormat3, cookCmdExt.lstrip())
    printLog("cookProject cookCmd script {}".format(cookCmd))
    bResult, msg = ciTools.runScript(cookCmd)
    return bResult

#after build
def copyConsoleConfig(buildParam: BuildProjectParam):
    printLog("copyConsoleConfig {} ".format(buildParam.projectName))
    if buildParam.includeConsoleConfig == True:
        plfName = buildParam.genericPlatformName
        destDirPath = os.path.join(buildParam.fullProjectOutPutDir, plfName, buildParam.projectName, "Saved", "Config", plfName)
        if buildParam.targetPlatform.startswith("Linux"):
            destDirPath = os.path.join(buildParam.fullProjectOutPutDir, plfName, buildParam.projectName, "Saved", "Config", plfName)
        if not os.path.exists(destDirPath):
            os.makedirs(destDirPath)

        destFile = os.path.join(destDirPath, "XConsole.ini")
        srcFile = os.path.join(buildParam.packageToolDir, "BaseXConsole_Dev.ini")
        if buildParam.consoleEnv.find("Release") != -1:
            srcFile = os.path.join(buildParam.packageToolDir, "BaseXConsole.ini")
        if os.path.exists(srcFile):
            shutil.copyfile(srcFile, destFile)
    
#after build
def copyEngineVersionFile(buildParam : BuildProjectParam):
    printLog("copyEngineVersionFile {} ".format(buildParam.projectName))
    destDirPath = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, buildParam.projectName, "Saved")
    if buildParam.targetPlatform.startswith("Linux"):
        destDirPath = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, buildParam.projectName, "Saved")
    if not os.path.exists(destDirPath):
        os.makedirs(destDirPath)
    destFile = os.path.join(destDirPath, "EngineVersion.ini")
    srcFile = os.path.join(buildParam.engineDir, "Engine", "Version", "EngineVersion.ini")
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)

    
#after build
def copyXBatchEditWhiteList(buildParam : BuildProjectParam):
    printLog("copyXBatchEditWhiteList {} ".format(buildParam.projectName))
    destDirPath = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, buildParam.projectName, "Saved", "Config", buildParam.genericPlatformName)
    if buildParam.targetPlatform.startswith("Linux"):
        destDirPath = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, buildParam.projectName, "Saved", "Config", buildParam.genericPlatformName)
    if not os.path.exists(destDirPath):
        os.makedirs(destDirPath)
    destFile = os.path.join(destDirPath, "RumtimeXBatchEditWhiteList.ini")
    srcFile = os.path.join(buildParam.projectSourceDir, "Config", "DefaultXBatchEditWhiteList.ini")
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)

def copyAndroidSo(buildParam : BuildProjectParam):
    androidBuildDir = os.path.join(buildParam.projectSourceDir, "Binaries", "Android")
    arm64FileName = "{}-arm64.so".format(buildParam.projectName)
    armv7FileName = "{}-armv7.so".format(buildParam.projectName)
    arm64SoPath = os.path.join(androidBuildDir, arm64FileName)
    armv7SoPath = os.path.join(androidBuildDir, armv7FileName)

    androidOutDir = os.path.join(buildParam.fullProjectOutPutDir, "Android_{}".format(buildParam.cookflavor))
    arm64SoDestPath = os.path.join(androidOutDir, arm64FileName)
    armv7SoDestPath = os.path.join(androidOutDir, armv7FileName)
    if os.path.exists(arm64SoPath):
        shutil.copyfile(arm64SoPath, arm64SoDestPath)
    if os.path.exists(armv7SoPath):
        shutil.copyfile(armv7SoPath, armv7SoDestPath)

# after build
def copyCaptureSh(buildParam : BuildProjectParam):
    srcFile = os.path.join(buildParam.packageToolDir, "XVerseCreator.sh")
    destFile = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, "XVerseCreator.sh")
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)

#after build
def copyLaunchBat(buildParam : BuildProjectParam):
    destFile = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, "MountSambaCache.bat")
    srcFile = os.path.join(buildParam.packageToolDir, "MountSambaCache.bat")
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)

    fileName = "Start{}.bat".format(buildParam.projectName)
    destFile = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, fileName)
    srcFile = os.path.join(buildParam.packageToolDir, fileName)
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)
def copyRuntimeDLL(buildParam : BuildProjectParam):
    printLog("copyRuntimeDLL %s, %s"%(buildParam.projectName, buildParam.targetPlatform))
    src = os.path.join(buildParam.projectSourceDir, "PackageLibs", buildParam.targetPlatform)
    if os.path.exists(src):
        dest = None
        if buildParam.targetPlatform.find("Win64") >= 0:
            dest = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, buildParam.projectName, "Binaries", "Win64")
        if dest is not None:
            if not os.path.exists(dest):
                os.makedirs(dest)
            printLog("copyRuntimeDLL start copy from %s to %s"%(src, dest))
            ciTools.copy_dir(src, dest)
        else:
            printLog("copyRuntimeDLL end 1 : dest is not %s"%buildParam.targetPlatform)
    else:
        printLog("copyRuntimeDLL end 2 : src not exist%s"%src)

#after build
def copyXVerseProjectConfig(buildParam : BuildProjectParam):
    destDirPath = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, buildParam.projectName, "Saved")
    if not os.path.exists(destDirPath):
        os.makedirs(destDirPath)
    destFile = os.path.join(destDirPath, "XVerseProjectConfig.ini")
    srcFile = os.path.join(buildParam.projectSourceDir, "Plugins", "XBaseLib", "Config", "XVerseProjectConfig.ini")
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)
#after build
def genLocalSrcVersionConfig(buildParam: BuildProjectParam):
    printLog("genLocalSrcVersionConfig start")
    destDirPath = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, buildParam.projectName, "Saved", "Config", buildParam.genericPlatformName)
    if not os.path.exists(destDirPath):
        os.makedirs(destDirPath)

    destFilePath = os.path.join(destDirPath, "DefaultLocalSrcVersion.ini")
    file = open(destFilePath, "a+", encoding="utf-8")
    file.write("[/Script/XverseAssetUploader.LocalSrcVersion]")
    file.write("\n")
    file.write("SourceVersion={}".format(buildParam.commitVersion))
    file.write("\n")
    file.write("SourceCommitId={}".format(buildParam.commitId))
    file.write("\n")
    #s


#after build
def genXVerseProject(buildParam: BuildProjectParam):
    printLog("genXVerseProject start")
    destDirPath = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, buildParam.projectName, "Saved", "Config", buildParam.genericPlatformName)
    if not os.path.exists(destDirPath):
        os.makedirs(destDirPath)
    destFilePath = os.path.join(destDirPath, "XVerseProject.ini")
    file = open(destFilePath, "a+", encoding="utf-8")
    file.write("[/Script/ProjectItem.XVProjectManager]")
    file.write("\n")
    file.write("Version={}-{}\n".format(buildParam.branch, buildParam.fullVersion))
    file.write("InnerVersion={}\n".format(buildParam.consoleEnv))
    file.write("ProjectCommitId={}\n".format(buildParam.commitId))
    file.write("IdePath={}\n".format(buildParam.xCaptureCosPath))
    file.write("CapturePath={}\n".format(buildParam.xCaptureCosPath))
    file.close()

#after build
def genXCreatorVersion(buildParam: BuildProjectParam):
    printLog("genXCreatorVersion start")
    destDirPath = os.path.join(buildParam.fullProjectOutPutDir, buildParam.genericPlatformName, buildParam.projectName, "Saved")
    if not os.path.exists(destDirPath):
        os.makedirs(destDirPath)
    destFilePath = os.path.join(destDirPath, "XCreatorVersion.ini")
    file = open(destFilePath, "a+", encoding="utf-8")
    file.write("Engine={}-{}\n".format(buildParam.engineCommitId,buildParam.engineCommitVersion))
    file.write("Game={}-{}\n".format(buildParam.branch,buildParam.fullVersion))
    file.write("ProjectCommitId={}\n".format(buildParam.commitId))
    file.close()

#before build
def genXVerseProjectConfig(buildParam: BuildProjectParam):
    printLog("genXVerseProjectConfig start {}".format(buildParam.projectName))
    srcVersionDirPath = os.path.join(buildParam.projectSourceDir, "Plugins", "XBaseLib", "Config")
    os.makedirs(srcVersionDirPath, exist_ok=True)
    destFilePath = os.path.join(srcVersionDirPath, "XVerseProjectConfig.ini")
    if os.path.exists(destFilePath):
        os.remove(destFilePath)
    file = open(destFilePath, "a+" , encoding="utf-8")
    file.write("[/Script/ProjectItem.XVerseProjectConfigMng]")
    file.write("\n")
    file.write("ProjectName={}\n".format(buildParam.projectName))
    file.write("BuildTime={}\n".format(buildParam.buildTime))
    file.write("ProjectVersion={}\n".format(buildParam.mainVersion))
    file.write("ConsoleEnv={}\n".format(buildParam.consoleEnv))
    file.write("PackageType={}\n".format(buildParam.gameConfigurations))
    file.write("Branch={}\n".format(buildParam.branch))
    file.write("CommitId={}\n".format(buildParam.commitId))
    file.write("CommitVersion={}\n".format(buildParam.commitVersion))
    file.write("CommitInfo={}\n".format(buildParam.commitInfo))
    file.write("EnginePkgVersion={}\n".format(buildParam.engineName))
    file.write("EngineBranch={}\n".format(buildParam.engineBranch))
    file.write("EngineCommitId={}\n".format(buildParam.engineCommitId))
    file.write("EngineVersion={}\n".format(buildParam.engineCommitVersion))
    file.write("Remarks={}\n".format(buildParam.remarks))
    file.write("Executor={}\n".format(buildParam.executor))
    file.write("IdePath={}\n".format(buildParam.xCaptureCosPath))
    file.write("CapturePath={}\n".format(buildParam.xCaptureCosPath))
    file.write("UProjectName={}\n".format(buildParam.projectName))
    file.write("ProjectBundleId=XVerse.Game.{}\n".format(buildParam.projectName))
    file.write("bForPlugin={}\n".format(True))
    file.write("bForProject={}\n".format(True))
    file.close()

    destConfDirPath = os.path.join(buildParam.projectSourceDir, "Plugins", "XBaseDev", "Config")
    os.makedirs(destConfDirPath, exist_ok=True)
    destConfigPath = os.path.join(destConfDirPath, "XVerseProjectConfig.ini")
    shutil.copyfile(destFilePath, destConfigPath)

def renameProject(buildParam : BuildProjectParam):
    printLog("renameProject {} , engineVersion {}".format(buildParam.fullProjectOutPutDir, buildParam.engineMajorVersion))
    if buildParam.engineMajorVersion < 5:
        return False, "engine < 5 no need rename"
    if buildParam.targetPlatform == "Linux" and buildParam.projectName.startswith("XCapture"):
        linuxPath = os.path.join(buildParam.fullProjectOutPutDir, "Linux")
        linuxNoEditorPath = os.path.join(buildParam.fullProjectOutPutDir, "LinuxNoEditor")
        shutil.move(linuxPath, linuxNoEditorPath)
    elif buildParam.targetPlatform == "Win64" and buildParam.projectName.startswith("XCapture"):
        srcPath = os.path.join(buildParam.fullProjectOutPutDir, "Windows")
        destPath = os.path.join(buildParam.fullProjectOutPutDir, "WindowsNoEditor")
        shutil.move(srcPath, destPath)

def compressProject(buildParam : BuildProjectParam):
    printLog("compressProject " + buildParam.fullProjectOutPutDir)
    if os.path.exists(buildParam.fullProjectOutPutDir):
        if buildParam.bVRAndroidCi or buildParam.bVRAndroidCiForEngine:
            pureCustomApk = os.path.join(buildParam.fullProjectOutPutDir, "Android_ASTC", "Android_ASTC", "XVerseVR_Oculus-arm64.apk")
            if os.path.exists(pureCustomApk):
                shutil.copy2(pureCustomApk, os.path.join(buildParam.fullProjectOutPutDir, "Android_ASTC", "XVerseVR_Oculus-arm64.apk"))
                # shutil.move(pureCustomApk, os.path.join(buildParam.fullProjectOutPutDir, "Android_ASTC"))
                shutil.rmtree(os.path.dirname(pureCustomApk))
            time.sleep(5)
            shutil.copytree(os.path.join(buildParam.fullProjectOutPutDir, "Android_ASTC"), os.path.join(buildParam.fullProjectOutPutDir, "asset"), dirs_exist_ok=True)
            shutil.rmtree(os.path.join(buildParam.fullProjectOutPutDir, "Android_ASTC"))
        if not buildParam.bNeedApk:
            if os.path.exists(os.path.join(buildParam.fullProjectOutPutDir, "asset", "XVerseVR_Oculus-arm64.apk")):
                os.remove(os.path.join(buildParam.fullProjectOutPutDir, "asset", "XVerseVR_Oculus-arm64.apk"))
        if not buildParam.bNeedMainPak:
            if os.path.exists(os.path.join(buildParam.fullProjectOutPutDir, "asset", "xverse-Android_ASTC.pak")):
                os.remove(os.path.join(buildParam.fullProjectOutPutDir, "asset", "xverse-Android_ASTC.pak"))
        if not buildParam.bNeedLowmodelPak:
            if os.path.exists(os.path.join(buildParam.fullProjectOutPutDir, "asset", "xverse-lowmodel-Android_ASTC.pak")):
                os.remove(os.path.join(buildParam.fullProjectOutPutDir, "asset", "xverse-lowmodel-Android_ASTC.pak"))
        os.chdir(buildParam.projectOutPutDir)
        script = "bz c {0}".format(buildParam.projectOutPutZipName)
        files = os.listdir(buildParam.fullProjectOutPutDir)
        for f in files:
            # if (buildParam.bVRAndroidCi or buildParam.bVRAndroidCiForEngine) and f.endswith("bat"):
            #     continue
            path = os.path.join(buildParam.fullProjectOutPutDir, f)
            script += " {0}".format(path)
        print("compressProject script=" + script)
        os.system(script)

def uploadToSamba(buildParam : BuildProjectParam):
    if os.path.exists(buildParam.fullProjectOutPutZipPath):
        ciTools.uploadToSamba(buildParam.projectSambaDir, buildParam.fullProjectOutPutZipPath)
    else:
        printLog("uploadToSamba error not exist file {}".format(buildParam.fullProjectOutPutZipPath))

def printLog(msg):
    ciTools.printLogTag(LogTag, msg)

def getEningeInfo(buildParam : BuildProjectParam):
    if not buildParam.bVRAndroidCiForEngine:
        if buildParam.engineForTest == True:
            buildParam.engineSearchDir = ciTools.XCiGlobalConfig.engineOutPutDirForTest
        buildParam.engineDir = BuildHelper.searchEngineDir(buildParam.engineSearchDir, buildParam.engineBranch, buildParam.bLatest)
    if  len(buildParam.engineDir) < 1 or not os.path.exists(buildParam.engineDir):
        errMsg = "{} engine dir no exist".format(buildParam.engineDir)
        buildParam.buildResultMsg = errMsg
        buildParam.buildResultCode = 2
        
        return False
    engineInstalledPath = os.path.join(buildParam.engineDir, "Engine", "Build", "InstalledBuild.txt")
    if os.path.exists(engineInstalledPath):
        buildParam.engineInstalled = True
    else:
        buildParam.engineInstalled = False
    return True


# before build and after generator project
def downloadPackageAsset(buildParam : BuildProjectParam):
    printLog("downloadPackageAsset {}, {}, {}" .format(buildParam.packageWithLocalAsset, buildParam.downloadAssetWithReleaseId, buildParam.downloadAssetWithLocalCopy))
    # pengzhu
    if buildParam.bVRAndroidCi == True:
        return downloadPackageAssetWithReleaseIdV1(buildParam)

    if buildParam.packageWithLocalAsset == True and buildParam.downloadAssetWithReleaseId:
        return downloadPackageAssetWithReleaseId(buildParam)

    if buildParam.packageWithLocalAsset == True and buildParam.downloadAssetWithPathId:
        return downloadPackageAssetWithPathId(buildParam)

    if buildParam.packageWithLocalAsset == True and buildParam.downloadAssetWithLocalCopy:
        return downloadPackageAssetWithLocalCopy(buildParam)
    return True

def downloadPackageAssetWithLocalCopy(buildParam : BuildProjectParam):
    printLog("downloadPackageAssetWithLocalCopy {}" .format(buildParam.packageToolDir))
    pBranch = buildParam.branch.replace("/", "_")
    srcContentDir = os.path.join(buildParam.packageToolDir, "PackageAsset", buildParam.projectName + "_" + pBranch, "Content")
    destContentDir = os.path.join(buildParam.projectSourceDir, "Content")
    if os.path.exists(srcContentDir):
        ciTools.copyDirByShutil(srcContentDir, destContentDir)
    return True

def downloadPackageAssetWithPathId(buildParam : BuildProjectParam):
    printLog("downloadPackageAssetWithPathId {}, {}" .format(buildParam.packageWithLocalAsset, buildParam.packageAssetPathIdList))
    assetFileListConfig = os.path.join(buildParam.projectSourceDir, "asset_config.txt")
    assetList = None
    if buildParam.downloadAssetPathIdList is not None and len(buildParam.downloadAssetPathIdList) > 1:
        assetList = buildParam.downloadAssetPathIdList.split(",")
    if assetList is None:
        printLog("downloadPackageAssetWithPathId no path id input")
        return True
    with open(assetFileListConfig, "w", encoding="utf-8") as out:
        for pathid in assetList:
            if len(pathid) > 0:
                out.write(pathid)
                out.write("\n")
        out.close()
    downloadRet = downloadAssetsWithEditorCmd(buildParam,assetFileListConfig, buildParam.downloadAssetResourceVersion)
    if downloadRet == False:
        buildParam.buildResultCode = 43
        buildParam.buildResultMsg = "download asset with console error"
        return False
    return True

def downloadPackageAssetWithReleaseId(buildParam : BuildProjectParam):
    printLog("downloadPackageAssetWithReleaseId {}, {}, {}" .format(buildParam.packageWithLocalAsset, buildParam.xsdkAppId, buildParam.xsdkReleaseId))
    downloadParam = ResouceDownlaodParam()
    downloadParam.targetPlatform = buildParam.targetPlatform
    downloadParam.projectSourceDir = buildParam.projectSourceDir
    downloadParam.cacheDir = os.path.join(buildParam.packageToolDir, "Cache")
    downloadParam.xsdkAppId = buildParam.xsdkAppId
    downloadParam.xsdkReleaseId = buildParam.xsdkReleaseId
    downloadParam.assetListDownloadConfigPath = os.path.join(downloadParam.cacheDir, "asset_config.txt")
    if os.path.exists(downloadParam.assetListDownloadConfigPath):
        os.remove(downloadParam.assetListDownloadConfigPath)
    downloadAssetMgr = DownloadAssetMgr()
    downloadAssetMgr.buildParam = downloadParam
    downloadAssetMgr.init()
    downloadAssetMgr.queryReleaseAssetList()
    # generate download asset pathList
    generateConfigRet = downloadAssetMgr.generateAssetConsoleConfig()
    if generateConfigRet == True:
        buildProjectRet = buildProjectNoArchiveAnNoCook(buildParam)
        if buildProjectRet == False:
            buildParam.buildResultCode = 42
            buildParam.buildResultMsg = "build project for no archive error"
            return False
        downloadRet = downloadAssetsWithEditorCmd(buildParam, downloadParam.assetListDownloadConfigPath, downloadAssetMgr.releaseConfig.resourceVersion)
        if downloadRet == False:
            buildParam.buildResultCode = 43
            buildParam.buildResultMsg = "download asset with console error"
            return False
        else:
            writePackedAssetInfo(buildParam, downloadAssetMgr.releaseConfig.resourceVersion)
            return True
    else:
        buildParam.buildResultCode = 41
        buildParam.buildResultMsg = "download package asset error"
        return False

def downloadPackageAssetWithReleaseIdV1(buildParam : BuildProjectParam):
    printLog("downloadPackageAssetWithReleaseIdV1 {}, {}, {}" .format(buildParam.packageWithLocalAsset, buildParam.xsdkAppId, buildParam.xsdkReleaseId))
    try:
        ridAssetMgr = AssetMgr(buildParam.xsdkAppId, buildParam.xsdkReleaseId, buildParam.projectSourceDir, buildParam.mockAppId, buildParam.panoVideoDstDir, buildParam.bPreRelease, buildParam.bCustomPak, buildParam.bNeedLowmodelPak, buildParam.bOnlyLowmodelPak, buildParam.bCollectPso, buildParam.xsdkEnv, buildParam.bGeneratePsoWorld, buildParam.bOnlyVideo)
        if buildParam.anchorConfigJsonData:
            with open(ridAssetMgr.asset_path / "Files" / buildParam.xsdkMarkerFile, 'wb') as e:
                e.write(orjson.dumps(buildParam.anchorConfigJsonData, option=orjson.OPT_INDENT_2))
        if buildParam.xsdkMarkerFile is not None and len(buildParam.xsdkMarkerFile) > 0 and not buildParam.bOnlyVideo and buildParam.bNeedLowmodelPak:
            ridAssetMgr.write_xsdk_anchor(buildParam.xsdkMarkerFile)
        if buildParam.bPreRelease and buildParam.xsdkReleaseId:
            buildParam.nativeLoadInfoPath = ridAssetMgr.cache_path / f"{buildParam.xsdkAppId}_{buildParam.xsdkReleaseId}_loadInfo.json"
            with open(buildParam.nativeLoadInfoPath, 'wb') as f:
                f.write(orjson.dumps(ridAssetMgr.native_load_info, option=orjson.OPT_INDENT_2))
            buildParam.applicationDisplayName = f"{ridAssetMgr.native_app_name}-{buildParam.xsdkReleaseId}预览"
            buildParam.packageName = f"com.xverse.{ridAssetMgr.native_repo_name.lower()}Preview"
            writeEngineAndGameConfig(buildParam, 'Android')

        buildParam.ibrAssetSize, buildParam.pbrAssetSize = ridAssetMgr.start()

        # 通用lbvr分支读取片源视频配置（Content/Movies）
        if buildParam.branch in ['feat/lbvr', 'feat/lbvr_en']  and (buildParam.bNeedMainPak or buildParam.bNeedApk):
            allLBVRMovieInfo = rtoml.load(ridAssetMgr.lbvr_movie_cfg_path)
            if not allLBVRMovieInfo.get(buildParam.packageName):
                shutil.rmtree(os.path.join(buildParam.projectSourceDir, "Content", "Movies"))
                printLog("remove all movies")
                # raise Exception(f"{buildParam.packageName} 未配置需要的视频文件")
            else:
                movieInfo = allLBVRMovieInfo.get(buildParam.packageName)
                if len(movieInfo['allMovies']) <= 0 and movieInfo['parent'] == "":
                    shutil.rmtree(os.path.join(buildParam.projectSourceDir, "Content", "Movies"))
                    printLog("remove all movies")
                else:
                    fileFilter(os.path.join(buildParam.projectSourceDir, "Content", "Movies"), movieInfo['allMovies'], movieInfo['parent'])
        return True
    except Exception as e:
        printLog("downloadPackageAssetWithReleaseIdV1 {}, {}, {} error: {}" .format(buildParam.packageWithLocalAsset, buildParam.xsdkAppId, buildParam.xsdkReleaseId, str(e)))
        return False

def clearOldCache(buildParam : BuildProjectParam):
    # printLog("clearOldCache {}, {}, {}" .format(buildParam.forceResetLocalBranch, buildParam.embedCleanOldCache, buildParam.embedGitClean))

    # if buildParam.forceResetLocalBranch == True:
    # ciTools.resetProjectBranch(buildParam.projectSourceDir)
    # if buildParam.bVRAndroidCiForEngine == True and buildParam.engineInstalled == False:
    #     ciTools.resetProjectBranch(buildParam.engineDir) #引擎额外

    # if buildParam.embedCleanOldCache == True:
    # ciTools.clearProjectIntermediate(buildParam.projectSourceDir)
    # ciTools.clearProjectContent(buildParam.projectSourceDir)

    # if buildParam.embedGitClean == True:
    printLog("clear all project cache {}".format(buildParam.projectSourceDir))
    ciTools.gitClean(buildParam.projectSourceDir)
    # if buildParam.bVRAndroidCiForEngine == True and buildParam.engineInstalled == False:
    #     printLog("clear all engine cache {}".format(buildParam.engineDir))
    #     ciTools.gitClean(buildParam.engineDir)

def handleProjectBranchInfo(buildParam : BuildProjectParam):
    printLog("handleProjectBranchInfo " + buildParam.projectName)
    if buildParam.embedSwitchBranch == True:
        result, msg = ciTools.switchBranch(buildParam.projectSourceDir, buildParam.branch)
        if result == False:
            buildParam.buildResultCode = 2
            buildParam.buildResultMsg = f"switch project branch error: {msg}"
            return False
    else:
        branchInfo = ciTools.getBranch(buildParam.projectSourceDir)
        buildParam.branch = branchInfo.branch
    if buildParam.embedPullLatestCode == True:   
        printLog("pullBranch " + buildParam.projectSourceDir)     
        result = ciTools.pullBranch(buildParam.projectSourceDir)
        if result == False:
            buildParam.buildResultMsg = "pull project code error"
            buildParam.buildResultCode = 3
            return False
    if buildParam.commitId is not None and len(buildParam.commitId)>1:
        result = ciTools.branchCheckoutCommitId(buildParam.projectSourceDir, buildParam.commitId)
        if result == False:
            buildParam.buildResultCode = 2
            buildParam.buildResultMsg = "project branch checkout commitId error"
            return False
    ciTools.printBranchLog(buildParam.projectSourceDir)
    ciTools.getProjectCommitInfo(buildParam)
    # if buildParam.bVRAndroidCiForEngine==False:
    #     ciTools.getEngineCommitInfo(buildParam, buildParam.engineDir, buildParam.engineBranch)
    return True

def handleEngineBranchInfo(buildParam : BuildProjectParam):
    printLog("handleEngineBranchInfo " + buildParam.engineDir)
    if buildParam.embedSwitchBranch == True:
        result, msg = ciTools.switchBranch(buildParam.engineDir, buildParam.engineBranch)
        if result == False:
            buildParam.buildResultCode = 2
            buildParam.buildResultMsg = f"switch engine branch error, {msg}"
            return False
    else:
        branchInfo = ciTools.getBranch(buildParam.engineDir)
        buildParam.branch = branchInfo.branch
    if buildParam.embedPullLatestCode == True:
        printLog("pullBranch " + buildParam.engineDir)     
        result = ciTools.pullBranch(buildParam.engineDir)
        if result == False:
            buildParam.buildResultMsg = "pull engine code error"
            buildParam.buildResultCode = 3
            return False
    if buildParam.engineCommitId is not None and len(buildParam.engineCommitId)>1:
        result = ciTools.branchCheckoutCommitId(buildParam.engineDir, buildParam.engineCommitId)
        if result == False:
            buildParam.buildResultCode = 2
            buildParam.buildResultMsg = "engine branch checkout commitId error"
            return False
    enginePropsPath = os.path.join(buildParam.engineDir, "Engine\\Source\\Programs\\Shared\\UnrealEngine.csproj.props")
    if not os.path.exists(enginePropsPath):
        shutil.copyfile(os.path.join(buildParam.packageToolDir, "Plugins\\UnrealEngine.csproj.props"), enginePropsPath)
    ciTools.printBranchLog(buildParam.engineDir)
    ciTools.getEngineCommitInfo(buildParam, buildParam.engineDir, buildParam.engineBranch)
    return True

def prepareBuildProject(buildParam : BuildProjectParam):
    # resetConfig(buildParam)
    # resetContent(buildParam)
    genXVerseProjectConfig(buildParam)
    writeXSDKConfig(buildParam)
    writeXPhySceneConfig(buildParam)
    removeStarterContent(buildParam)
    writeAsseetCookConfig(buildParam)

def genDeployScripts(buildParam : BuildProjectParam):
    if buildParam.bVRAndroidCi:
        # 拷贝InstallTools
        shutil.copytree(os.path.join(buildParam.packageToolDir, 'InstallTools'), os.path.join(buildParam.fullProjectOutPutDir, 'tool'))
        if buildParam.xsdkReleaseId and buildParam.xsdkAppId:
            panoVideoCfg = {
                "AppId": buildParam.xsdkAppId,
                "ReleaseId": buildParam.xsdkReleaseId,
                "DownloadPath": "./video"
            }
            with open(os.path.join(buildParam.fullProjectOutPutDir, 'tool/config.json'), 'wb') as f:
                f.write(orjson.dumps(panoVideoCfg, option=orjson.OPT_INDENT_2))
        # 获取包名
        obbName = getObbName(buildParam.projectSourceDir)
        # 生成脚本
        if buildParam.bCustomPak:
            formatScripts(Path(os.path.join(buildParam.packageToolDir, 'template/custom_pak')), Path(buildParam.fullProjectOutPutDir), obbName)
        else:
            formatScripts(Path(os.path.join(buildParam.packageToolDir, 'template/obb')), Path(buildParam.fullProjectOutPutDir), obbName)
            

def getObbName(proj_src_dir):
    try:
        cfg = IniConfigHelper()
        cfg.read(os.path.join(proj_src_dir, 'Config\\Android\\AndroidEngine.ini'))
        return cfg.get('/Script/AndroidRuntimeSettings.AndroidRuntimeSettings', 'PackageName')
    except:
        raise ValueError("获取安装包名错误")

def formatScripts(template_dir, out_dir, obbName):
    for ts in template_dir.iterdir():
        out_ts_path = out_dir / ts.name
        out_ts_str = ts.read_text().replace("com.xverse.template", obbName)
        with open(out_ts_path, 'w') as f:
            f.write(out_ts_str)
        


def afterBuildProject(buildParam : BuildProjectParam):
    resetConfig(buildParam)
    resetContent(buildParam)
    
def customPak(buildParam : BuildProjectParam):
    printLog("customPak {} ".format(buildParam.branch))
    projectPath = "{0}\{1}.uproject".format(buildParam.projectSourceDir, buildParam.projectName)
    exeName = "UnrealEditor-Cmd.exe"
    unrealEditorExe = os.path.join(buildParam.engineDir,"Engine", "Binaries", "Win64", exeName)
    pakDstDir = os.path.join(buildParam.fullProjectOutPutDir, "{0}_{1}".format(buildParam.targetPlatform, buildParam.cookflavor))
    customPakLog = os.path.join(buildParam.projectOutPutDir, "Logs", buildParam.projectName, buildParam.outputFileName, "custompakLog.txt")
    customLowModelPakLog = os.path.join(buildParam.projectOutPutDir, "Logs", buildParam.projectName, buildParam.outputFileName, "customLowModelpakLog.txt")
    customLowModelPakCmd = "{0} {1} -RenderOffScreen -abslog={2} -stdout -CrashForUAT -unattended -NoLogTimes -UTF8Output --- z.StartLowModelPakOnly {3}".format(unrealEditorExe, projectPath, customLowModelPakLog, pakDstDir)
    customPakCmd = "{0} {1} -RenderOffScreen -abslog={2} -stdout -CrashForUAT -unattended -NoLogTimes -UTF8Output --- z.StartCustomPakSilent {3}".format(unrealEditorExe, projectPath, customPakLog, pakDstDir)
    if buildParam.bNeedApk or buildParam.bNeedMainPak:
        bResult, msg = ciTools.runScriptWithLog(customPakCmd, customPakLog)
        if not bResult:
            printLog("customPak failed")
            buildParam.buildResultMsg = msg
            return bResult
        stopCustomPak(buildParam)
        # extra custompak，后续删除
        # if buildParam.branch == 'feat/lbvr-opt-movies':
        # if os.path.exists(os.path.join(buildParam.projectSourceDir, "Content", "Movies")):
        #     shutil.rmtree(os.path.join(buildParam.projectSourceDir, "Content", "Movies"))
        #     extraPakDstDir = os.path.join(buildParam.fullProjectOutPutDir, "extra")
        #     extraCustomPakLog = os.path.join(buildParam.projectOutPutDir, "Logs", buildParam.projectName, buildParam.outputFileName, "extraCustompakLog.txt")
        #     extraCustomPakCmd = "{0} {1} -RenderOffScreen -abslog={2} -stdout -CrashForUAT -unattended -NoLogTimes -UTF8Output --- z.StartCustomPakSilent {3}".format(unrealEditorExe, projectPath, extraCustomPakLog, extraPakDstDir)
        #     bResult, msg = ciTools.runScriptWithLog(extraCustomPakCmd, extraCustomPakLog)
        #     if not bResult:
        #         printLog("customPak failed")
        #         buildParam.buildResultMsg = msg
        #         return bResult
        #     stopCustomPak(buildParam)
        #     shutil.copy2(os.path.join(extraPakDstDir, "Android_ASTC", "XVerseVR_Oculus_arm64.apk"), os.path.join(pakDstDir, "Android_ASTC", "XVerseVR_Oculus_arm64.apk"))
        #     shutil.rmtree(extraPakDstDir)
        shaderPipelineCachesPath = os.path.join(buildParam.projectSourceDir, f"Saved\\Cooked\\Android_ASTC\\{buildParam.projectName}\\Metadata\PipelineCaches")
        shaderPipelineCacheNum = len(glob.glob(shaderPipelineCachesPath + "\\*"))
        if os.path.exists(shaderPipelineCachesPath) and shaderPipelineCacheNum == 3:
            shutil.copytree(shaderPipelineCachesPath, os.path.join(buildParam.fullProjectOutPutDir, "PipelineCaches"), dirs_exist_ok=True)
        else:
            printLog(f"{shaderPipelineCachesPath} 未生成")
    if buildParam.bNeedLowmodelPak:
        bResult, msg = ciTools.runScriptWithLog(customLowModelPakCmd, customLowModelPakLog)
        if not bResult:
            printLog("customPak gen lowmodel pak failed")
            buildParam.buildResultMsg = msg
            return bResult
    return True

def generatePsoWorldUmap(buildParam : BuildProjectParam):
    printLog("generatePsoWorldUmap {}".format(buildParam.branch))
    projectPath = "{0}\{1}.uproject".format(buildParam.projectSourceDir, buildParam.projectName)
    exeName = "UnrealEditor-Cmd.exe"
    unrealEditorExe = os.path.join(buildParam.engineDir,"Engine", "Binaries", "Win64", exeName)
    generatePsoWorldCmd = "{0} {1} -RenderOffScreen -Log -stdout -CrashForUAT -unattended -NoLogTimes -UTF8Output -run=PSOCollectEditorCommandlet".format(unrealEditorExe, projectPath)
    printLog("generatePsoWorldUmap script {}".format(generatePsoWorldCmd))
    bResult, msg = ciTools.runScript(generatePsoWorldCmd)
    if not bResult:
        buildParam.buildResultMsg = msg
    if os.path.exists(os.path.join(buildParam.projectSourceDir,"Content","PSOCollect","PSOWorld.umap")):
        return bResult
    else:
        return False

def modifyAllStartupMap(buildParam: BuildProjectParam, map):
    config = IniConfigHelper()
    def modifyEngineIni(iniPath):
        config.content.clear()
        config.read(iniPath)
        config.set('/Script/EngineSettings.GameMapsSettings', 'EditorStartupMap', map)
        config.set('/Script/EngineSettings.GameMapsSettings', 'GameDefaultMap', map)
        config.write(ep)
        
    engineConfigPaths = [os.path.join(buildParam.projectSourceDir, "Config", "Android", f"AndroidEngine.ini"), os.path.join(buildParam.projectSourceDir, "Config", "Windows", f"WindowsEngine.ini"), os.path.join(buildParam.projectSourceDir, "Config", "DefaultEngine.ini")]
    for ep in engineConfigPaths:
        if not os.path.exists(ep):
            continue
        modifyEngineIni(ep)

def nativeLoad(buildParam : BuildProjectParam):
    printLog("start native loader {}".format(buildParam.branch))
    projectPath = "{0}\{1}.uproject".format(buildParam.projectSourceDir, buildParam.projectName)
    exeName = "UnrealEditor-Cmd.exe"
    unrealEditorExe = os.path.join(buildParam.engineDir,"Engine", "Binaries", "Win64", exeName)
    nativeLoaderCmd = "{0} {1} -RenderOffScreen -Log -stdout -CrashForUAT -unattended -NoLogTimes -UTF8Output --- z.VribrSetNativeLoader @{2} --- z.PipelineExit".format(unrealEditorExe, projectPath, buildParam.nativeLoadInfoPath)
    printLog("start native loader script {}".format(nativeLoaderCmd))
    bResult, msg = ciTools.runScript(nativeLoaderCmd)
    if not bResult:
        buildParam.buildResultMsg = msg
    return bResult


def stopCustomPak(buildParam : BuildProjectParam):
    killCmd = "TASKKILL /F /IM UnrealEditor-Cmd.exe /T"
    printLog("stopCustomPak {} ".format(killCmd))
    bResult, msg = ciTools.runScript(killCmd)
    return bResult

def uploadCustompakLog(buildParam : BuildProjectParam):
    custompakLogPath = os.path.join(buildParam.projectOutPutDir, "Logs", buildParam.projectName, buildParam.outputFileName, "custompakLog.txt")
    
    projectBuildInfo = buildParam.projectBuildUrl.split('/')
    custompakLogDisplayName = f"{projectBuildInfo[-3]}{projectBuildInfo[-2]}CustompakLog.txt"
    tempLogPath = os.path.join("C:\\custompak", custompakLogDisplayName)
    with open(custompakLogPath, 'r', encoding='utf-8') as src, \
            open(tempLogPath, 'w', encoding='utf-8') as dst:
        dst.writelines(
            line for line in src 
            if not line.startswith("LogUdpMessaging")
        )
    with open(tempLogPath, 'rb') as f:
        data = {
            'filename': custompakLogDisplayName,
            'Content-Disposition': 'form-data;',
            'Content-Type': 'application/octet-stream',
            'file': (custompakLogDisplayName, f, 'application/octet-stream')
        }
        form_data = MultipartEncoder(data)
        response = requests.post("https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key=e26e016b-bc57-4c04-9c09-cce9939d091a&type=file", headers={'Content-Type': form_data.content_type}, data=form_data).json()
        if response['errcode'] == 0:
            return response['media_id']
    os.remove(tempLogPath)
    return None
        

def  buildProjectInner(buildParam : BuildProjectParam):
    if not buildParam.bOnlyVideo:
        ret = createProject(buildParam)
        if ret == False:
            buildParam.buildResultCode = 1
            buildParam.buildResultMsg = "创建project失败"
            return False
        try:
            if buildParam.targetPlatform.startswith("Android"):
                writeEngineAndGameConfig(buildParam, "Android")
                writeEngineAndGameConfig(buildParam, "Windows")
                # writeOverrideConfigJsonFile(buildParam)
                writeUprojectFile(buildParam)
        except Exception as e:
            buildParam.buildResultCode = 1
            buildParam.buildResultMsg = f"写入engine和game配置失败：{str(e)}"
            return False
    buildParam.downloadAssetStartTime = datetime.now()
    isDownloadAssetSucc = downloadPackageAsset(buildParam)
    if isDownloadAssetSucc == False:
        # copyProjectBuildLog(buildParam)
        buildParam.buildResultCode = 11
        buildParam.buildResultMsg = "{} 资产下载失败".format(buildParam.projectName) 
        return False
    if not buildParam.bOnlyVideo:
        getXsdkAnchor(buildParam)
        buildParam.buildStartTime = datetime.now()
        # if not (buildParam.bOnlyLowmodelPak or buildParam.bGeneratePsoWorld or buildParam.bPreRelease):
        #     isBuildSucc = buildAndCookProject(buildParam)
        # else:
        if buildParam.bGeneratePsoWorld:
            shutil.copytree(os.path.join(buildParam.packageToolDir, "Plugins\\PSOCollect"), os.path.join(buildParam.projectSourceDir, "Plugins\\PSOCollect"), dirs_exist_ok=True)
        isBuildSucc = buildProjectNoArchiveAnNoCook(buildParam)
        if isBuildSucc == False:
            copyProjectBuildLog(buildParam)
            buildParam.buildResultCode = 11
            # buildParam.buildResultMsg = "{} 编译失败".format(buildParam.projectName) 
            return False
        
        buildParam.custompakStartTime = datetime.now()
        if buildParam.bGeneratePsoWorld:
            isGenSucc = generatePsoWorldUmap(buildParam)
            if isGenSucc == False:
                copyProjectBuildLog(buildParam) 
                buildParam.stateDescribe = f"pso关卡生成失败"
                buildParam.buildResultCode = 1
                buildParam.buildResultMsg = f"pso关卡生成失败"
                return False
        
        if buildParam.bPreRelease:
            modifyAllStartupMap(buildParam, '/XAppEntry/XIBRNativeMap.XIBRNativeMap')
            isLoadSucc = nativeLoad(buildParam)
            if isLoadSucc == False:
                copyProjectBuildLog(buildParam) 
                buildParam.stateDescribe = f"加载预览包资产失败"
                buildParam.buildResultCode = 1
                buildParam.buildResultMsg = f"加载预览包资产失败"
                return False
            modifyAllStartupMap(buildParam, '/XAppEntry/XIBRNativeLoading.XIBRNativeLoading')
        if not buildParam.bOnlyLowmodelPak:
            isCookSucc = buildAndCookProject(buildParam)
            if isCookSucc == False:
                copyProjectBuildLog(buildParam) 
                buildParam.stateDescribe = f"生成apk/cook/打包失败"
                buildParam.buildResultCode = 1
                buildParam.buildResultMsg = f"生成apk/cook/打包失败"
                return False
            shaderPipelineCachesPath = os.path.join(buildParam.projectSourceDir, f"Saved\\Cooked\\Android_ASTC\\{buildParam.projectName}\\Metadata\PipelineCaches")
            shaderPipelineCacheNum = len(glob.glob(shaderPipelineCachesPath + "\\*"))
            if os.path.exists(shaderPipelineCachesPath) and shaderPipelineCacheNum == 3:
                shutil.copytree(shaderPipelineCachesPath, os.path.join(buildParam.fullProjectOutPutDir, "PipelineCaches"), dirs_exist_ok=True)
            else:
                printLog(f"{shaderPipelineCachesPath} 未生成")
        
        if not buildParam.bCustomPak:
            if not glob.glob(os.path.join(buildParam.fullProjectOutPutDir, "Android_ASTC\\*.obb")) and not buildParam.bGeneratePsoWorld and not buildParam.bPreRelease:
                buildParam.stateDescribe = f"obb打包失败"
                buildParam.buildResultCode = 1
                buildParam.buildResultMsg = "{} obb打包失败".format(buildParam.projectName) 
                return False
            stopCustomPak(buildParam)
        # customPak
        else:
            pakSuccess = customPak(buildParam)
            if pakSuccess == False:
                copyProjectBuildLog(buildParam)
                buildParam.buildResultCode = 16
                # stopCustomPak(buildParam)
                time.sleep(5)
                return False
                # try:
                #     buildParam.customPakLogMediaId = uploadCustompakLog(buildParam)
                # except Exception as e:
                #     printLog("上传custompak日志失败: {}".format(str(e)))
                # return False
            else:
                stopCustomPak(buildParam)
                time.sleep(10)
            
        copyProjectBuildLog(buildParam)    
        if buildParam.targetPlatform.lower() == "ios":
            rsyncRet = rsyncIPAToRemote(buildParam)
            if rsyncRet == False:
                buildParam.buildResultCode = 12
                buildParam.buildResultMsg = "上传ipa到远程mac失败"
                return False
            signRet = signIPA(buildParam)
            if signRet == False and 1 == 0:
                buildParam.buildResultCode = 13
                buildParam.buildResultMsg = "远程ipa签名失败"
                return False
            downloadIpaRet = rsyncIPAToLocal(buildParam)
            if downloadIpaRet == False:
                buildParam.buildResultCode = 14
                buildParam.buildResultMsg = "下载远程ipa失败"
                return False
            saveIosDsym(buildParam)
        if buildParam.embedUploadSymbol == True:
            retCode, msg = ciTools.uploadProjecSymbol(buildParam)
            if retCode == False:
                buildParam.buildResultCode = 15
                buildParam.buildResultMsg = f"upload symbol error:{msg}"
                return False
        if buildParam.targetPlatform.startswith("Win64") or buildParam.targetPlatform.startswith("Linux"):
            genXVerseProject(buildParam)
            genXCreatorVersion(buildParam)
            genLocalSrcVersionConfig(buildParam)
            #copyConsoleConfig(buildParam)
            copyEngineVersionFile(buildParam)
            copyXBatchEditWhiteList(buildParam)
            copyXVerseProjectConfig(buildParam)
        copyRuntimeDLL(buildParam)
        if buildParam.targetPlatform.startswith("Win64"):
            copyLaunchBat(buildParam)
        if buildParam.targetPlatform.startswith("Android") and not (buildParam.bVRAndroidCi or buildParam.bVRAndroidCiForEngine):
            copyAndroidSo(buildParam)
        if buildParam.targetPlatform.startswith("Linux") and buildParam.projectName.startswith("XCapture"):
            copyCaptureSh(buildParam)
        if buildParam.embedRemoveSymbol == True:
            ciTools.removeSymbol(buildParam.fullProjectOutPutDir)
        
        buildParam.sambaUploadStartTime = datetime.now()
        renameProject(buildParam)
        try:
            genDeployScripts(buildParam)
        except Exception as e:
            buildParam.stateDescribe = "生成安装脚本失败"
            buildParam.buildResultCode = 1
            buildParam.buildResultMsg = str(e)
            return False
        try:
            if buildParam.embedUploadCos or buildParam.embedUploadSamba:
                compressProject(buildParam)
            if buildParam.embedUploadSamba == True:
                uploadToSamba(buildParam)
        except Exception as e:
            buildParam.stateDescribe = "上传至samba失败"
            buildParam.buildResultCode = 1
            buildParam.buildResultMsg = str(e)
            return False
        if buildParam.embedUploadCos == True and buildParam.projectName in ciTools.CIConfig.allowUploadCosProject:
            if buildParam.directionUploadCos == True:
                ret = ciTools.uploadToCosByDefault(buildParam.projectCosPath, buildParam.fullProjectOutPutZipPath)
                if ret == True:
                    buildParam.stateDescribe = "上传Cos成功"
                    if buildParam.projectName.startswith("XCapture"):
                        insertCaptureCosRecord(buildParam, ciTools.CaptureCosInfo.StateUploadSucc)
                else:
                    buildParam.stateDescribe = "上传Cos失败"
                    buildParam.buildResultCode = 1
                    buildParam.buildResultMsg = "xcapture upload to cos error"
                    return False
            else:
                buildParam.stateDescribe = "等待上传Cos(任务ID:{})".format(buildParam.taskUid)
                captureFilePath = insertCaptureCosRecord(buildParam)
                ciTools.addUploadCaptureTask(buildParam, captureFilePath)

    # afterBuildProject(buildParam)
    buildParam.buildResultCode = 0
    buildParam.buildResultMsg = "OK"
    overrideConfigPath = os.path.join(buildParam.projectSourceDir, "Content", "Files", "OverrideConfig.json")
    if buildParam.backgroundUpload:
        try:
            tmpConfigPath = os.path.join(buildParam.panoVideoDstDir, "OverrideConfig.json")
            shutil.copy2(overrideConfigPath, tmpConfigPath)
            temp_file_path = ''
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pkl', dir=os.path.join(os.getcwd(),'tmp')) as tf:
                pickle.dump(buildParam, tf)
                temp_file_path = tf.name
            subprocess.Popen(['python', f"{buildParam.packageToolDir}\\uploadNotify.py", temp_file_path], start_new_session=True, creationflags=subprocess.DETACHED_PROCESS | subprocess.CREATE_NEW_PROCESS_GROUP, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            # printLog(f"uploadNotify pid:{p.pid}")
        except Exception as e:
            cosUtil.sendDevMessage(f'{buildParam.projectBuildUrl}, {e}: 创建上传通知任务失败')
    else:
        if buildParam.bOTA:
            buildParam.otaUploadStartTime = datetime.now()
            apkLocalPath = os.path.join(buildParam.fullProjectOutPutDir, "asset","XVerseVR_Oculus-arm64.apk")
            pakLocalPath = os.path.join(buildParam.fullProjectOutPutDir, "asset","xverse-Android_ASTC.pak")
            lowModelPakLocalPath = os.path.join(buildParam.fullProjectOutPutDir, "asset", "xverse-lowmodel-Android_ASTC.pak")
            
            if buildParam.apkCosPrefix:
                apkCosPrefix = buildParam.apkCosPrefix
            else:
                apkCosPrefix = f"{buildParam.videoName}/{buildParam.storeName}"
            apkCosPath = f"{apkCosPrefix}/apk/{buildParam.versionDisplayName}/XVerseVR_Oculus-arm64.apk"

            if buildParam.pakCosPrefix:
                pakCosPrefix = buildParam.pakCosPrefix
            else:
                pakCosPrefix = f"{buildParam.videoName}/{buildParam.storeName}"

            pakCosPath = f"{pakCosPrefix}/pak/{buildParam.xsdkVersion}/xverse-Android_ASTC.pak"
            
            if buildParam.lowmodelPakCosPrefix:
                lowmodelPakCosPrefix = buildParam.lowmodelPakCosPrefix
            else:
                lowmodelPakCosPrefix = f"{buildParam.videoName}/{buildParam.storeName}"
            lowModelPakCosPath = f"{lowmodelPakCosPrefix}/lowmodel/{buildParam.xsdkLowModelVersion}/xverse-lowmodel-Android_ASTC.pak"
            

            buildParam.stateDescribe = ''
            otaStorage = OTAStorage()
            if os.path.exists(apkLocalPath):
                buildParam.apkCosPath, msg = otaStorage.uploadWithCrc64(apkLocalPath, apkCosPath, buildParam.bForceUploadOTA)
                buildParam.apkSize = getFileSize(apkLocalPath)
                printLog(f"upload to {apkCosPath}: {msg}")
                if not buildParam.apkCosPath:
                    buildParam.stateDescribe += f"OTA-apk版本冲突：{msg}\n"
            if os.path.exists(pakLocalPath):
                buildParam.pakCosPath, msg = otaStorage.uploadWithCrc64(pakLocalPath, pakCosPath, buildParam.bForceUploadOTA)
                buildParam.pakSize = getFileSize(pakLocalPath)
                printLog(f"upload to {pakCosPath}: {msg}")
                if not buildParam.pakCosPath:
                    buildParam.stateDescribe += f"OTA-主pak版本冲突：{msg}\n"
            if os.path.exists(lowModelPakLocalPath):
                buildParam.lowModelPakCosPath, msg = otaStorage.uploadWithCrc64(lowModelPakLocalPath, lowModelPakCosPath, buildParam.bForceUploadOTA)
                buildParam.lowmodelPakSize = getFileSize(lowModelPakLocalPath)
                printLog(f"upload to {lowModelPakCosPath}: {msg}")
                if not buildParam.lowModelPakCosPath:
                    buildParam.stateDescribe += f"OTA-低模pak版本冲突：{msg}\n"
            if buildParam.panoVideoDstDir:
                allPanoramicVideoInfo = getAllPanoramicVideoInfo(buildParam, overrideConfigPath)
                buildParam.panoramicVideoSize = allPanoramicVideoInfo['size']
                if len(allPanoramicVideoInfo['data']) > 0:
                    videoCosPath, bUpload =  otaStorage.geSertPanoramicOTAInfo(allPanoramicVideoInfo)
                    if bUpload:
                        buildParam.videoCosPath, msg = otaStorage.uploadDir(videoCosPath, buildParam.panoVideoDstDir+"\\")
                        printLog(f"upload to {videoCosPath}: {msg}")
                    else:
                        buildParam.videoCosPath = videoCosPath
                        printLog(f"{buildParam.xsdkAppId}-{buildParam.xsdkReleaseId} reuse {videoCosPath} panoramic assets")
                    if not buildParam.videoCosPath:
                        buildParam.stateDescribe += f"全景视频查询数据库失败，请联系开发人员处理\n"
                        
            msg = otaStorage.startUpload()
            if msg:
                buildParam.stateDescribe += f'OTA上传: {msg}'

            
            buildAllInfo = (buildParam.branch, buildParam.commitId, buildParam.engineBranch, buildParam.engineCommitId,
                buildParam.xsdkAppId, buildParam.xsdkReleaseId, buildParam.pakMode, buildParam.xsdkEnv, buildParam.xsdkVersion, buildParam.locateType,
                buildParam.xsdkMarkerFile, buildParam.xsdkLowModelVersion, buildParam.xsdkLowModelPath, buildParam.xsdkSceneId,
                buildParam.videoName, buildParam.storeName, buildParam.packageName, buildParam.applicationDisplayName,
                buildParam.storeVersion, buildParam.versionDisplayName, buildParam.projectSambaDirForNotify, buildParam.projectBuildUrl,
                buildParam.apkCosPath, buildParam.pakCosPath, buildParam.videoCosPath, buildParam.lowModelPakCosPath,
                buildParam.ibrAssetSize, buildParam.pbrAssetSize, buildParam.panoramicVideoSize, buildParam.apkSize, buildParam.pakSize, buildParam.lowmodelPakSize,
                buildParam.creatTime, buildParam.pullProjectStartTime, buildParam.pullEngineStartTime, buildParam.downloadAssetStartTime,
                buildParam.buildStartTime, buildParam.custompakStartTime, buildParam.sambaUploadStartTime, buildParam.otaUploadStartTime,    
                buildParam.executor, buildParam.buildResultCode, buildParam.buildResultMsg)
            ret, msg = otaStorage.upsertBuildParameterInfo(buildAllInfo)
            
            if not ret:
                cosUtil.sendDevMessage(f'{buildParam.projectBuildUrl}: 写入build 参数和统计信息失败,{msg}\n {buildAllInfo}')
        if buildParam.removeDataAfterBuild == True:
            printLog("buildProjectMain remove Build Data {}".format(buildParam.fullProjectOutPutDir))
            ciTools.removeDir(buildParam.fullProjectOutPutDir)
            ciTools.removeFile(buildParam.fullProjectOutPutZipPath)
            ciTools.removeDir(buildParam.panoVideoDstDir)
        # if buildParam.buildResultCode != 0:
        #     if buildParam.clearDataWhenBuildFail == True:
        #         ciTools.removeDir(buildParam.fullProjectOutPutDir)
        #         ciTools.removeFile(buildParam.fullProjectOutPutZipPath)
        #         ciTools.removeDir(buildParam.panoVideoDstDir)
        #     ciTools.sendBuildResultMessage(buildParam)
            # if buildParam.embedExitWhenBuildFail == True:
            #     ciTools.stopProcess(buildParam.buildResultMsg)
        # else:
        #     buildParam.buildResultCode = 0
        #     buildParam.buildResultMsg = "OK"
        ciTools.sendBuildResultMessage(buildParam)
    return True


def getAllPanoramicVideoInfo(buildParam : BuildProjectParam, overrideConfigPath):
    # overrideConfigPath = os.path.join(buildParam.projectSourceDir, "Content", "Files", "OverrideConfig.json")
    with open(overrideConfigPath, 'rb') as f:
        overrideConfig = orjson.loads(f.read())
    if buildParam.videoCosPrefix:
        videoCosPrefix = buildParam.videoCosPrefix + "/"
    else:
        videoCosPrefix = f"{buildParam.videoName}/{buildParam.storeName}/"
    allPanoramicVideoInfo = {"appID": buildParam.xsdkAppId, "releaseID": buildParam.xsdkReleaseId, "cosPathPrefix": videoCosPrefix, "data":[], "size":0}
    for roomInfo in overrideConfig['config']['roomList']:
        for skinInfo in roomInfo['skinList']:
            skinInfoKeys = list(skinInfo.keys())
            if 'panoramicVideo' in skinInfoKeys and skinInfo['panoramicVideo']:
                allPanoramicVideoInfo['data'].append(
                    {
                        "url": skinInfo['panoramicVideo']['url'].replace("\\", "/"),
                        "local_path": os.path.join(buildParam.panoVideoDstDir, os.path.basename(skinInfo['panoramicVideo']['url']))
                    }
                )
                allPanoramicVideoInfo['size'] += getFileSize(os.path.join(buildParam.panoVideoDstDir, os.path.basename(skinInfo['panoramicVideo']['url'])))
    return allPanoramicVideoInfo



def getCaptureCosInfo(buildParam : BuildProjectParam):
    cInfo = ciTools.readCaptureCosInfo(buildParam.engineBranch, buildParam.branch, buildParam.commitVersion, buildParam.consoleEnv)
    if cInfo is not None and cInfo.cosState == ciTools.CaptureCosInfo.StateUploadSucc:
        buildParam.xCaptureCosPath = cInfo.cosPath

#storage cos path
def insertCaptureCosRecord(buildParam : BuildProjectParam, cosState = ciTools.CaptureCosInfo.StateUnLoad):
    pBranch = buildParam.branch.replace("/", "_")
    branchDirName = "{}_{}".format(pBranch, buildParam.engineBranch)
    xcaptureDirPath = os.path.join(ciTools.CIConfig.xCaptureHistoryDir, branchDirName)
    if not os.path.exists(xcaptureDirPath):
        os.makedirs(xcaptureDirPath) 
    fileName = "{}_{}.json".format(buildParam.commitVersion, buildParam.consoleEnv) 
    filePath = os.path.join(xcaptureDirPath, fileName)
    cInfo = ciTools.CaptureCosInfo()
    cInfo.commitVersion = buildParam.commitVersion
    cInfo.consoleEnv = buildParam.consoleEnv
    cInfo.cosPath = buildParam.projectCosPath
    cInfo.engineBranch = buildParam.engineBranch
    cInfo.projectBranch = buildParam.branch
    cInfo.cosState = cosState
    cInfo.cosRegoin = ciTools.CIConfig.cosRegoin
    return ciTools.writeCaptureCosInfo(filePath, cInfo)

def buildProject(buildParam : BuildProjectParam):
    if buildParam.includeXCapture == True:
        if buildParam.forceBuildXCapture == True or buildParam.xCaptureCosPath is None or len(buildParam.xCaptureCosPath) < 1:
            printLog("Start buildCapture with Project = {}".format(buildParam.projectName))
            captureParam = copy.deepcopy(buildParam)
            captureParam.targetPlatform = "Linux"
            captureParam.projectName = "XCapture"
            captureParam.taskUid = ciTools.incAndGetTaskUid()
            captureParam.embedUploadCos = buildParam.embedCaptureUploadCos
            captureParam.projectDescribeName = captureParam.projectName
            captureParam.projectNameForNotify = captureParam.projectDescribeName
            captureParam.projectPlatformForNotify = captureParam.targetPlatform
            ciTools.updataParamsAfterUserSetting(captureParam)
            getBuildOutputDirInfo(captureParam)
            getProjectUploadPath(captureParam)
            prepareBuildProject(captureParam)
            ciTools.printBuildParam(buildParam, "before build XCaptrue")
            buildCaptureRet = buildProjectInner(captureParam)
            printLog("buildCapture result = {}".format(captureParam.buildResultCode))
            if buildCaptureRet == True:
                captureParam.buildResultCode = 0
                captureParam.buildResultMsg = "OK"
                captureParam.projectNotifyTitle = "{}({})打包成功".format(captureParam.projectNameForNotify, captureParam.projectPlatformForNotify)
                buildParam.projectCosPath = captureParam.projectCosPath
                buildParam.xCaptureCosPath = captureParam.projectCosPath
                if buildParam.noticeMessageList is None:
                    buildParam.noticeMessageList = []
                buildParam.noticeMessageList.append("拍照包:%s"%captureParam.projectCosPath)
                printLog("buildCapture result success = {}".format(captureParam.projectName))
                ciTools.sendBuildResultMessage(captureParam)
            else:
                buildParam.buildResultMsg = captureParam.buildResultMsg
                buildParam.buildResultCode = captureParam.buildResultCode
                buildParam.embedSendNoticeMessage = False
                captureParam.projectNotifyTitle = "{}({})打包失败".format(captureParam.projectNameForNotify, captureParam.projectPlatformForNotify)
                ciTools.sendBuildResultMessage(captureParam)
                return False
        else:
            printLog("buildCapture no Need ReBuild XCaputre(already exist) = {}".format(buildParam.xCaptureCosPath))
            if buildParam.noticeMessageList is None:
                buildParam.noticeMessageList = []
            buildParam.noticeMessageList.append("拍照包:%s"%buildParam.xCaptureCosPath)
    
    if not buildParam.bOnlyVideo:
        try:
            prepareBuildProject(buildParam)
        except Exception as e:
            buildParam.buildResultCode = 1
            buildParam.buildResultMsg = "{} 更新project配置失败：{}".format(buildParam.projectName, str(e)) 
            return False

    return buildProjectInner(buildParam)
def resetConfig(buildParam : BuildProjectParam):
    printLog("resetConfig=" + buildParam.projectName)
    configDir = os.path.join(buildParam.projectSourceDir, "Config")
    if os.path.exists(configDir):
        os.chdir(configDir)
        cmd = "git clean -dxf"
        ret, msg = ciTools.runScript(cmd)
        # ciTools.gitCheckoutDir(configDir)
        printLog("resetConfig %s ret %s"%(configDir, str(ret)))

def resetContent(buildParam : BuildProjectParam):
    printLog("resetContent=" + buildParam.projectName)
    contentDir = os.path.join(buildParam.projectSourceDir, "Content")
    if os.path.exists(contentDir):
        os.chdir(contentDir)
        cmd = "git clean -dxf"
        ret, msg = ciTools.runScript(cmd)
        # ciTools.gitCheckoutDir(contentDir)
        printLog("resetContent %s ret %s"%(contentDir, str(ret)))

#before build project
def removeStarterContent(buildParam : BuildProjectParam):
    printLog("removeStarterContent {}".format(buildParam.packageWithLocalAsset))
    if buildParam.packageWithLocalAsset == True:
        configPath = os.path.join(buildParam.projectSourceDir, "Config", "DefaultGame.ini")
        if not os.path.exists(configPath):
            return
        config = IniConfigHelper()
        config.read(configPath, encoding="utf-8")
        config.remove("StartupActions", "InsertPack")
        config.write(configPath)


def writeAsseetCookConfig(buildParam : BuildProjectParam):
    printLog("writeAsseetCookConfig {} ".format(buildParam.packageWithLocalAsset))
    if buildParam.packageWithLocalAsset == True:
        #EditorStrate
        configPath = os.path.join(buildParam.projectSourceDir, "Config", "EditorStrategyPackaging_%s.ini"%buildParam.projectName)
        config = IniConfigHelper()
        config.read(configPath, encoding="utf-8")
        config.set("/Script/MultiProjectSupports.StrategyPackagingSettings", "bWithGameContentOverride", True)
        config.appendList("/Script/MultiProjectSupports.StrategyPackagingSettings", "DirectoriesToAlwaysCook", '(Path="/Game")', withSign=False)
        config.write(configPath)
#before build project
def writeXSDKConfig(buildParam : BuildProjectParam):
    #Version=0.1.1
    #AppId=11052
    #Env=sit
    #WorldId=42af638cb3734e7180641d5bf5d78ebc
    printLog("writeXSDConfig=" + buildParam.projectName)
    xsdkConfigPath = os.path.join(buildParam.projectSourceDir, "Config", "DefaultXSDKConfig.ini")
    if not os.path.exists(xsdkConfigPath):
        return
    config = IniConfigHelper()
    # keep Upper and lower
    config.read(xsdkConfigPath, encoding="utf-8")
    #oldAppId = config.get("/Script/XBizUtil.XVerseSDKConfig", "AppId")
    if buildParam.xsdkVersion is not None and len(buildParam.xsdkVersion) > 0:
        config.set("/Script/XBizUtil.XVerseSDKConfig", "Version", buildParam.xsdkVersion)
    buildParam.xsdkVersion = config.get("/Script/XBizUtil.XVerseSDKConfig", "Version")
    if buildParam.xsdkAppId is not None and len(buildParam.xsdkAppId) > 0:
        config.set("/Script/XBizUtil.XVerseSDKConfig", "AppId", buildParam.xsdkAppId)
    if buildParam.mockAppId is not None and len(buildParam.mockAppId) > 0:
        config.set("/Script/XBizUtil.XVerseSDKConfig", "AppId", buildParam.mockAppId)
    if buildParam.xsdkEnv is not None and len(buildParam.xsdkEnv) > 0:
        config.set("/Script/XBizUtil.XVerseSDKConfig", "Env", buildParam.xsdkEnv)
    if buildParam.xsdkWorldId is not None and len(buildParam.xsdkWorldId) > 0:
        config.set("/Script/XBizUtil.XVerseSDKConfig", "WorldId", buildParam.xsdkWorldId)
    # pengzhu
    # if buildParam.xsdkbForceUseLocalConfig is not None:
    config.set("/Script/XBizUtil.XVerseSDKConfig", "bForceUseLocalConfig", "true")
    if buildParam.xsdkPackedAssetVersion is not None and len(buildParam.xsdkPackedAssetVersion) > 0:
        config.set("/Script/XBizUtil.XVerseSDKConfig", "PackedAssetVersion", buildParam.xsdkPackedAssetVersion)
    if buildParam.xsdkSceneId is not None and len(buildParam.xsdkSceneId) > 0:
        config.set("/Script/XBizUtil.XVersePhySceneConfig", "SceneId", buildParam.xsdkSceneId)
    buildParam.xsdkSceneId = config.get("/Script/XBizUtil.XVersePhySceneConfig", "SceneId")
    # if buildParam.xsdkMarkerFile is not None and len(buildParam.xsdkMarkerFile) > 0:
    #     config.set("/Script/XBizUtil.XVersePhySceneConfig", "MarkerFile", buildParam.xsdkMarkerFile)
    if buildParam.xsdkLowModelPath is not None and len(buildParam.xsdkLowModelPath) > 0:
        config.set("/Script/XBizUtil.XVersePhySceneConfig", "LowModelPath", buildParam.xsdkLowModelPath)
    buildParam.xsdkLowModelPath = config.get("/Script/XBizUtil.XVersePhySceneConfig", "LowModelPath")
    config.set("/Script/XBizUtil.XVerseSDKConfig", "BranchCommitVersion", buildParam.commitVersion)
    #config.append(xsdkConfigPath)
    config.write(xsdkConfigPath)
    
    with open(os.path.join(buildParam.projectSourceDir, "Content/Files/locate_config.json"), 'w') as f:
        json.dump({"LocateType":locateTypeMap[buildParam.locateType]}, f)

def writeXPhySceneConfig(buildParam : BuildProjectParam):
    printLog("writeXPhySceneConfig=" + buildParam.projectName)
    xphyConfigPath = os.path.join(buildParam.projectSourceDir, "Config", "DefaultXPhySceneConfig.ini")
    # if not os.path.exists(xphyConfigPath):
    #     return
    config = IniConfigHelper()
    # keep Upper and lower
    config.read(xphyConfigPath, encoding="utf-8")
    if buildParam.xsdkLowModelVersion is not None and len(buildParam.xsdkLowModelVersion) > 0:
        config.set("/Script/XBizUtil.XVersePhySceneConfig", "PhyVersion", buildParam.xsdkLowModelVersion)
    buildParam.xsdkLowModelVersion = config.get("/Script/XBizUtil.XVersePhySceneConfig", "PhyVersion")
    if buildParam.xsdkSceneId is not None and len(buildParam.xsdkSceneId) > 0:
        config.set("/Script/XBizUtil.XVersePhySceneConfig", "SceneId", buildParam.xsdkSceneId)
    buildParam.xsdkSceneId = config.get("/Script/XBizUtil.XVersePhySceneConfig", "SceneId")
    # if buildParam.xsdkMarkerFile is not None and len(buildParam.xsdkMarkerFile) > 0:
    #     config.set("/Script/XBizUtil.XVersePhySceneConfig", "MarkerFile", buildParam.xsdkMarkerFile)
    if buildParam.xsdkLowModelPath is not None and len(buildParam.xsdkLowModelPath) > 0:
        config.set("/Script/XBizUtil.XVersePhySceneConfig", "LowModelPath", buildParam.xsdkLowModelPath)
    buildParam.xsdkLowModelPath = config.get("/Script/XBizUtil.XVersePhySceneConfig", "LowModelPath")
    config.write(xphyConfigPath)

def getXsdkAnchor(buildParam : BuildProjectParam):
    xsdkConfigPath = os.path.join(buildParam.projectSourceDir, "Config", "DefaultXSDKConfig.ini")
    config = IniConfigHelper()
    config.read(xsdkConfigPath, encoding="utf-8")
    buildParam.xsdkMarkerFile = config.get("/Script/XBizUtil.XVersePhySceneConfig", "MarkerFile")
    if not buildParam.xsdkMarkerFile:
        config.content.clear()
        config.read(os.path.join(buildParam.projectSourceDir, "Config", "DefaultXPhySceneConfig.ini"), encoding="utf-8")
        buildParam.xsdkMarkerFile = config.get("/Script/XBizUtil.XVersePhySceneConfig", "MarkerFile")

def writePackedAssetInfo(buildParam : BuildProjectParam, packedAssetVersion):
    printLog("writePackedAssetInfo=" + buildParam.projectName)
    xsdkConfigPath = os.path.join(buildParam.projectSourceDir, "Config", "DefaultXSDKConfig.ini")
    if not os.path.exists(xsdkConfigPath):
        return
    config = IniConfigHelper()
    # keep Upper and lower
    config.read(xsdkConfigPath, encoding="utf-8")
    config.set("/Script/XBizUtil.XVerseSDKConfig","PackedAssetVersion", packedAssetVersion)
    config.write(xsdkConfigPath)
#before build project
def writeEngineAndGameConfig(buildParam : BuildProjectParam, platform):
    printLog("writeAndroidEngineConfig=" + buildParam.projectName)
    engineConfigPath = os.path.join(buildParam.projectSourceDir, "Config", platform, f"{platform}Engine.ini")
    if not os.path.exists(engineConfigPath):
        return
    config = IniConfigHelper()
    hasModify = False
    # keep Upper and lower
    config.read(engineConfigPath, encoding="utf-8")
    if buildParam.apkDebugSign is not None and (buildParam.apkDebugSign == "True" or buildParam.apkDebugSign == "False"):
        config.set("/Script/XMobilePublishEditor.XMobilePublishSetting", "bEnableDebugApkSign", buildParam.apkDebugSign)
        hasModify = True

    if buildParam.apkPublishGoogle == True:
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "TargetSDKVersion", "33")
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "SDKAPILevelOverride", "android-33")
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "bUseExternalFilesDir", "True")
        hasModify = True
    if buildParam.buildWithBundle == True:
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "bEnableBundle", "True")
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "bEnableUniversalAPK", "True")
        # split
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "bBundleABISplit", "False")
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "bBundleLanguageSplit", "False")
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "bBundleDensitySplit", "False")
        hasModify = True
    if buildParam.packageName is not None and len(buildParam.packageName) > 0:
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "PackageName", buildParam.packageName)
        hasModify = True
    buildParam.packageName = config.get("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "PackageName")
    if buildParam.applicationDisplayName is not None and len(buildParam.applicationDisplayName) > 0 :
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "ApplicationDisplayName", buildParam.applicationDisplayName)
        hasModify = True
    buildParam.applicationDisplayName = config.get("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "ApplicationDisplayName")

    if buildParam.storeVersion is not None and len(str(buildParam.storeVersion)) > 0 :
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "StoreVersion", buildParam.storeVersion)
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "VersionDisplayName", f"{buildParam.storeVersion}.0.0")
        hasModify = True
    buildParam.storeVersion = config.get("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "StoreVersion")
    # if buildParam.versionDisplayName is not None and len(buildParam.versionDisplayName) > 0 :
    #     config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "VersionDisplayName", buildParam.versionDisplayName)
    #     hasModify = True
    buildParam.versionDisplayName = config.get("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "VersionDisplayName")
    if buildParam.googlePAD == True:
        config.set("/Script/GooglePADEditor.GooglePADRuntimeSetting", "bEnablePlugin", "True")
        config.set("/Script/GooglePADEditor.GooglePADRuntimeSetting", "bOnlyShipping", "False")
        config.set("/Script/GooglePADEditor.GooglePADRuntimeSetting", "bOnlyDistribution", "False")
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "bPackageDataInsideApk", "False")
        hasModify = True
    
    if buildParam.DebugKeyStore is not None and len(buildParam.DebugKeyStore) > 0:
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "DebugKeyStore", buildParam.DebugKeyStore)
        hasModify = True   
    if buildParam.DebugKeyAlias is not None and len(buildParam.DebugKeyAlias) > 0:
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "DebugKeyAlias", buildParam.DebugKeyAlias)
        hasModify = True 
    if buildParam.DebugKeyStorePassword is not None and len(buildParam.DebugKeyStorePassword) > 0:
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "DebugKeyStorePassword", buildParam.DebugKeyStorePassword)
        hasModify = True 
    if buildParam.DebugKeyPassword is not None and len(buildParam.DebugKeyPassword) > 0:
        config.set("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings", "DebugKeyPassword", buildParam.DebugKeyPassword)
        hasModify = True
     # pengzhu
    # if buildParam.bVRAndroidCi == True:
        # 改android engine只会生效apilevel，需要更改defaultEngine or windowsEngine
        # config.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "SDKPath", '''(Path="C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk")''')
        # config.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "NDKPath", '''(Path="C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393")''')
        # config.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "JDKPath", '''(Path="C:\\Program Files\\Java\\jdk-1.8")''')
        # config.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "SDKAPILevel", "matchndk")
        # config.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "NDKAPILevel", "android-32")
        # os.environ["NDK_ROOT"] = "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393"
        # os.environ["NDKROOT"] = "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.1.8937393"
        # os.environ["ANDROID_SDK_HOME"] = "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk"
        # os.environ["ANDROID_HOME"] = "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk"
        # os.environ["JAVA_HOME"] = "C:\\Program Files\\Java\\jdk-1.8"

        # hasModify = True
        # if buildParam.bPreRelease == True:
        #     config.set("/Script/EngineSettings.GameMapsSettings", "EditorStartupMap", "todo pz")
        #     config.set("/Script/EngineSettings.GameMapsSettings", "GameDefaultMap", "todo pz")

    if hasModify == True:
        config.write(engineConfigPath)
    
    printLog("writeAndroidGameConfig=" + buildParam.projectName)
    gameConfigPath = os.path.join(buildParam.projectSourceDir, "Config", platform, f"{platform}Game.ini")
    if not os.path.exists(gameConfigPath):
        return
    
    # hasModify = False
    config.content.clear()
    # keep Upper and lower
    config.read(gameConfigPath, encoding="utf-8")

    config.set("/Script/UnrealEd.ProjectPackagingSettings", "ForDistribution", "False")
    if buildParam.distribution:
        config.set("/Script/UnrealEd.ProjectPackagingSettings", "ForDistribution", "True")
    
    config.write(gameConfigPath)

def setAndroidSDKConfig(buildParam : BuildProjectParam):
    printLog("writeAndroidSDKConfig=" + buildParam.projectName)
    androidSdkConfigPath = os.path.join(buildParam.projectSourceDir, "Config", "DefaultEngine.ini")
    androidSdkConfig = IniConfigHelper()
    androidSdkConfig.read(androidSdkConfigPath)
    androidSdkConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "SDKPath", "(Path=\"C:/Users/<USER>/AppData/Local/Android/Sdk\")")
    androidSdkConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "NDKPath", "(Path=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393\")")
    androidSdkConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "JavePath", "(Path=\"C:/Program Files/Java/jdk-11\")")
    androidSdkConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "SDKAPILevel", "android-29")
    androidSdkConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "NDKAPILevel", "android-25")
    os.environ["JAVA_HOME"] = "C:\\Program Files\\Java\\jdk-11"
    if buildParam.xsdkAppId == "11142":
        androidSdkConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "JavePath", "(Path=\"C:/Program Files/Java/jdk-1.8\")")
        os.environ["JAVA_HOME"] = "C:\\Program Files\\Java\\jdk-1.8"
        androidSdkConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "SDKAPILevel", "matchndk")
        androidSdkConfig.set("/Script/AndroidPlatformEditor.AndroidSDKSettings", "NDKAPILevel", "android-32")
    
    androidSdkConfig.write(androidSdkConfigPath)
    
        
def writeOverrideConfigJsonFile(buildParam : BuildProjectParam):
    # XVerseStudio\Content\Files\OverrideConfig.json
    printLog("writeOverrideConfigJsonFile=" + buildParam.projectName)
    if buildParam.bPreRelease == False:
        return
    overrideConfigJsonPath = os.path.join(buildParam.projectSourceDir, "Content", "Files", "OverrideConfig.json")
    if not os.path.exists(overrideConfigJsonPath):
        return
    with open(overrideConfigJsonPath, 'r') as file:
        cfg = json.load(file)
    cfg["appEntryPath"] = "todo pz"
    if cfg["config"] is not None and cfg["config"]["roomList"] is not None:
        for room_idx, room in enumerate(cfg["config"]["roomList"]):
            if room is not None and room["skinList"] is not None:
                for skin_idx, skin in enumerate(room["skinList"]):
                    skin["skinEntryPath"] = "todo pz"
    with open(overrideConfigJsonPath, 'w') as file:
        json.dump(cfg, file, indent=4, ensure_ascii=False)
    
    
#before build project and after copy project file
def writeUprojectFile(buildParam : BuildProjectParam):
    uprojectPath = os.path.join(buildParam.projectSourceDir, "{}.uproject".format(buildParam.projectName))
    uProjectHelper = UProjectHelper()
    uProjectHelper.read(uprojectPath)
    if buildParam.targetPlatform.startswith("Android") and buildParam.googlePAD == True:
        uProjectHelper.addPlugin("GooglePAD", True)
        uProjectHelper.write(uprojectPath)

def copyProjectBuildLog(buildParam : BuildProjectParam):
    if buildParam.projectLocalBuildLogPath is None or len(buildParam.projectLocalBuildLogPath) < 1:
        buildParam.projectLocalBuildLogPath = os.path.join(buildParam.projectOutPutDir, "Logs", buildParam.projectName, buildParam.outputFileName)
    engineBuildLogPath = ""
    if buildParam.engineInstalled == True:
        appData = os.getenv('APPDATA')
        engineAppDataDirName = buildParam.engineDir.replace("\\", "+")
        engineAppDataDirName = engineAppDataDirName.replace(":", "")
        engineBuildLogPath = os.path.join(appData, "Unreal Engine", "AutomationTool", "Logs", engineAppDataDirName)
        printLog("copyProjectBuildLog={}".format(engineBuildLogPath))
        #engineBuildLogPath="C:\Users\<USER>\AppData\Roaming\Unreal Engine\AutomationTool\Logs\C+work+xverse+EngineOutput+{}".format(buildParam.engineName)
    else:
        engineBuildLogPath = os.path.join(buildParam.engineDir, "Engine", "Programs", "AutomationTool", "Saved", "Logs")
        printLog("copyProjectBuildLog With Source={}".format(engineBuildLogPath))
    if not os.path.exists(buildParam.projectLocalBuildLogPath):
        os.makedirs(buildParam.projectLocalBuildLogPath)
    if os.path.exists(engineBuildLogPath):
        ciTools.copyDirByShutil(engineBuildLogPath, buildParam.projectLocalBuildLogPath)

def signIPA(buildParam : BuildProjectParam):
    printLog("signIPA=" + buildParam.projectName)
    ret = False
    rsyncExe = os.path.join(buildParam.engineDir, "Engine", "Extras", "ThirdPartyNotUE", "DeltaCopy", "Binaries", "Rsync.exe")
    sshExe = os.path.join(buildParam.engineDir, "Engine", "Extras", "ThirdPartyNotUE", "DeltaCopy", "Binaries", "Ssh.exe")
    remoteUser = "H"
    remoteHost = "***********"
    remoteKeyName = "RemoteToolChainPrivate.key"
    remoteSignDir = "RemoteIpaSign"
    appData = os.getenv('APPDATA')
    #C:\Users\<USER>\AppData\Roaming\Unreal Engine\UnrealBuildTool\SSHKeys\***********\H
    securityCmd = "'security unlock-keychain -p 1234 login.keychain'"
    remotePrivateKey = os.path.join(appData, "Unreal Engine", "UnrealBuildTool", "SSHKeys", remoteHost,  remoteUser, remoteKeyName)
    signCmd = "{} -o BatchMode=yes -i '{}' -p 22 {}@{} 'cd {};cd {};{};./CodeSign.sh'".format(sshExe,remotePrivateKey, remoteUser, remoteHost, remoteSignDir, buildParam.projectName, securityCmd)
    ret, msg = ciTools.runScript(signCmd)
    return ret
def rsyncIPAToRemote(buildParam : BuildProjectParam):
    printLog("rsyncIPAToRemote {} {}".format(buildParam.projectName, buildParam.fullProjectOutPutDir))
    ret = False
    rsyncExe = os.path.join(buildParam.engineDir, "Engine", "Extras", "ThirdPartyNotUE", "DeltaCopy", "Binaries", "Rsync.exe")
    sshExe = os.path.join(buildParam.engineDir, "Engine", "Extras", "ThirdPartyNotUE", "DeltaCopy", "Binaries", "Ssh.exe")
    remoteUser = "H"
    remoteHost = "***********"
    remoteKeyName = "RemoteToolChainPrivate.key"
    remoteSignDir = "RemoteIpaSign"
    ipaDir = os.path.join(buildParam.fullProjectOutPutDir, "IOS")
    if not os.path.exists(ipaDir):
        return False
    ipaFileName = os.path.join("{}.ipa".format(buildParam.projectName))
    destSignIpaPath = os.path.join("/Users", "h", remoteSignDir, buildParam.projectName)
    destSignIpaPath = destSignIpaPath.replace("\\", "/")
    os.chdir(ipaDir)
    appData = os.getenv('APPDATA')
    #C:\Users\<USER>\AppData\Roaming\Unreal Engine\UnrealBuildTool\SSHKeys\***********\H
    remotePrivateKey = os.path.join(appData, "Unreal Engine", "UnrealBuildTool", "SSHKeys", remoteHost,  remoteUser, remoteKeyName)
    rsyncCmd = "{} --compress --verbose --rsh=\"{} -i '{}' -p 22\" --chmod=ugo=rwx --recursive --delete --delete-excluded --times --omit-dir-times --prune-empty-dirs --rsync-path=\"rsync\" {} \"{}@{}\":'{}'".format(rsyncExe, sshExe, remotePrivateKey, ipaFileName, remoteUser, remoteHost, destSignIpaPath)
    #rsyncCmd = "{} --compress --rsh=\"./ssh -i '{}' -p 22\" --rsync-path=\"rsync\" {} {}@{}:{}".format(rsyncExe,remotePrivateKey, srcIpaPath, remoteUser, remoteHost, destSignIpaPath)
    ret, msg = ciTools.runScript(rsyncCmd)
    return ret

def rsyncIPAToLocal(buildParam : BuildProjectParam):
    printLog("rsyncIPAToLocal=" + buildParam.fullProjectOutPutDir)
    if not os.path.exists(buildParam.fullProjectOutPutDir):
        printLog("rsyncIPAToLocal error {} exist".format(buildParam.fullProjectOutPutDir))
        return False
    ret = False
    rsyncExe = os.path.join(buildParam.engineDir, "Engine", "Extras", "ThirdPartyNotUE", "DeltaCopy", "Binaries", "Rsync.exe")
    sshExe = os.path.join(buildParam.engineDir, "Engine", "Extras", "ThirdPartyNotUE", "DeltaCopy", "Binaries", "Ssh.exe")
    remoteUser = "H"
    remoteHost = "***********"
    remoteKeyName = "RemoteToolChainPrivate.key"
    remoteSignDir = "RemoteIpaSign"
    ipaDir = os.path.join(buildParam.fullProjectOutPutDir, "IOS")
    if not os.path.exists(ipaDir):
        os.mkdir(ipaDir)
    ipaFileName = "{}_Sign.ipa".format(buildParam.projectName)
    remoteIpaPath = os.path.join("/Users", "h", remoteSignDir, buildParam.projectName, buildParam.projectName+"_Sign.ipa")
    remoteIpaPath = remoteIpaPath.replace("\\", "/")
    #os.chdir("C:\\work\\slimo\\iosIpa")
    os.chdir(ipaDir)
    appData = os.getenv('APPDATA')
    #C:\Users\<USER>\AppData\Roaming\Unreal Engine\UnrealBuildTool\SSHKeys\***********\H
    remotePrivateKey = os.path.join(appData, "Unreal Engine", "UnrealBuildTool", "SSHKeys", remoteHost,  remoteUser, remoteKeyName)
    rsyncCmd = "{} --compress --verbose --rsh=\"{} -i '{}' -p 22\" --chmod=ugo=rwx --recursive --delete --delete-excluded --times --omit-dir-times --prune-empty-dirs --rsync-path=\"rsync\" \"{}@{}\":'{}' '{}'".format(rsyncExe, sshExe, remotePrivateKey, remoteUser, remoteHost, remoteIpaPath, ipaFileName)
    #rsyncCmd = "{} --compress --rsh=\"./ssh -i '{}' -p 22\" --rsync-path=\"rsync\" {} {}@{}:{}".format(rsyncExe,remotePrivateKey, srcIpaPath, remoteUser, remoteHost, destSignIpaPath)
    ret, msg = ciTools.runScript(rsyncCmd)
    return ret 

def saveIosDsym(buildParam : BuildProjectParam):
    printLog("saveIosDsym {} {}".format(buildParam.projectName, buildParam.fullProjectOutPutDir))
    srcDsymPath = os.path.join(buildParam.projectSourceDir, "Binaries", "IOS", "{}.dSYM".format(buildParam.projectName))
    srcDsymZipPath = os.path.join(buildParam.projectSourceDir, "Binaries", "IOS", "{}.dSYM.zip".format(buildParam.projectName))
    if os.path.exists(srcDsymPath):
        destDsymPath = os.path.join(buildParam.fullProjectOutPutDir, "IOS", "{}.dSYM".format(buildParam.projectName))
        ciTools.copyFileByShutil(srcDsymPath, destDsymPath)
    
    if os.path.exists(srcDsymZipPath):
        destDsymZipPath = os.path.join(buildParam.fullProjectOutPutDir, "IOS","{}.dSYM.zip".format(buildParam.projectName))
        ciTools.copyFileByShutil(srcDsymZipPath, destDsymZipPath)
    
def build(buildParam : BuildProjectParam):
    ciTools.updateBuildParams(buildParam)
    ciTools.printBuildParam(buildParam, "after updateBuildParams")
    hasUbtRunning = ciTools.checkUBTProcess()
    if hasUbtRunning:
        stopCustomPak(buildParam)
    # 清理僵尸任务
    ciTools.kill_process_by_arguments(['buildProject5.py', 'buildRoomPreview.py', 'buildEngine5.py', 'buildPluginNew.py'])
    if buildParam.branch is None or len(buildParam.branch.strip()) < 1:
        buildParam.buildResultCode = 2
        buildParam.buildResultMsg = "branch is Empty"
        return False
    
    getBuildOutputDirInfo(buildParam)
    if not buildParam.bOnlyVideo:
        
        ret = getEningeInfo(buildParam)
        if ret == False:
            return False
        setEngineInfo(buildParam)
        ciTools.printBuildParam(buildParam, "after getEngine")
        getProjectBuildInfo(buildParam)
        # 清理本地修改, 包括content下的内容
        resetConfig(buildParam)
        resetContent(buildParam)
        ciTools.resetProjectBranch(buildParam.projectSourceDir)
        if buildParam.bVRAndroidCiForEngine == True and buildParam.engineInstalled == False:
            ciTools.resetProjectBranch(buildParam.engineDir)
        
        if buildParam.bVRAndroidCiForEngine:
            buildParam.pullEngineStartTime = datetime.now()
            ret = handleEngineBranchInfo(buildParam)#引擎额外
            if ret == False:
                return False
        
        ciTools.getEngineCommitInfo(buildParam, buildParam.engineDir, buildParam.engineBranch)

        if buildParam.bForceClearAllLocalCache:
            clearOldCache(buildParam)
        else:
            if buildParam.lastBuildEngineInfo != f"{buildParam.engineBranch}_{buildParam.engineCommitId}":
                clearOldCache(buildParam)
            else:
                # 根据变更大小，决定是否清理缓存
                if ciTools.getProjectDiff(buildParam.projectSourceDir, buildParam.branch):
                    clearOldCache(buildParam)
        
        buildParam.pullProjectStartTime = datetime.now()
        ret = handleProjectBranchInfo(buildParam)
        if ret == False:
            return False
        
        if buildParam.bUseSharedDDC:
            buildParam.sharedDDCPath = f"{buildParam.sharedDDCPathPrefix}{buildParam.engineBranch.replace('/', '_')}_{buildParam.engineCommitId}/common"
            if buildParam.bUseIndependentSharedDDC:
                buildParam.sharedDDCPath = f"{buildParam.sharedDDCPathPrefix}{buildParam.engineBranch.replace('/', '_')}_{buildParam.engineCommitId}/{buildParam.branch.replace('/','_')}/{buildParam.xsdkAppId}_{buildParam.xsdkReleaseId}"
        
        getProjectUploadPath(buildParam)
        getCaptureCosInfo(buildParam)
        updateProjectBuildInfo(buildParam)
        if buildParam.bUseSharedDDC:
            os.makedirs(buildParam.sharedDDCPath, exist_ok=True)
            #修改defaultEngine.ini，配置ddc路径、多层缓存检索、异步写入
            enableSharedDDC(buildParam)

        ciTools.printBuildParam(buildParam, "before build")
    return buildProject(buildParam)

def setBuildTimeInfo(buildParam : BuildProjectParam):
    currentYMDHMS = ciTools.currentYMDHMS()
    buildParam.buildTime = currentYMDHMS
    buildParam.taskStartTime = ciTools.getCurrentDateTimeStr1()
    buildParam.creatTime = datetime.now()

def buildProjectMain(buildParam : BuildProjectParam):
    buildListenScheduler = BackgroundScheduler()
    if buildParam.bVRAndroidCi:
        startTime = time.time()
        taskInfo = f"{buildParam.xsdkAppId}_{buildParam.xsdkReleaseId}_{buildParam.branch}:{buildParam.projectBuildUrl}"
        buildListenScheduler.add_job(ciTools.buildTimeoutNotify, 'interval', minutes=30, id=taskInfo, args=[taskInfo, startTime])
        buildListenScheduler.start()
    setBuildTimeInfo(buildParam)
    ciTools.updataParamsAfterUserSetting(buildParam)
    ret = build(buildParam)
    if ret == False:
        stopCustomPak(buildParam)
        if buildParam.clearDataWhenBuildFail == True:
            ciTools.removeDir(buildParam.fullProjectOutPutDir)
            ciTools.removeFile(buildParam.fullProjectOutPutZipPath)
            ciTools.removeDir(buildParam.panoVideoDstDir)
        ciTools.sendBuildResultMessage(buildParam)
        # if buildParam.embedExitWhenBuildFail == True:
        #     ciTools.stopProcess(buildParam.buildResultMsg)
        buildInfoRecord = OTAStorage()
        buildAllInfo = (buildParam.branch, buildParam.commitId, buildParam.engineBranch, buildParam.engineCommitId,
                buildParam.xsdkAppId, buildParam.xsdkReleaseId, buildParam.pakMode, buildParam.xsdkEnv, buildParam.xsdkVersion, buildParam.locateType,
                buildParam.xsdkMarkerFile, buildParam.xsdkLowModelVersion, buildParam.xsdkLowModelPath, buildParam.xsdkSceneId,
                buildParam.videoName, buildParam.storeName, buildParam.packageName, buildParam.applicationDisplayName,
                buildParam.storeVersion, buildParam.versionDisplayName, buildParam.projectSambaDirForNotify, buildParam.projectBuildUrl,
                buildParam.apkCosPath, buildParam.pakCosPath, buildParam.videoCosPath, buildParam.lowModelPakCosPath,
                buildParam.ibrAssetSize, buildParam.pbrAssetSize, buildParam.panoramicVideoSize, buildParam.apkSize, buildParam.pakSize, buildParam.lowmodelPakSize,
                buildParam.creatTime, buildParam.pullProjectStartTime, buildParam.pullEngineStartTime, buildParam.downloadAssetStartTime,
                buildParam.buildStartTime, buildParam.custompakStartTime, buildParam.sambaUploadStartTime, buildParam.otaUploadStartTime,    
                buildParam.executor, buildParam.buildResultCode, buildParam.buildResultMsg)
        insertRet, msg = buildInfoRecord.upsertBuildParameterInfo(buildAllInfo)
        if not insertRet:
            cosUtil.sendDevMessage(f'{buildParam.projectBuildUrl}: 写入build 参数和统计信息失败,{msg}\n {buildAllInfo}')

    printLog("buildProjectMain build ret {}".format(ret))
    # ciTools.sendLogFileMessage(buildParam.customPakLogMediaId)
    buildListenScheduler.shutdown()
    return ret
if __name__ == "__main__":
    cwd = os.getcwd()
    printLog("buildProject cwd=" + cwd)
    BinPath = sys.argv[0]
    printLog("buildProject BinPath=" + BinPath)
    buildParam = BuildProjectParam()
    readParams(sys.argv, buildParam)
    if buildParam.embedLocalTest == True:
        buildParam.projectName = "XTravelDog"
        buildParam.targetPlatform = "Android"
        buildParam.cookflavor = "ASTC"
        buildParam.branch = "dev"
        buildParam.engineBranch = "xstudio"
        buildParam.embedGitClean = False
        buildParam.embedCleanOldCache = False
        buildParam.gameConfigurations = "Development"
        #buildParam.engineDir = "C:\\work\\xverse\\ArtEngineOutput\\XVerseEngine-20230829-153937"
        buildParam.distribution = False
        buildParam.embedPullLatestCode = True
        buildParam.forceResetLocalBranch = True
        buildParam.embedSwitchBranch = True
        buildParam.useDeploy = False
        buildParam.compressed = False
        buildParam.apkDebugSign = False
        buildParam.notifyForTest = True
        buildParam.apkPublishGoogle = True
        buildParam.buildWithBundle = True
        buildParam.googlePAD = False
        buildParam.storeVersion = 6
        buildParam.engineForTest = False
        buildParam.createBuildProjectWithGen = True
        buildParam.xsdkAppId = "11088"
        buildParam.xsdkReleaseId = "2309142121_4e7db9"
        buildParam.packageWithLocalAsset = True
    buildResult = buildProjectMain(buildParam)
    printLog(f"buildResult={buildResult}")
        

