#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import requests
import socket
import datetime
import cosUtil

def printLog(msg):
    printLogTag("cosUtil", msg)

def printLogTag(tag, msg):
    cdate = datetime.datetime.now()
    date_time = cdate.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    log = "{} {} -- {}".format(date_time,tag, msg)
    print(log, flush=True)

def testNotify():
    msg : cosUtil.BuildNotifyMessage = cosUtil.BuildNotifyMessage()
    msg.forTest = True
    # msg.mentionedMobileList = ["17620336602"]
    msg.mentionedList = ["唐立"]
    msg.title = "測試Mentioned"
    cosUtil.sendWeixinTipsMessage(msg)
if __name__ == "__main__":
    testNotify()
