#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import os
import datetime
import shutil
import glob
import subprocess
import cosUtil
import BuildHelper
from collections import namedtuple
import codecs
import psutil
import time
from multiprocessing import Process
import xverseUtil
from pathlib import Path
import re

logTAG = "CiTools"


# sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
# sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

class XCiGlobalConfig:
    cosCliHome = "C:\\work\\xverse\\PackageTools\\CosTools"
    packageToolDir = "C:\\work\\xverse\\PackageTools\\PackageTool"
    projectSourceDir = "C:\\work\\xverse\\XVerseStudio"
    engineSourceDir = "C:\\work\\xverse\\UnrealEngine"
    engineOutPutDir = "C:\\work\\xverse\\EngineOutPut"
    engineOutPutDirForArtPlugin = "C:\\work\\xverse\\ArtEngineOutPut"
    engineOutPutDirForTest = "E:\\work\\xverse\\EngineOutPutForTest"
    engineSambaDir = "\\\\CreatorSamba\\XverseCreator\\XCreatorEngine"
    pluginOutPutDir = "C:\\work\\xverse\\PluginOutput\\XVerseEnginePlugins"
    projectOutPutDir = "C:\\work\\xverse\\ProjectOutput"
    panoVideoDstDir = "C:\\work\\xverse\\PanoVideo"

    def __init__(self):
        self.currentTaskUid = 0


class CaptureCosInfo:
    StateUploadSucc = 0
    StateUnLoad = 1
    StateUploading = 2
    StateUploadFail = 3
    StateUnknown = -1

    def __init__(self):
        self.cosRegoin = ""
        self.cosPath = ""
        self.projectBranch = ""
        self.engineBranch = ""

        # 0:UploadSucc,1 waitUpload, 2:Uploading,3:UploadFail, -1:unknown error
        self.cosState = -1
        self.commitVersion = 0
        self.consoleEnv = "Release"


class CosUploadStatus:
    StateUploadSuccess = 0
    StateUnLoad = 1
    StateUploading = 2
    StateUploadFail = 3
    StateUnknown = -1


class EngineCosTaskInfo:
    def __init__(self):
        self.cos_region = ""
        self.project_branch = ""
        self.commit_version = ""
        self.cos_path = ""
        self.task_file_path = ""
        self.upload_file_path = ""

        # CosUploadStatus
        self.cos_state = -1
        self.fail_count = 0


class CIConfig:
    ciDataHomeDir = "C:\\work\\xverse\\PackageTools\\CIData"
    cosCliToolHome = XCiGlobalConfig.cosCliHome
    cosBucket = "cos://xverse-creator-sh-1258211750/"
    cosRegoin = "cos.ap-shanghai.myqcloud.com"

    # cosBucket = "cos://xverse-creator-1258211750/"
    # cosRegoin = "cos.ap-guangzhou.myqcloud.com"
    uploadCosTasksDir = os.path.join(ciDataHomeDir, "CosUploadTasks")
    xCaptureHistoryDir = os.path.join(ciDataHomeDir, "XCaptureHistory")
    xBackProcessPidPath = os.path.join(ciDataHomeDir, "xverseBackProcess.pid")
    xCiGlobalConfigPath = os.path.join(ciDataHomeDir, "xCiGlobalConfig.conf")
    xCiBackProcessLogDir = os.path.join(ciDataHomeDir, "xCiBackProcessLogs")
    xCiCaptureTestTaskDir = os.path.join(ciDataHomeDir, "xCiCaptureCheckTasks")

    x_engine_tasks_dir = os.path.join(ciDataHomeDir, "XEngineTasks")

    allowUploadCosProject = ["XCapture", "XStudio"]

    def __init__(self) -> None:
        pass


class CaptureUploadTaskInfo:
    def __init__(self):
        self.cosPath = ""
        self.projectBranch = ""
        self.engineBranch = ""
        self.mainVersion = "1.0"
        self.commitVersion = 0
        self.buildTime = ""
        self.cosState = CaptureCosInfo.StateUnLoad
        self.taskFilePath = ""
        self.uploadFilePath = ""
        self.captureRecordFilePath = ""
        self.projectName = ""
        self.targetPlatform = ""
        self.taskUid = 0
        self.uploadCosFailCount = 0


class CaptureTestTaskInfo(CaptureUploadTaskInfo):
    StateTestSucc = 0
    StateDownloadSkining = 1
    StateDownloadSkinFail = 2
    StateDownloadSkinSucc = 3
    StateDownloadPackageing = 10
    StateDownloadPackageFail = 11
    StateDownloadPackageSucc = 12
    StateTesting = 20
    StateTestFail = 21
    StateUnknown = -1

    def __init__(self):
        super(CaptureTestTaskInfo, self).__init__()
        self.appId = ""
        self.roomId = ""
        self.skinId = ""
        self.branchId = ""
        self.testState = CaptureTestTaskInfo.StateUnknown
        self.errCode = 0
        self.errMsg = ""


class BranchInfo:
    def __init__(self):
        self.msg = "OK"
        self.commitId = ""
        self.commitVersion = ""
        self.commitInfo = ""
        self.branch = ""
        self.commitUser = None
        self.commitDate = None
        self.author = None
        self.authorDate = None


class BaseBuildParam:

    def __init__(self):
        self.channel = "用户编译"
        self.engineSearchDir = XCiGlobalConfig.engineOutPutDir
        self.engineDir = ""
        self.engineName = ""
        self.engineZipFile = ""
        self.gameConfigurations = "Development"
        self.targetPlatform = "Win64"
        self.genericPlatformName = None
        self.projectArchiveName = None
        self.embedUploadCos = False
        self.embedCaptureUploadCos = True
        self.otaVersion = ""
        self.remarks = "daily task"
        self.embedSymStore = True
        self.embedUploadSamba = False
        self.embedGitClean = False
        self.embedPullLatestCode = True
        self.embedSwitchBranch = True
        self.forceResetLocalBranch = False
        self.embedCleanOldCache = False
        self.projectName = ""
        self.projectDir = None
        self.projectSourceDir = XCiGlobalConfig.projectSourceDir
        self.projectOutPutDir = XCiGlobalConfig.projectOutPutDir
        self.projectSambaDir = ""
        self.projectSambaDirForNotify = ""
        self.projectCosPath = ""
        self.projectBranch = ""
        self.packageToolDir = XCiGlobalConfig.packageToolDir
        self.outputFileName = ""
        self.fullProjectOutPutDir = ""
        self.fullProjectOutPutZipPath = ""
        self.projectOutPutZipName = ""
        self.commitId = ""
        self.commitVersion = ""
        self.commitInfo = ""
        self.commitUser = None
        self.commitDate = None
        self.author = None
        self.authorDate = None
        self.branch = "dev"
        self.buildTime = ""
        self.taskStartTime = ""
        self.mainVersion = "1.0"
        self.fullVersion = ""
        self.engineBranch = "xstudio"
        self.engineCommitId = ""
        self.engineCommitVersion = ""
        self.engineCommitInfo = ""
        self.engineCommitUser: str = None
        self.engineCommitDate: str = None
        self.engineAuthor: str = None
        self.engineAuthorDate: str = None
        self.projectDescribeName = ""
        self.projectNotifyTitle = ""
        self.projectNameForNotify: str = None
        self.projectPlatformForNotify = ""
        self.projectCosName = ""
        self.cosCliHome = XCiGlobalConfig.cosCliHome
        self.compileCacheDir = ""
        self.projectBuildUrl = None
        self.projectLocalBuildLogPath = None
        self.projectRemoteBuildLogPath = None
        self.buildResultCode = -1
        self.buildResultMsg = "UNKNOWN"
        self.embedUploadSymbol = True
        self.embedRemoveSymbol = False
        self.xCaptureCosPath = ""
        self.embedSendNoticeMessage = True
        self.disableNotifyMessageAtSucc = False
        self.taskUid = None
        self.directionUploadCos = False
        self.stateDescribe = None
        self.cdStateDescribe = None
        self.embedVerifyCapture = False
        self.distribution = False
        self.useDeploy = False
        self.compressed = False
        self.embedLocalTest = False
        self.notifyForTest = False
        self.notifyForEngine = False
        self.executor = None
        self.engineForTest = False
        self.engineInstalled = False
        self.currentStep = None
        self.currentStepState = 0
        self.noticeMessageList = []
        self.embedExitWhenBuildFail = True
        self.removeDataAfterBuild = False
        self.clearDataWhenBuildFail = False
        self.engineMajorVersion = 4
        self.engineMinorVersion = 2
        self.engineBuildVersion = 6
        self.includeConsoleConfig = False
        self.bVRAndroidCi = False
        self.bPrebuild = True
        self.bLatest = False    # 是否最新预编译引擎
        self.bVRAndroidCiForEngine = False
        self.bPreRelease = False
        self.nativeLoadInfoPath = None
        self.pakMode = None
        self.bCustomPak = False
        self.customPakLogMediaId = None
        self.bNeedApk = False
        self.bNeedMainPak = False
        self.bNeedLowmodelPak = False
        self.bOnlyLowmodelPak = False
        self.bNeedVideo = False
        self.bOnlyVideo = False
        self.psoCollectMode = None
        self.bCollectPso = False
        self.bGeneratePsoWorld = False
        
        self.consoleBaseUrl = "https://console-api.xverse.cn/release/console"
        self.consoleAuthToken = "4e3fa26e64d24869ac691578b149a7b7"
        
        self.apkCosPath = None
        self.pakCosPath = None
        self.videoCosPath = None
        self.lowModelPakCosPath = None
        self.backgroundUpload = True

        self.ibrAssetSize = 0
        self.pbrAssetSize = 0
        self.panoramicVideoSize = 0
        self.apkSize = 0
        self.pakSize = 0
        self.lowmodelPakSize = 0
        self.creatTime = None
        self.pullProjectStartTime = None
        self.pullEngineStartTime = None
        self.downloadAssetStartTime = None
        self.buildStartTime = None
        self.custompakStartTime = None
        self.sambaUploadStartTime = None
        self.otaUploadStartTime = None
        self.endTime = None


class BuildProjectParam(BaseBuildParam):
    def __init__(self):
        super(BuildProjectParam, self).__init__()
        self.projectDir = None
        self.projectSourceDir = XCiGlobalConfig.projectSourceDir
        self.panoVideoDstDir = ""
        self.engineSearchDir = XCiGlobalConfig.engineOutPutDir
        self.engineBranch = "xstudio"
        self.branch = "dev"
        self.projectOutPutDir = XCiGlobalConfig.projectOutPutDir
        self.projectName = "XStudio"
        # Dev or Release
        self.consoleEnv = "Release"
        self.embedSwitchBranch = True
        self.embedPullLatestCode = True
        self.forceResetLocalBranch = True
        self.bForceClearAllLocalCache = False
        self.bUseSharedDDC = True
        self.bUseIndependentSharedDDC = False
        self.sharedDDCPath = ''
        self.sharedDDCPathPrefix = ''
        self.lbvrProjectName = None
        self.lastBuildEngineInfo = ''
        self.embedUploadSamba = True
        self.embedUploadCos = False
        self.embedGitClean = False
        self.cookflavor = "ETC2"
        self.targetPlatform = "Win64"
        self.includeCrashReporter = True
        self.includeDebugFiles = True
        self.includePrerequisites = True
        self.includeMountSamba = True
        self.includeXCapture = False
        self.forceBuildXCapture = True
        self.xsdkAppId = None
        self.mockAppId = None
        self.xsdkVersion = None
        self.xsdkEnv = None
        self.xsdkWorldId = None
        self.xsdkReleaseId = None
        self.xsdkPackedAssetVersion = None # pengzhu
        self.locateType = None
        self.anchorConfigJsonData = None
        self.xsdkMarkerFile = None
        self.xsdkLowModelVersion = None
        self.xsdkLowModelPath = None
        self.xsdkSceneId = None
        self.xsdkbForceUseLocalConfig = None

        self.apkDebugSign = False
        self.apkPublishGoogle = False
        self.buildWithBundle = False
        self.DebugKeyStore = None
        self.DebugKeyAlias = None
        self.DebugKeyStorePassword = None
        self.DebugKeyPassword = None
        self.embedProjectCook = True
        self.embedProjectStage = True
        self.embedProjectArchive = True
        self.embedProjectPackage = True
        self.embedProjectPak = True
        self.embedProjectDDC = True
        self.embedProjectInstalled = True
        self.googlePAD = False
        self.videoName = None
        self.storeName = None
        self.bForceUploadOTA = False
        self.bOTA = False
        self.otaOption = ""
        self.otaPath = ""
        self.apkCosPrefix = ""
        self.pakCosPrefix = ""
        self.lowmodelPakCosPrefix = ""
        self.videoCosPrefix = ""
        self.dstStoreId = ""
        self.bRelease = False
        self.packageName = None
        self.applicationDisplayName = None
        self.storeVersion = None
        self.versionDisplayName = None
        self.createBuildProjectWithGen = True
        self.packageWithLocalAsset = False
        self.downloadAssetWithReleaseId: bool = False
        self.downloadAssetWithPathId: bool = False
        self.downloadAssetWithLocalCopy: bool = False
        self.disableAssetUpgrade = False
        self.clearDataWhenBuildFail = True
        self.downloadAssetPathIdList: str = None
        self.downloadAssetResourceVersion: str = None
        self.deviceType = ""


class BuildPluginParam(BaseBuildParam):

    def __init__(self):
        super(BuildPluginParam, self).__init__()
        self.pluginList = []
        self.uprojectPluginList = []
        # split with;
        self.plugins = ""
        self.projectDir = None
        self.projectSourceDir = XCiGlobalConfig.projectSourceDir
        self.engineSearchDir = XCiGlobalConfig.engineOutPutDir
        self.engineBranch = "xstudio"
        self.branch = "dev"
        self.pluginBuildTempDir = ""
        self.projectOutPutDir = XCiGlobalConfig.pluginOutPutDir
        self.projectName = "XStudio"
        self.pluginProjectName = "ArtPlugin"
        self.projectDescribeName = "ArtPlugins"
        self.projectNameForNotify = "美术插件"
        # Dev or Release
        self.consoleEnv = "Release"
        self.includeEngine = False
        self.embedSwitchBranch = True
        self.embedPullLatestCode = True
        self.forceResetLocalBranch = True
        self.embedUploadSamba = True
        self.embedGitClean = True
        self.searchPluginResult = ""
        self.pluginIncludeSource = False
        self.pluginIncludeIntermediate = False
        self.pluginIncludeShipping = False
        self.pluginIncludePdb = True
        self.enablePluginList = []
        self.disablePluginList = []


class BuildEngineParam(BaseBuildParam):

    def __init__(self):
        super(BuildEngineParam, self).__init__()
        self.gameConfigurations = "Development;Shipping"
        self.withWin64 = True
        self.withWin32 = False
        self.withLinux = False
        self.withAndroid = False
        self.withIOS = False
        self.withMac = False
        self.withTVOS = False
        self.withLinuxAArch64 = False
        self.withLumin = False
        self.withHoloLens = False
        self.withLinuxArm64 = False
        self.embedSymStore = True
        self.embedUploadSamba = True
        self.embedGitClean = False
        self.embedPullLatestCode = True
        self.embedSwitchBranch = True
        self.embedRemoveSymbol = True
        self.embedCleanOldCache = False
        self.projectDir = None
        self.projectSourceDir = XCiGlobalConfig.engineSourceDir
        self.withFullDebugInfo = False
        self.withDDC = False
        self.branch = "xstudio"
        self.engineBranch: str = None
        self.embedUploadCos = False
        self.includePDB = True
        self.embedUploadSymbol = True
        self.cosCliHome = XCiGlobalConfig.cosCliHome
        self.notifyForEngine = True
        # lock engine avoid been clear
        self.embedEngineLock = False
        # enable engine 
        self.embedEngineWork = True

        self.clearDataWhenBuildFail = True

        self.embedEngineBridgePlugin: bool = True

        self.copyEngineForArtPlugin: bool = False

        self.engineOnlyForArtPlugin: bool = False

        self.artPluginEngineOutputDir: str = XCiGlobalConfig.engineOutPutDirForArtPlugin

        # 240305 注入： Engine COS Path 路径
        self.engine_cos_upload_path = ""


def ciEnvInit():
    if not os.path.exists(CIConfig.ciDataHomeDir):
        os.makedirs(CIConfig.ciDataHomeDir)


def getEngineCommitInfo(buildParam: BaseBuildParam, engineDir, engineBranch):
    printLog("GetEngineCommitInfo {}".format(engineDir))
    engineVersionPath = os.path.join(engineDir, "Engine", "Version", "EngineVersion.ini")
    if os.path.exists(engineVersionPath):
        engineVersionFile = open(engineVersionPath, "r", encoding="utf-8")
        for line in engineVersionFile:
            # printLog("ciTools GetEngineCommitInfo Line=" + line)
            if line.startswith("CommitId:"):
                buildParam.engineCommitId = line[len("CommitId:"):].replace('\n', '').replace('\r', '').strip()
            if line.startswith("CommitVersion:"):
                buildParam.engineCommitVersion = line[len("CommitVersion:"):].replace('\n', '').replace('\r',
                                                                                                        '').strip()
            if line.startswith("CommitInfo:"):
                buildParam.engineCommitInfo = line[len("CommitInfo:"):].replace('\n', '').replace('\r', '')

        engineVersionFile.close()
        return True
    else:
        branchInfo = getBranchInfo(engineDir, engineBranch)
        if branchInfo.msg == "OK":
            buildParam.engineCommitId = branchInfo.commitId
            buildParam.engineCommitVersion = branchInfo.commitVersion
            buildParam.engineCommitInfo = branchInfo.commitInfo
            buildParam.engineCommitUser = branchInfo.commitUser
            buildParam.engineCommitDate = branchInfo.commitDate
            buildParam.engineAuthor = branchInfo.author
            buildParam.engineAuthorDate = branchInfo.authorDate
            return True
        return False


def getProjectCommitInfo(buildParam: BaseBuildParam):
    printLog("getProjectCommitInfo")
    branchInfo = getBranchInfo(buildParam.projectSourceDir, buildParam.branch)
    # printLog("getProjectCommitInfo result={}".format(branchInfo))
    if branchInfo.msg == "OK":
        buildParam.commitId = branchInfo.commitId
        buildParam.commitVersion = branchInfo.commitVersion
        buildParam.commitInfo = branchInfo.commitInfo
        buildParam.commitUser = branchInfo.commitUser
        buildParam.commitDate = branchInfo.commitDate
        buildParam.author = branchInfo.author
        buildParam.authorDate = branchInfo.authorDate
        return True
    return False


def customXGlobalConfigDecoder(xGlobalDict):
    return namedtuple('X', xGlobalDict.keys())(*xGlobalDict.values())


def readXGlobalConfig():
    if not os.path.exists(CIConfig.xCiGlobalConfigPath):
        return None
    obj = XCiGlobalConfig()
    readObjectFromFile(CIConfig.xCiGlobalConfigPath, obj)
    return obj


def writeXGlobalConfig(object: XCiGlobalConfig):
    writeObjectToFile(CIConfig.xCiGlobalConfigPath, object)


def incAndGetTaskUid():
    xGlobalObject = readXGlobalConfig()
    taskUid = 0
    if xGlobalObject is None:
        xGlobalObject = XCiGlobalConfig()
    else:
        taskUid = xGlobalObject.currentTaskUid
    taskUid += 1
    xGlobalObject.currentTaskUid = taskUid
    writeXGlobalConfig(xGlobalObject)
    return taskUid


def printLog(msg):
    printLogTag(logTAG, msg)


def printLogTag(tag, msg):
    cdate = datetime.datetime.now()
    date_time = cdate.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    log = "{} {} -- {}".format(date_time, tag, msg)
    print(log, flush=True)


def getCurrentDateTimeStr1():
    cdate = datetime.datetime.now()
    date_time = cdate.strftime("%Y-%m-%d %H:%M:%S")
    return date_time


def getCurrentDateTimeStr2():
    cdate = datetime.datetime.now()
    date_time = cdate.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    return date_time


def currentYMDHMS():
    cdate = datetime.datetime.now()
    date_time = cdate.strftime("%Y%m%d-%H%M%S")
    return date_time


def currentYMD():
    cdate = datetime.datetime.now()
    date_time = cdate.strftime("%Y%m%d")
    return date_time


def getBranch(targetDir):
    branchInfo = BranchInfo()
    branchInfo.msg = "OK"
    if not os.path.exists(targetDir):
        branchInfo.msg = "targetDir not exist"
        return branchInfo
    gitCmd = "git branch --show-current"
    printLog("gitCmd=" + gitCmd)
    os.chdir("{0}".format(targetDir))
    gitFile = os.path.join(targetDir, ".git")
    if not os.path.exists(gitFile):
        branchInfo.msg = "targetDir not a git dir"
        return branchInfo
    result = os.popen(gitCmd)
    context = result.buffer.read().decode('utf-8')
    for line in context.splitlines():
        printLog("getBranch=" + line)
        branchInfo.branch = line
    return branchInfo


def getBranchInfo(targetDir, branch):
    return getBranchFullInfo(targetDir, branch)


def getBranchSimpleInfo(targetDir, branch):
    commitInfo = BranchInfo()
    commitInfo.branch = branch
    commitInfo.msg = "OK"
    if not os.path.exists(targetDir):
        commitInfo.msg = "targetDir not exist"
        return commitInfo
    gitlogCmd = "git log -1 --oneline"
    printLog("gitlogCmd=" + gitlogCmd)
    os.chdir("{0}".format(targetDir))
    gitFile = os.path.join(targetDir, ".git")
    if not os.path.exists(gitFile):
        commitInfo.msg = "targetDir not a git dir"
        return commitInfo
    result = os.popen(gitlogCmd)
    context = result.buffer.read().decode('utf-8')
    for line in context.splitlines():
        index = line.find(" ")
        commitId = line[:index]
        info = line[index + 1:]
        printLog("getCommitInfo result={} index={} commitId={}".format(line, index, commitId))
        commitInfo.commitId = commitId
        commitInfo.commitInfo = info
    result.close()
    gitVersionCmd = "git rev-list --count --first-parent {0}".format(branch)
    printLog("gitVersionCmd=" + gitVersionCmd)
    result = os.popen(gitVersionCmd)
    context = result.buffer.read().decode('utf-8')
    for line in context.splitlines():
        printLog("gitVersionCmd=" + line)
        commitInfo.commitVersion = line
    result.close()
    return commitInfo


def getBranchFullInfo(targetDir, branch):
    branchInfo = BranchInfo()
    branchInfo.branch = branch
    branchInfo.msg = "OK"
    if not os.path.exists(targetDir):
        branchInfo.msg = "targetDir not exist"
        return branchInfo
    gitlogCmd = "git log -1 --pretty=fuller"
    printLog("gitlogCmd=" + gitlogCmd)
    os.chdir(targetDir)
    gitFile = os.path.join(targetDir, ".git")
    if not os.path.exists(gitFile):
        branchInfo.msg = "targetDir not a git dir"
        return branchInfo
    result = os.popen(gitlogCmd)
    context = result.buffer.read().decode('utf-8')
    commitInfoList = []
    for line in context.splitlines():
        startWithSpace = line.startswith(" ")
        newLine = line.strip()
        if newLine.startswith("commit"):
            commitList = newLine.split(" ")
            commitId = commitList[1][:8]
            branchInfo.commitId = commitId
        elif newLine.startswith("Author:"):
            data = newLine[7:]
            branchInfo.author = data.strip()
        elif newLine.startswith("AuthorDate:"):
            data = newLine[11:]
            branchInfo.authorDate = data.strip()
        elif newLine.startswith("Commit:"):
            commitUser = newLine[7:]
            branchInfo.commitUser = commitUser.strip()
        elif newLine.startswith("CommitDate:"):
            commitDate = newLine[11:]
            branchInfo.commitDate = commitDate.strip()
        elif startWithSpace:
            commitInfoList.append(newLine)
    commitInfo = ""
    for i in reversed(commitInfoList):
        temp = commitInfo + i
        if len(temp) > 200:
            break
        commitInfo = "{} {}".format(i, commitInfo)
    branchInfo.commitInfo = commitInfo
    result.close()
    # gitVersionCmd = "git rev-list --count --first-parent {0}".format(branch)
    gitVersionCmd = "git rev-list --count --first-parent HEAD"
    printLog("gitVersionCmd=" + gitVersionCmd)
    result = os.popen(gitVersionCmd)
    context = result.buffer.read().decode('utf-8')
    for line in context.splitlines():
        # printLog("getBranchFullInfo=" + line)
        branchInfo.commitVersion = line
    result.close()
    return branchInfo

# todo pz
def branchCheckoutCommitId(projectDir, commitId):
    os.chdir("{0}".format(projectDir))
    ret = os.system("git checkout {0}".format(commitId))
    return ret==0

def branchCheckoutCommitId(projectDir, commitId):
    os.chdir("{0}".format(projectDir))
    ret = os.system("git checkout {0}".format(commitId))
    return ret==0

def resetProjectBranch(projectDir):
    branchInfo = getBranch(projectDir)
    os.chdir("{0}".format(projectDir))
    os.system("git reset --hard")
    printLog("clear {0} {1} local change".format(projectDir, branchInfo.branch))
    # os.system("git checkout .")
    # os.system("git rebase --abort")
    # os.system("git merge --abort")
    # os.system("git reset --hard origin/{0}".format(branchInfo.branch))

def getProjectDiff(projectDir, branch):
    branchInfo = getBranch(projectDir)
    os.system("git fetch")
    diff = subprocess.check_output(["git", "diff", "--stat", branchInfo.branch, f"origin/{branch}"], text=True)
    if re.search('.cpp|.h|.uasset|.hpp|.uplugin|.Build.cs|.Target.cs', diff):
        return True
    return False


def switchBranch(targetDir, branch):
    printLog("switchBranch {} {}".format(targetDir, branch))
    if not os.path.exists(targetDir):
        printLog("switchBranch error not exist {0}".format(targetDir))
        return False, f"switchBranch error {targetDir} not exist"
    gitFile = os.path.join(targetDir, ".git")
    if not os.path.exists(gitFile):
        printLog("switchBranch error not exist git")
        return False, "switchBranch error not exist git"
    os.chdir("{0}".format(targetDir))

    branchInfo = getBranch(targetDir)
    if branchInfo.branch is None or len(branchInfo.branch) < 1:
        printLog("switchBranch get local branch error")
        return False, "switchBranch get local branch error"
    printLog("switchBranch localBranch={0}, newBranch={1}".format(branchInfo.branch, branch))
    if branchInfo.branch == branch:
        printLog("switchBranch no need switch(this branch already is " + branch + ")")
        return True, ""
    # else:
    printLog("switchBranch start checkout to newBranch={0}".format(branch))
    os.system("git fetch")
    script = "git checkout {0}".format(branch)
    result, msg = runScript(script)
    return result, msg


def pullBranch(targetDir):
    if not os.path.exists(targetDir):
        return False
    gitFile = os.path.join(targetDir, ".git")
    if not os.path.exists(gitFile):
        return False
    os.chdir("{0}".format(targetDir))
    result = os.system("git pull")
    printLog("pullBranch {0} result={1}".format(targetDir, result))
    return result == 0


def gitCheckoutDir(dir):
    if os.path.exists(dir):
        os.chdir(dir)
        script = "git checkout *"
        return runScript(script)[0]
    return False


def printBranchLog(targetDir, logSize=3):
    printLog("printBranchLog {} {}".format(targetDir, logSize))
    if not os.path.exists(targetDir):
        return
    gitFile = os.path.join(targetDir, ".git")
    if not os.path.exists(gitFile):
        return
    os.chdir("{0}".format(targetDir))
    result = os.popen("git log -{0}".format(logSize))
    context = result.buffer.read().decode('utf-8')
    for line in context.splitlines():
        printLog("printBranchLog=" + line)
    result.close()


def copy_dir(dir1, dir2):
    dlist = os.listdir(dir1)
    if not os.path.exists(dir2):
        os.makedirs(dir2)
    for f in dlist:
        file1 = os.path.join(dir1, f)  # 源文件
        file2 = os.path.join(dir2, f)  # 目标文件
        if os.path.isfile(file1):
            shutil.copyfile(file1, file2)
        if os.path.isdir(file1):
            copy_dir(file1, file2)


def copyDirByShutil(dir1, dir2):
    printLog("copyDirByShutil src={}, dest={}".format(dir1, dir2))
    if os.path.exists(dir1):
        shutil.copytree(dir1, dir2, dirs_exist_ok=True)


def copyDirByBz(dir1, dir2):
    printLog("copyDirByBz src={}, dest={}".format(dir1, dir2))
    script = "robocopy /E {0} {1}".format(dir1, dir2)
    print(script)
    os.system(script)


def copyFileByShutil(srcFile, destFile):
    printLog("copyFileByShutil {} to {}".format(srcFile, destFile))
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)
    else:
        printLog("copyFileByShutil error {} not exist".format(srcFile))


def getFullCosPath(cosPath):
    fullCosPath = cosPath
    if not fullCosPath.startswith("cos:"):
        fullCosPath = "{0}{1}".format(CIConfig.cosBucket, cosPath)
    return fullCosPath


# uploadToCosByDefault 上传文件到COS，参数 1 COS 上目标路径， 参数 2 文件路径
def uploadToCosByDefault(cosPath, filePath):
    return uploadToCos(CIConfig.cosCliToolHome, cosPath, filePath)


def uploadToCos(cosCliHome, cosPath, filePath, cosRegoin=None):
    cosSecretId = "AKIDIagW9wbotiOuDGjREo96Al9FAkodlRSH"
    cosSecretKey = "kNlKfGdp67vaMvyPsPhmbppd4npeUjiS"
    fullCosPath = getFullCosPath(cosPath)
    regoin = cosRegoin
    if regoin == None:
        regoin = CIConfig.cosRegoin
    script = "{0}\coscli.exe cp {1} {2} -e {3} -i {4} -k {5}".format(cosCliHome, filePath, fullCosPath, regoin,
                                                                     cosSecretId, cosSecretKey)
    printLog("uploadToCos script=" + script)
    return runScript(script)[0]


def uploadToSamba(sambaDir, filePath, sambaFileName=None):
    sambaPath = sambaDir
    if sambaFileName is not None:
        sambaPath = os.path.join(sambaDir, sambaFileName)
    printLog("uploadToSamba sambaPath={}, filePath={}".format(sambaPath, filePath))
    if not os.path.exists(sambaDir):
        os.makedirs(sambaDir)
    if os.path.exists(filePath):
        shutil.copy(filePath, sambaPath)


def updateBuildParams(buildParam: BaseBuildParam):
    if buildParam.projectDescribeName is None or len(buildParam.projectDescribeName) < 1:
        buildParam.projectDescribeName = buildParam.projectName

    if buildParam.projectNameForNotify is None or len(buildParam.projectNameForNotify) < 1:
        buildParam.projectNameForNotify = buildParam.projectDescribeName

    if buildParam.projectPlatformForNotify is None or len(buildParam.projectPlatformForNotify) < 1:
        buildParam.projectPlatformForNotify = buildParam.targetPlatform

    if buildParam.projectBranch is not None and len(buildParam.projectBranch) > 1:
        buildParam.branch = buildParam.projectBranch

    if buildParam.projectBuildUrl is not None and len(buildParam.projectBuildUrl) > 0:
        buildParam.projectRemoteBuildLogPath = buildParam.projectBuildUrl + "console"

    # buildParam.taskUid = incAndGetTaskUid()
    buildParam.taskUid = None
    compileCacheDir = os.path.join(buildParam.packageToolDir, "CompileCache")
    if not os.path.exists(compileCacheDir):
        os.makedirs(compileCacheDir)
    buildParam.compileCacheDir = compileCacheDir


def readBuildParams(argv, buildParam: BaseBuildParam):
    ciEnvInit()
    xverseUtil.readParams(argv, buildParam)
    if buildParam.projectDir is not None and len(buildParam.projectDir) > 0:
        buildParam.projectSourceDir = buildParam.projectDir


def updataParamsAfterUserSetting(buildParam: BaseBuildParam):
    # if buildParam.projectBuildUrl is not None and len(buildParam.projectBuildUrl) > 0 and buildParam.executor is None:
    #     buildParam.executor = "Jekin用户"
    targetPlatform = buildParam.targetPlatform
    buildParam.genericPlatformName = targetPlatform
    if buildParam.engineMajorVersion >= 5:
        if targetPlatform == "Win64":
            buildParam.genericPlatformName = "Windows"
        elif targetPlatform == "Linux":
            buildParam.genericPlatformName = "Linux"
    elif buildParam.engineMajorVersion == 4:
        if targetPlatform == "Win64":
            buildParam.genericPlatformName = "WindowsNoEditor"
        elif targetPlatform == "Linux":
            buildParam.genericPlatformName = "LinuxNoEditor"

    # reset binaryOutputName for upload cos
    buildParam.projectArchiveName = targetPlatform
    if targetPlatform == "Win64":
        buildParam.projectArchiveName = "WindowsNoEditor"
    elif targetPlatform == "Linux":
        buildParam.projectArchiveName = "LinuxNoEditor"


# WindowsNoEditor
# LinuxNoEditor
def setProjectUploadPath(buildParam: BuildProjectParam):
    buildParam.fullVersion = "{}.{}".format(buildParam.mainVersion, buildParam.commitVersion)
    buildParam.projectCosName = buildParam.projectName
    branchName = buildParam.branch.replace("/", "_")
    buildParam.projectSambaDir = "\\\\CreatorSamba\\XverseCreator\\{}\\{}\\{}_{}_{}.{}".format(buildParam.projectName,
                                                                                               buildParam.targetPlatform,
                                                                                               buildParam.outputFileName,
                                                                                               branchName,
                                                                                               buildParam.mainVersion,
                                                                                               buildParam.commitVersion)
    if buildParam.bVRAndroidCi == True:
        pakType = 'full'
        if buildParam.bOnlyLowmodelPak:
            pakType = 'lowModel'
        if buildParam.storeName:
            
            buildParam.projectSambaDir = "\\\\DDCache\\CIPackage\\{}\\{}\\{}\\{}\\{}_{}_{}.{}".format(buildParam.projectName,
                                                                                               buildParam.targetPlatform,
                                                                                               buildParam.storeName,
                                                                                               pakType,
                                                                                               buildParam.outputFileName,
                                                                                               branchName,
                                                                                               buildParam.mainVersion,
                                                                                               buildParam.commitVersion)
            buildParam.projectSambaDir = str(Path(buildParam.projectSambaDir).resolve())
            buildParam.projectSambaDirForNotify = buildParam.projectSambaDir.replace("\\\\DDCache", "http://**********").replace("\\","/")
            # buildParam.projectSambaDirForNotify = "http://**********/CIPackage/{}/{}/{}/{}/{}_{}_{}.{}".format(buildParam.projectName,
            #                                                                                    buildParam.targetPlatform,
            #                                                                                    buildParam.storeName,
            #                                                                                    pakType,
            #                                                                                    buildParam.outputFileName,
            #                                                                                    branchName,
            #                                                                                    buildParam.mainVersion,
            #                                                                                    buildParam.commitVersion)
        else:
            buildParam.projectSambaDir = "\\\\DDCache\\CIPackage\\{}\\{}\\{}\\{}_{}_{}.{}".format(buildParam.projectName,
                                                                                               buildParam.targetPlatform,
                                                                                               pakType,
                                                                                               buildParam.outputFileName,
                                                                                               branchName,
                                                                                               buildParam.mainVersion,
                                                                                               buildParam.commitVersion)
            buildParam.projectSambaDirForNotify = "http://**********/CIPackage/{}/{}/{}/{}_{}_{}.{}".format(buildParam.projectName,
                                                                                               buildParam.targetPlatform,
                                                                                               pakType,
                                                                                               buildParam.outputFileName,
                                                                                               branchName,
                                                                                               buildParam.mainVersion,
                                                                                               buildParam.commitVersion)
    buildParam.projectCosPath = f"{buildParam.projectArchiveName}/{branchName}_{buildParam.commitVersion}/{buildParam.mainVersion}.{buildParam.commitVersion}/{buildParam.projectCosName}.zip"


def uploadProjecSymbol(buildParam: BaseBuildParam):
    printLog("uploadProjecSymbol {}".format(buildParam.projectName))
    runUATBin = os.path.join(buildParam.engineDir, "Engine", "Build", "BatchFiles", "RunUAT.bat")
    scriptFile = os.path.join(buildParam.packageToolDir, "PackageProjectBuild.xml")
    uploadSymbolCmd1 = "{} BuildGraph -target=\"Make Installed Build Win64\" -script={} -set:EmbedSymStore={}".format(
        runUATBin, scriptFile, buildParam.embedUploadSymbol)
    uploadSymbolCmd2 = "-set:XProjectDir={} -set:XProjectName={} -set:XPlatform={} -set:EmbedRemoveSymbol={}".format(
        buildParam.fullProjectOutPutDir, buildParam.projectName, buildParam.targetPlatform,
        buildParam.embedRemoveSymbol)
    uploadSymbolCmd = "{} {}".format(uploadSymbolCmd1, uploadSymbolCmd2)
    return runScript(uploadSymbolCmd)


# remove symbo file
# ignorelist with file name ,e.g tbb.pdb
def removeSymbol(path, ignoreList: list = None):
    printLog("removeSymbol {}".format(path))
    removeFilesWithType(path, ".pdb", ignoreList)
    removeFilesWithType(path, ".debug", ignoreList)


def printBuildParam(buildParam: BaseBuildParam, reason="None"):
    printLog("printBuildParam {} ".format(reason))
    attr = [a for a in dir(buildParam) if not a.startswith('__')]
    for a in attr:
        printLog("{0}={1}".format(a, getattr(buildParam, a)))


def printObject(obj: object, reason="None"):
    printLog("printObject {}".format(reason))
    attr = [a for a in dir(obj) if not a.startswith('__')]
    for a in attr:
        printLog("{0}={1}".format(a, getattr(obj, a)))


def clearProjectIntermediate(projectDir):
    intermediateDir = os.path.join(projectDir, "Intermediate")
    printLog("clearProjectIntermediate Project Intermediate {}".format(intermediateDir))
    if os.path.exists(intermediateDir):
        removeDir(intermediateDir)

def clearProjectContent(projectDir):
    contentDir = os.path.join(projectDir, "Content")
    printLog("clearProject content {}".format(contentDir))
    if os.path.exists(contentDir):
        os.chdir(contentDir)
        os.system("git clean -dxf")

def gitClean(targetDir):
    printLog("gitClean")
    os.chdir(targetDir)
    os.system("git clean -f -x -d")


def deletePlguinsIntermediate(targetDir):
    printLog("deletePlguinsIntermediate")
    os.chdir(targetDir)


def deletePluginsBinarys(targetDir):
    printLog("deletePluginsBinarys")
    os.chdir(targetDir)


def removeFiles(path):
    printLog("removeFiles %s" % path)
    if os.path.exists(file):
        files = glob.glob(path)
        for file in files:
            os.remove(file)


# path *.py
def removeFilesWithType(path: str, fileType: str, ignoreList: list = None):
    printLog("removeFilesWithType %s" % path)
    if fileType is None:
        return False
    if os.path.exists(path):
        fullPath = os.path.join(path, "**/*%s" % fileType)
        files = glob.glob(fullPath, recursive=True)
        for file in files:
            try:
                fileName = getFileName(file)
                if ignoreList is not None and fileName in ignoreList:
                    printLog("removeFilesWithType ignore file {}".format(file))
                    continue
                os.remove(file)
            except Exception as e:
                printLog("removeFilesWithType error {}".format(file))
    return True


def removeFile(file):
    printLog("removeFile %s" % file)
    if os.path.exists(file):
        os.remove(file)


def removeDir(dir):
    printLog("removeDir %s" % dir)
    if os.path.exists(dir):
        shutil.rmtree(dir, ignore_errors=True, onerror=None)


def stopProcess(reason="None", withFail=True):
    printLog("stopProcess reason {}".format(reason))
    # if withFail == True:
    #     sys.exit(1)
    # else:
    #     sys.exit()


# current file in dir path :/user/dir/dir1 return /user/dir
def getFileName(path):
    return os.path.basename(path)


# parent dir with current path :/user/dir/dir1 return /user/dir
def getPrevDirPath(path):
    return os.path.dirname(path)


def runScriptByOutFile(script, outFilePath=None):
    fpath = outFilePath
    if fpath is None or len(fpath) < 1:
        compileDir = os.getenv('APPDATA')
        if not os.path.exists(compileDir):
            os.makedirs(compileDir)
        fpath = os.path.join(compileDir, "xverseCiBuildOutFile.txt")

    if os.path.exists(fpath):
        os.remove(fpath)
    printLog("runScriptByOutFile outFile {}".format(fpath))
    fhandle = open(fpath, "a+")
    pipe = subprocess.Popen(script, shell=True, stdout=fhandle, stderr=fhandle, bufsize=1, universal_newlines=True)
    retCode = pipe.wait()
    printLog("runScriptByOutFile result {}".format(retCode))
    return retCode == 0


def runScript(script):
    printLog("runScript script {}".format(script))
    # retCode = os.system(script)
    p = subprocess.Popen(script, shell=True)
    while True:
        if not is_parent_alive():
            p.terminate()
            p.wait()
            printLog("任务手动取消")
            return False, "任务取消"
        retCode = p.poll()
        if retCode is not None:
            if retCode == 0:
                return True, ""
            else:
                return False, f"{script}执行失败"

def runScriptNoBlock(script):
    printLog("runScript result {}".format(script))
    subprocess.Popen(script, shell=True)
    return True

def is_parent_alive():
    try:
        parent = psutil.Process(os.getppid())
        return parent.is_running()
    except psutil.NoSuchProcess:
        return False

def runScriptWithLog(script, log_path):
    printLog("runScript result {}".format(script))
    subprocess.Popen(script, shell=True)
    while True:
        if not is_parent_alive():
            return False, "任务取消"
        if os.path.exists(log_path):
            break
    with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
        while True:
            if not is_parent_alive():
                return False, "任务取消"
            line = f.readline().strip()
            if line and not line.startswith("LogUdpMessaging"):
                printLog(line)
                # 检查是否包含取消或失败的关键字
                if "BuildCookRun Canceled/Failed" in line or "Error: 打包失败" in line:
                    printLog("Detected 'BuildCookRun Canceled/Failed' in the log, buildProject customPak failed")
                    return False, 'custompak失败'
                elif "CustomPak Total Success" in line:
                    return True, ''
    
def runScriptRet(script):
    printLog("runScriptRet script {}".format(script))
    retCode = os.system(script)
    printLog("runScriptRet result {}".format(retCode))
    return retCode


def runScriptByStdOut(script):
    printLog("runScriptByStdOut script {}".format(script))
    pipe = subprocess.Popen(script, shell=True, stdout=None, stderr=None)
    retCode = pipe.wait()
    printLog("runScriptByStdOut result {}".format(retCode))
    return retCode == 0

def buildTimeoutNotify(task_info, start_time):
    timeCost = datetime.timedelta(seconds=time.time() - start_time)
    total_minutes = int(timeCost.total_seconds() // 60)

    days, remainder = divmod(total_minutes, 1440)  # 1440 分钟 = 1 天
    hours, minutes = divmod(remainder, 60)

    parts = []
    if days:
        parts.append(f"{days}天")
    if hours:
        parts.append(f"{hours}小时")
    if minutes:
        parts.append(f"{minutes}分钟")
        
    readableTimeCost = ', '.join(parts) if parts else "0分钟"
    msg = f"{task_info} 已经执行了{readableTimeCost}"
    cosUtil.seneSimpleMessage(msg)
    

def sendBuildResultMessage(buildParam: BaseBuildParam):
    if buildParam.embedSendNoticeMessage == False:
        printLog("sendBuildResultMessage not embedNotice {}".format(buildParam.projectName))
        return False

    if buildParam.buildResultCode == 0 and buildParam.disableNotifyMessageAtSucc == True:
        printLog("sendBuildResultMessage had disableNotifyMessageAtSucc {}".format(buildParam.projectName))
        return False
    msg = cosUtil.BuildNotifyMessage()
    if buildParam.projectNotifyTitle is None or len(buildParam.projectNotifyTitle) < 1:
        retDesc = "成功"
        if buildParam.buildResultCode != 0:
            retDesc = "失败"
        msg.title = "{}({})打包{}".format(buildParam.projectNameForNotify, buildParam.projectPlatformForNotify, retDesc)
    else:
        msg.title = buildParam.projectNotifyTitle
    msg.buildTime = buildParam.taskStartTime
    if buildParam.buildResultCode == 0:
        msg.outPutPath = buildParam.projectSambaDirForNotify
        msg.apkCosPath = buildParam.apkCosPath
        msg.pakCosPath = buildParam.pakCosPath
        msg.videoCosPath = buildParam.videoCosPath
        msg.lowModelPakCosPath = buildParam.lowModelPakCosPath

    # commit info
    if buildParam.noticeMessageList is None:
        buildParam.noticeMessageList = []

    if buildParam.commitId is not None  and len(buildParam.commitId)>1:
        buildParam.noticeMessageList.append("提交ID:%s" % buildParam.commitId)

    if buildParam.commitUser is not None:
        buildParam.noticeMessageList.append("提交用户:%s" % buildParam.commitUser)

    if buildParam.commitDate is not None:
        buildParam.noticeMessageList.append("提交时间:%s" % buildParam.commitDate)

    if buildParam.commitInfo is not None:
        commitInfo = buildParam.commitInfo
        if len(buildParam.commitInfo) > 120:
            commitInfo = buildParam.commitInfo[0: 120]
        buildParam.noticeMessageList.append("提交日志:%s" % commitInfo)
    
     # 引擎信息
    buildParam.noticeMessageList.append(" ")
    buildParam.noticeMessageList.append("【引擎版本信息】")
    if buildParam.engineBranch is not None  and len(buildParam.engineBranch)>1:
        buildParam.noticeMessageList.append("分支:%s" % buildParam.engineBranch)

    if buildParam.engineCommitId is not None  and len(buildParam.engineCommitId)>1:
        buildParam.noticeMessageList.append("提交ID:%s" % buildParam.engineCommitId)

    if buildParam.engineCommitUser is not None:
        buildParam.noticeMessageList.append("提交用户:%s" % buildParam.engineCommitUser)

    if buildParam.engineCommitDate is not None:
        buildParam.noticeMessageList.append("提交时间:%s" % buildParam.engineCommitDate)

    if buildParam.engineCommitInfo is not None:
        commitInfo = buildParam.engineCommitInfo
        if len(buildParam.engineCommitInfo) > 120:
            commitInfo = buildParam.engineCommitInfo[0: 120]
        buildParam.noticeMessageList.append("提交日志:%s" % commitInfo)
    
    # 资产信息
    buildParam.noticeMessageList.append(" ")
    buildParam.noticeMessageList.append("【资产信息】")
    if buildParam.xsdkAppId is not None and len(buildParam.xsdkAppId) > 1:
        buildParam.noticeMessageList.append("应用ID:%s" % buildParam.xsdkAppId)
    if buildParam.xsdkReleaseId is not None and len(buildParam.xsdkReleaseId) > 1:
        buildParam.noticeMessageList.append("资产releaseId:%s" % buildParam.xsdkReleaseId)
    
    buildParam.noticeMessageList.append(" ")
    buildParam.noticeMessageList.append("【共享DDC信息】")
    buildParam.noticeMessageList.append("是否使用共享DDC:%s" % buildParam.bUseSharedDDC)
    if buildParam.bUseSharedDDC:
        buildParam.noticeMessageList.append("共享DDC目录:%s" % buildParam.sharedDDCPath)
    
    buildParam.noticeMessageList.append(" ")
    if buildParam.bRelease:
        buildParam.noticeMessageList.append("【CD发布信息】")
        buildParam.noticeMessageList.append("发布状态：%s" % buildParam.cdStateDescribe)

    # common
    msg.gameConfiguration = buildParam.gameConfigurations
    msg.version = buildParam.fullVersion
    msg.localLogPath = buildParam.projectLocalBuildLogPath
    msg.branchName = buildParam.branch
    msg.remoteLogPath = buildParam.projectRemoteBuildLogPath
    msg.errCode = buildParam.buildResultCode
    msg.errMsg = buildParam.buildResultMsg
    msg.taskUid = buildParam.taskUid
    msg.stateDescribe = buildParam.stateDescribe
    msg.executor = buildParam.executor
    msg.forTest = buildParam.notifyForTest
    msg.forEngine = buildParam.notifyForEngine
    msg.channel = buildParam.channel
    msg.extendMessageList = buildParam.noticeMessageList
    msg.forVR = buildParam.bVRAndroidCi
    msg.remarks = buildParam.remarks
    cosUtil.sendWeixinTipsMessage(msg)


def sendLogFileMessage(media_id):
    cosUtil.sendFileMessage(media_id)

# return CaptureCosInfo object
def readCaptureCosInfo(engineBranch, projectBranch, projectCommitVersion, consoleEnv):
    pBranch = projectBranch.replace("/", "_")
    branchDirName = "{}_{}".format(pBranch, engineBranch)
    xcaptureDirPath = os.path.join(CIConfig.xCaptureHistoryDir, branchDirName)
    if not os.path.exists(xcaptureDirPath):
        return None
    fileName = "{}_{}.json".format(projectCommitVersion, consoleEnv)
    filePath = os.path.join(xcaptureDirPath, fileName)
    return readCaptureCosInfoByPath(filePath)


def readCaptureCosInfoByPath(filePath):
    printLog("readCaptureCosInfoByPath file {}".format(filePath))
    if not os.path.exists(filePath):
        return None
    try:
        cInfo = CaptureCosInfo()
        readObjectFromFile(filePath, cInfo)
        return cInfo
    except:
        printLog("readCaptureCosInfoByPath error")
        return None


def writeCaptureCosInfo(filePath, cInfo: CaptureCosInfo):
    printLog("writeCaptureCosInfo file {}".format(filePath))
    writeObjectToFile(filePath, cInfo)
    return filePath


def addUploadCaptureTask(buildParam: BuildProjectParam, captureRecordFilePath=None):
    printLog("addUploadCaptureTask {}".format(buildParam.projectCosName))
    pBranch = buildParam.branch.replace("/", "_")
    xcaptureTaskDirPath = os.path.join(CIConfig.uploadCosTasksDir)
    if not os.path.exists(xcaptureTaskDirPath):
        os.makedirs(xcaptureTaskDirPath)
    fileName = "{}_{}_{}_{}.task".format(buildParam.projectName, pBranch, buildParam.commitVersion,
                                         buildParam.buildTime)
    filePath = os.path.join(xcaptureTaskDirPath, fileName)
    cInfo = CaptureUploadTaskInfo()
    cInfo.cosPath = buildParam.projectCosPath
    cInfo.projectBranch = buildParam.branch
    cInfo.engineBranch = buildParam.engineBranch
    cInfo.mainVersion = buildParam.mainVersion
    cInfo.commitVersion = buildParam.commitVersion
    cInfo.buildTime = buildParam.buildTime
    cInfo.cosState = CaptureCosInfo.StateUnLoad
    cInfo.taskFilePath = filePath
    cInfo.uploadFilePath = buildParam.fullProjectOutPutZipPath
    cInfo.captureRecordFilePath = captureRecordFilePath
    cInfo.projectName = buildParam.projectName
    cInfo.targetPlatform = buildParam.targetPlatform
    cInfo.taskUid = buildParam.taskUid
    cInfo.uploadCosFailCount = 0
    writeObjectToFile(filePath, cInfo)
    return filePath


def updateCaptureTask(captureInfo: CaptureUploadTaskInfo):
    filePath = captureInfo.taskFilePath
    printLog("updateCaptureTask captureInfo {}".format(filePath))
    writeObjectToFile(filePath, captureInfo)


def getNextCaptureTask(allowMaxFailCount=0):
    xcaptureTaskDirPath = os.path.join(CIConfig.uploadCosTasksDir)
    if not os.path.exists(xcaptureTaskDirPath):
        return None
    files = os.listdir(xcaptureTaskDirPath)
    files.sort()
    cInfo = None
    for fpath in files:
        fullPath = os.path.join(xcaptureTaskDirPath, fpath)
        captureObj = CaptureUploadTaskInfo()
        readObjectFromFile(fullPath, captureObj)
        try:
            if captureObj.cosState == CaptureCosInfo.StateUnLoad:
                cInfo = captureObj
                break
            elif captureObj.cosState == CaptureCosInfo.StateUploadFail and allowMaxFailCount > 0 and captureObj.uploadCosFailCount < allowMaxFailCount:
                cInfo = captureObj
                break
        except:
            printLog("getNextCaptureTask error " + fullPath)
            os.remove(fullPath)
    return cInfo


def createCaptureCheckFromTask(captureInfo: CaptureUploadTaskInfo):
    printLog("createCaptureCheckFromTask captureInfo {}".format(captureInfo.taskFilePath))
    pBranch = captureInfo.projectBranch.replace("/", "_")
    captureTestTaskDirPath = os.path.join(CIConfig.xCiCaptureTestTaskDir)
    if not os.path.exists(captureTestTaskDirPath):
        os.makedirs(captureTestTaskDirPath)
    fileName = "{}_{}_{}_{}.task".format(captureInfo.projectName, pBranch, captureInfo.commitVersion,
                                         captureInfo.buildTime)
    filePath = os.path.join(captureTestTaskDirPath, fileName)


def writeObjectToFile(filePath, obj: object):
    if obj is not None:
        f = open(filePath, "w")
        jsonData = obj.__dict__
        f.write(json.dumps(jsonData))
        f.close()


def writeObjectToFileForTree(filePath, obj: object):
    if obj is not None:
        f = open(filePath, "w")
        jsonData = {}
        f.write(json.dumps(jsonData))
        f.close()


def getJsonFromObject(obj: object):
    if obj is not None:
        jsonData = {}
        attr = [a for a in dir(obj) if not a.startswith('__')]
        for a in attr:
            key = "{}".format(a)
            value = getattr(obj, a)
            attType = type(value)
            if value is None:
                continue
            if attType == int or attType == str or attType == float or attType == bool:
                jsonData[key] = value
            elif attType == list:
                jsonData[key] = value
            elif attType == dict:
                jsonData[key] = getJsonFromDict(value)
            else:
                jsonData[key] = getJsonFromObject(value)
        return jsonData

    return None


def getJsonFromDict(obj: object):
    if obj is not None:
        jsonData = {}
        for key in obj:
            value = obj[key]
            tp = type(value)
            if value is None:
                continue
            if tp == int or tp == str or tp == float or tp == bool:
                jsonData[key] = value
            elif tp == list:
                jsonData[key] = value
            elif tp == dict:
                jsonData[key] = getJsonFromDict(value)
            else:
                jsonData[key] = getJsonFromObject(value)
        return jsonData

    return None


def writeJsonToFile(filePath, obj: dict):
    if obj is not None:
        f = open(filePath, "w")
        f.write(json.dumps(obj))
        f.close()


def readObjectFromFile(filePath, obj: object):
    if not os.path.exists(filePath):
        return
    try:
        f = open(filePath)
        data = json.load(f)
        f.close()
        attr = [a for a in dir(obj) if not a.startswith('__')]
        for a in attr:
            key = "{}".format(a)
            if not xverseUtil.existParam(data, key):
                continue
            # pt = getattr(obj, a)
            # tp = type(pt)
            val = data[key]
            setattr(obj, a, val)
    except Exception as error:
        printLog("readObjectFromFile error {}".format(error))


def convertDict2Object(data, obj: object):
    if data is None:
        return False
    try:
        attr = [a for a in dir(obj) if not a.startswith('__')]
        for a in attr:
            key = "{}".format(a)
            if not xverseUtil.existParam(data, key):
                continue
            # pt = getattr(obj, a)
            # tp = type(pt)
            val = data[key]
            setattr(obj, a, val)
    except Exception as error:
        printLog("convertDict2Object error {}".format(error))


def readJsonFromFile(filePath):
    if not os.path.exists(filePath):
        return None
    try:
        f = open(filePath)
        data = json.load(f)
        f.close()
        return data
    except Exception as error:
        printLog("readJsonFromFile error {}".format(error))
    return None


def object2json(obj: object):
    return json.dumps(obj.__dict__)


def checkProcessRunning(prefixProcessName: str, subfixProcessName: str, queryCmdLine: str = None):
    printLog("checkProcessRunning prefix=%s, subfix=%s queryCmdLine=%s" % (
    prefixProcessName, subfixProcessName, queryCmdLine))
    if prefixProcessName is None or len(prefixProcessName) < 1:
        printLog("checkProcessRunning error:prefix is empty")
        return False
    pList = psutil.pids()
    processRunning = False
    for pid in pList:
        if pid <= 0:
            continue
        try:
            p = psutil.Process(pid)
            name = p.name()
            exe = p.exe()
            if exe is None:
                continue
            # printLog("pinfo pid={}, name={}, exe={}".format(pid, name,exe))
            cmdline = None
            try:
                cmdline = p.cmdline()
            except:
                pass
            if name.startswith(prefixProcessName):
                processRunning = True
                if subfixProcessName is None:
                    processRunning &= True
                elif subfixProcessName is not None and name.endswith(subfixProcessName):
                    printLog("match2 processName pid={}, name={}, exe={}".format(pid, name, exe))
                    processRunning &= True
                else:
                    processRunning &= False

                if queryCmdLine is None:
                    processRunning &= True
                elif queryCmdLine is not None and cmdline is not None:
                    cmdState = False
                    for cline in cmdline:
                        if cline.find(queryCmdLine) >= 0:
                            printLog("match3 processName pid={}, name={}, exe={}".format(pid, name, exe))
                            cmdState = True
                            break
                    processRunning &= cmdState
                else:
                    processRunning &= False
                if processRunning:
                    break
        except Exception as e:
            print("read process error", e)
    return processRunning

def kill_process_by_arguments(arguments):
    current_pid = os.getpid()  # 获取当前进程的 PID
    for process in psutil.process_iter(attrs=["pid", "name", "cmdline"]):
        try:
            pid = process.info['pid']
            cmdline = process.info['cmdline']
            name = process.info['name']

            # 检查是否是当前进程或没有命令行参数
            if pid == current_pid or not cmdline or 'python' not in name:
                continue

            # 查找包含指定参数的进程
            for argument in arguments:
                if any(argument in arg for arg in cmdline):
                    printLog(f"Killing process {pid} with cmdline: {cmdline}")
                    psutil.Process(pid).terminate()  # 终止进程

        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue

def checkUBTProcess(engineDir=None):
    printLog("checkUBTProcess engineDir={}".format(engineDir))
    pList = psutil.pids()
    ubtRunning = False
    for pid in pList:
        if pid <= 0:
            continue
        try:
            p = psutil.Process(pid)
            name = p.name()
            exe = p.exe()
            if exe is None:
                continue
            # printLog("pinfo pid={}, name={}, exe={}".format(pid, name,exe))
            if name.startswith("UE"):
                printLog("pinfo match UEEditor pid={}, name={}, exe={}".format(pid, name, exe))
                # ubtRunning = True
                # break
            if name.find("UnrealBuildTool") >= 0:
                printLog("pinfo match UnrealBuildTool pid={}, name={}, exe={}".format(pid, name, exe))
                ubtRunning = True
                break
            if name.find("UnrealHeaderTool") >= 0:
                printLog("pinfo match UnrealHeaderTool pid={}, name={}, exe={}".format(pid, name, exe))
                ubtRunning = True
                break
            if name.find("AutomationTool") >= 0:
                printLog("pinfo match AutomationTool pid={}, name={}, exe={}".format(pid, name, exe))
                ubtRunning = True
                break
            if name.find("UnrealEditor-Cmd") >= 0:
                printLog("pinfo match UnrealEditor-Cmd pid={}, name={}, exe={}".format(pid, name, exe))
                ubtRunning = True
                break
            if name.find("UE4Editor-Cmd") >= 0:
                printLog("pinfo match UE4Editor-Cmd pid={}, name={}, exe={}".format(pid, name, exe))
                ubtRunning = True
                break
        except:
            # printLog("read process error")
            pass
        pass
    return ubtRunning


def checkCIPythonProcess():
    printLog("checkCIPythonProcess")
    pList = psutil.pids()
    processRunning = False
    for pid in pList:
        if pid <= 0:
            continue
        try:
            p = psutil.Process(pid)
            name = p.name()
            exe = p.exe()
            if exe is None:
                continue
            # printLog("pinfo pid={}, name={}, exe={}".format(pid, name,exe))
            if name.find("python.exe") > -1:
                printLog("pinfo match pid={}, name={}, exe={},".format(pid, name, exe))
                pass
        except:
            printLog("read process error")
            pass
        pass
    return processRunning


def enablePluginDefaultState(pluginConfigFile: str, defState: bool = True):
    if os.path.exists(pluginConfigFile):
        pjson = json.load(codecs.open(pluginConfigFile, 'r', 'utf-8-sig'))
        pjson["EnabledByDefault"] = defState
        with open(pluginConfigFile, "w") as outfile:
            json_object = json.dumps(pjson, indent=4)
            outfile.write(json_object)
    else:
        printLog("enablePluginDefaultState error file not exist %s" % pluginConfigFile)


def getPlatformConfigName(platform):
    configName = platform
    if platform == "Win64":
        platform = "Windows"
    return configName


def writeEngineCosTask(file_path, task_info: EngineCosTaskInfo):
    printLog("writeEngineCosInfo file {}".format(file_path))
    writeObjectToFile(file_path, task_info)
    return file_path


def updateEngineCosTask(engine_task: EngineCosTaskInfo):
    filePath = engine_task.task_file_path
    printLog("updateEngineCosTask file {}".format(filePath))
    writeObjectToFile(filePath, engine_task)


def getNextEngineTask(allowMaxFailCount=0):
    # 引擎任务路径
    x_engine_task_path_dir_path = os.path.join(CIConfig.x_engine_tasks_dir)
    if not os.path.exists(x_engine_task_path_dir_path):
        return None
    # 枚举所有的 引擎任务
    files = os.listdir(x_engine_task_path_dir_path)
    files.sort()
    task_info = None
    for fpath in files:
        # 全路径尝试读取并序列化
        full_path = os.path.join(x_engine_task_path_dir_path, fpath)
        engine_cos_task_obj = EngineCosTaskInfo()
        readObjectFromFile(full_path, engine_cos_task_obj)
        try:
            # 状态判断为为上传则返回任务信息
            if engine_cos_task_obj.cos_state == CosUploadStatus.StateUnLoad:
                task_info = engine_cos_task_obj
                break
            # 上传失败且允许最大失败次数小于当前失败次数则返回任务信息
            elif (engine_cos_task_obj.cos_state == CosUploadStatus.StateUploadFail and allowMaxFailCount > 0
                  and engine_cos_task_obj.fail_count < allowMaxFailCount):
                task_info = engine_cos_task_obj
                break
        except :
            printLog("getNextEngineTask error " + full_path)
            os.remove(full_path)
    return task_info


def get_build_version():
    # 获取当前日期和时间
    now = datetime.datetime.now()

    # 提取年、月、日
    year = now.year
    month = now.month
    day = now.day

    # 获取当天是今年的第几天
    day_of_year = now.timetuple().tm_yday
    return year, month, day, day_of_year


if __name__ == "__main__":
    engineDir = xverseUtil.getStringParam(sys.argv, "-engineDir")
    logLevel = xverseUtil.getIntParam(sys.argv, "-logLevel")
    embedUploadCos = xverseUtil.getBoolParam(sys.argv, "-embedRemoveSymbol")
    printLogTag(logTAG, "engineDir={}, logLevel={}, embedUploadCos={}".format(engineDir, logLevel, embedUploadCos))
    currentYMDHMS()
    currentYMD()
    buildParam = BuildProjectParam()
    xverseUtil.readParams(sys.argv, buildParam)
    buildParam.engineDir = BuildHelper.searchEngineDir(buildParam.engineSearchDir, buildParam.engineBranch)
    buildParam.branch = "feat/revertUE4MeshTool\slimo"
    branchName = buildParam.branch.replace("/", "_")
    branchName = branchName.replace("\\", "_")
    print("branchName", branchName)
    b = checkUBTProcess()

    path = "C:\\work\\xverse\\Project5OutPut\\XStudio-20231205-151258\\Windows\\XStudio\\Binaries\\Win64\\XStudio.pdb"
    fType = ".pdb"
    path = "C:\\work\\xverse\\Project5OutPut\\XStudio-20231205-151258\\Windows\\XStudio\\Binaries\\Win64"
    dirPath = getPrevDirPath(path)
    fileName = getFileName(path)
    # removeFilesWithType(path, fType , ["XStudio.pdb"])

    print(b)
