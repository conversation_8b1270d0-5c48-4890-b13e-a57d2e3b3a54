#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import requests
import socket
import datetime
from userMap import userMap


class WeixinNotifyConfig:
    NotifyUrlTest = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=acd124a6-04b3-4182-83a5-83a30fdcaea7"
    NotifyUrlEngine = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=08d48b1a-a352-4439-af69-273040647067"
    NotifyUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1ca38134-d516-416c-a99c-ef5af107571a"
    NotifyUrlDev = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c094f2f9-ba98-43aa-8f0d-96564e72da15"
    # NotifyUrlVR = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c094f2f9-ba98-43aa-8f0d-96564e72da15"
    NotifyUrlVR = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e26e016b-bc57-4c04-9c09-cce9939d091a"
    def __init__(self): None

def printLog(msg):
    printLogTag("cosUtil", msg)

def printLogTag(tag, msg):
    cdate = datetime.datetime.now()
    date_time = cdate.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    log = "{} {} -- {}".format(date_time,tag, msg)
    print(log, flush=True)

class BuildNotifyMessage:
    def __init__(self):
        self.title = ""
        self.buildTime = ""
        self.outPutPath = None
        self.apkCosPath = None
        self.pakCosPath = None
        self.videoCosPath = None
        self.lowModelPakCosPath = None
        self.gameConfiguration = None
        self.version = None
        self.localLogPath = None
        self.branchName = None
        self.remoteLogPath = None
        self.errCode = 0
        self.errMsg = ""
        self.forTest = False
        self.forEngine = False
        self.stateDescribe = None
        self.cdStateDescribe = None
        self.taskUid = None
        self.executor = None
        self.currentPC = None
        self.channel = None
        self.extendMessageList = []
        self.mentionedMobileList = []
        self.mentionedList = []
        self.forVR = False
        self.remarks = ""

def sendWeixinTipsMessage(msg : BuildNotifyMessage):
    printLog("sendWeixinTipsMessage Start {} {}".format(msg.title, msg.errMsg))
    try:
        if msg.currentPC is None:
            msg.currentPC = socket.gethostname()
        MsgContent = {}
        Content = "标题:" + msg.title + "\n"
        if msg.buildTime is not None:
            Content += "时间:" + msg.buildTime + "\n"

        if msg.taskUid is not None:
            Content += "任务ID:{}\n".format(msg.taskUid)

        if msg.channel is not None:
            Content += "渠道:{}\n".format(msg.channel)

        if msg.executor is not None:
            Content += "执行者:{}\n".format(msg.executor)

        if msg.remarks is not None and len(msg.remarks)>1:
            Content += "备注:{}\n".format(msg.remarks)
        
        if msg.currentPC is not None:
            Content += "主机:{}\n".format(msg.currentPC)

        if msg.outPutPath is not None:
            Content += "地址:" + msg.outPutPath + "\n"

        if msg.gameConfiguration is not None:
            Content += "类型:" + msg.gameConfiguration + "\n"

        if msg.version is not None:
            Content += "版本:" + msg.version + "\n"

        if msg.branchName is not None:
            Content += "分支:" + msg.branchName + "\n"
        if msg.remoteLogPath is not None:
            Content += "日志:" + msg.remoteLogPath + "\n"

        if msg.errCode != 0:
            Content += "错误:({}){}\n".format(msg.errCode, msg.errMsg)

        if msg.stateDescribe is not None and len(msg.stateDescribe) > 0:
            Content += "状态:{}\n".format(msg.stateDescribe)
        
        if msg.apkCosPath is not None:
            Content += f"OTA COS桶:lbvr-apps-1258211750, OTA地址：\n"
            Content += f"\t apk:{msg.apkCosPath}\n"
        if msg.pakCosPath is not None:
            Content += f"\t pak:{msg.pakCosPath}\n"
        if msg.videoCosPath is not None:
            Content += f"\t video:{msg.videoCosPath}\n"
        if msg.lowModelPakCosPath is not None:
            Content += f"\t lowmodel pak:{msg.lowModelPakCosPath}\n"

        if msg.extendMessageList is not None:
            for message in msg.extendMessageList:
                Content += "{}\n".format(message)

        MsgContent["content"] = Content
        # if msg.forTest == True and msg.mentionedMobileList is not None and len(msg.mentionedMobileList) > 0:
        #     MsgContent["mentioned_mobile_list"] = msg.mentionedMobileList
        # if msg.forTest == True and msg.mentionedList is not None and len(msg.mentionedList) > 0:
        #     MsgContent["mentioned_list"] = msg.mentionedList
        MsgContent["mentioned_mobile_list"] = []
        if msg.executor is not None:
            MsgContent["mentioned_list"] = [msg.executor]
            if msg.executor in list(userMap.keys()):
                MsgContent["mentioned_mobile_list"].append([userMap[msg.executor]])
        
        # if msg.errCode != 0:
        #     MsgContent["mentioned_mobile_list"].extend(["17612950805"])
        
        MsgMap = {}
        MsgMap["msgtype"] = "text"
        MsgMap["text"] = MsgContent
        
        OutMsg = json.dumps(MsgMap, sort_keys=False, ensure_ascii=False, indent=4, separators=(',', ': '))
        printLog(OutMsg)
        headers = {
            'charset': 'utf-8',
            'content-type': 'application/json',
        }
        #data = '{\t"appId": appId,\t"appSecret": "appSecret"}'
        NotifyUrl = WeixinNotifyConfig.NotifyUrl
        if msg.forTest:
            NotifyUrl = WeixinNotifyConfig.NotifyUrlTest
        elif msg.forEngine:
            NotifyUrl = WeixinNotifyConfig.NotifyUrlEngine
        elif msg.forVR:
            NotifyUrl = WeixinNotifyConfig.NotifyUrlVR
        response = requests.post(NotifyUrl, headers=headers, data=OutMsg.encode("utf-8"))
        printLog(response)
    except Exception as e:
        printLog("sendWeixinTipsMessage except {}".format(e))

def sendFileMessage(media_id):
    try:
        if media_id is None:
            return
        msg = {
            "msgtype": "file",
            "file": {
                "media_id": media_id
            }
        }
        response = requests.post(
            WeixinNotifyConfig.NotifyUrlVR, headers={
                'charset': 'utf-8',
                'content-type': 'application/json',
            }, json=msg
        )
        printLog(response)
    except Exception as e:
        printLog("send file message error: {}".format(e))

def seneSimpleMessage(smsg):
    try:
        MsgMap = {}
        MsgMap["msgtype"] = "text"
        MsgMap["text"] = {
            "content": smsg,
        }
        response = requests.post(
            WeixinNotifyConfig.NotifyUrlVR, headers={
                'charset': 'utf-8',
                'content-type': 'application/json',
            }, json = MsgMap
        )
        printLog(response)
    except Exception as e:
        printLog("send simple message except {}".format(e))

def sendDevMessage(smsg):
    try:
        MsgMap = {}
        MsgMap["msgtype"] = "text"
        MsgMap["text"] = {
            "content": smsg,
        }
        response = requests.post(
            WeixinNotifyConfig.NotifyUrlDev, headers={
                'charset': 'utf-8',
                'content-type': 'application/json',
            }, json = MsgMap
        )
        printLog(response)
    except Exception as e:
        printLog("send simple message except {}".format(e))
    
if __name__ == "__main__":
    if len(sys.argv) > 6:
        BuildLogUrl = ""
        if len(sys.argv) > 8:
            BuildLogUrl = sys.argv[8]
        sendWeixinTipsMessage(sys.argv[1], sys.argv[2], sys.argv[3], sys.argv[4], sys.argv[5], sys.argv[6], sys.argv[7], BuildLogUrl)
    else:
        login = socket.gethostname()
        printLog("Start SendMsg error not matching argv length {}".format(login))
        pass