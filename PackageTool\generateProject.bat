@chcp 65001>nul
@cls
@echo off
pushd %~dp0
cd ../
set ProjectScriptDir=%cd%
cd ../
set ProjectDir=%cd%

set SambaName=**********
set PythonSambaPath=\\%SambaName%\XverseCreator\UEPackageTools\XPython310
set PythonDownloadUrl=http://**********:8080/tools/XPython310.zip
set UnzipDownloadUrl=http://**********:8080/tools/unzip.exe
set ProjectPythonDir=%ProjectScriptDir%\XPython310
set PythonDir=%ProjectPythonDir%
set CommonPythonDir=%AppData%\UETools\XPython310
set ExpandAppDataCmd=unzip -q XPython310.zip -d %CommonPythonDir%
set ExpandProjectCmd=unzip -q XPython310.zip -d %PythonDir% 

rem copy python
if exist %PythonDir% (
     echo %PythonDir% already exists
) else (
	echo Project XPython not exist
	set "PythonDir=%CommonPythonDir%"
	if not exist %CommonPythonDir% (
		echo Start Download unzip From samba %UnzipDownloadUrl%
		curl -o unzip.exe %UnzipDownloadUrl%
		echo Start Download XPython From samba %PythonDownloadUrl%
		curl -o XPython310.zip %PythonDownloadUrl%
		echo ExpandAppDataCmd=%ExpandAppDataCmd%
		call %ExpandAppDataCmd%
          if not exist %CommonPythonDir% (
               echo Copy XPython to AppData fail
               set "PythonDir=%ProjectPythonDir%"
               echo ExpandProjectCmd=%ExpandProjectCmd%
		     call %ExpandProjectCmd%
          ) else (
		     echo Copy XPython to AppData success
	     )
          echo Delete Cache Files XPython310.zip unzip.exe
          rm XPython310.zip
          rm unzip.exe
	) else (
		echo AppData XPython has exist
	)
)

set "RealPythonDir=%PythonDir%"

if "%2" == "" (
     echo not input ProjectDir 
) else (
     echo set input ProjectDir 
     set ProjectDir=%2
)

if "%3" == "" (
     echo not input pythonDir 
) else (
     echo set input PythonDir 
     set RealPythonDir=%3
)

set pythonBin=%RealPythonDir%\python.exe
set generateProjectFile=%ProjectDir%\Tools\generateProject.py

echo ProjectDir=%ProjectDir%
echo pythonBin=%pythonBin%
echo generateProjectFile=%generateProjectFile%

cd %~dp0
set Script=%pythonBin% %generateProjectFile% -scriptProject=%1 
echo Script=%Script%
call %Script%