#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import os
import xverseUtil
import projectGenerateUtil
logTAG = "generateProject"

def printLog(msg):
    xverseUtil.printLogTag(logTAG, msg)
if __name__ == "__main__":
    currPath = os.getcwd()
    scriptProject = None
    scriptProjectInput = xverseUtil.getStringParam(sys.argv, "-scriptProject")
    printLog("scriptProjectInput %s"%(scriptProjectInput))
    if scriptProjectInput is not None and len(scriptProjectInput) > 0:
        if scriptProjectInput.find(":") > 0:
            scriptProject = scriptProjectInput
        else:
            scriptProject = os.path.join(currPath, scriptProjectInput)

    if scriptProject is None or len(scriptProject) < 1:
        printLog("scriptProject is empty")
        sys.exit(0)
        
    projectDir = os.path.join(scriptProject, "../", "../", "../")
    projectDir = os.path.abspath(projectDir)

    projectName = xverseUtil.getFileName(scriptProject)
    onlyRemove = xverseUtil.getBoolParam(sys.argv, "-onlyRemove")
    printLog("scriptProject={}, projectDir={}, projectName={}, onlyRemove={}".format(scriptProject, projectDir, projectName, onlyRemove))
    projectUProjectFile = os.path.join(scriptProject, "%s.uproject"%(projectName))
    projectPluginConf = os.path.join(scriptProject, "%s.uplugin.conf"%(projectName))
    if not os.path.exists(scriptProject):
        printLog("generateProject %s error(projectName not exist)"%(projectName))
        sys.exit(0)
    if onlyRemove == True:
        projectGenerateUtil.removeOldProject(projectDir, projectName)
    else:
        projectGenerateUtil.generateProjectUProject(projectDir, projectName, InEnableAutoGenDeps=True)
        projectGenerateUtil.removeOldProject(projectDir, projectName)
        projectGenerateUtil.generateProjectSource(projectDir, projectName, True)
        projectGenerateUtil.copyProjectConfig(projectDir, projectName)
        projectGenerateUtil.copyProjectBuild(projectDir, projectName)
        printLog("generateProject %s success"%(projectName))
