import sys
import argparse
import orjson
import buildProject
import re
from uploadNotify import getStoreIdMap
from ciTools import BuildProjectParam


def main():

    buildParam = BuildProjectParam()
    buildParam.branch = args.projectBranch
    buildParam.engineMajorVersion = 5
    buildParam.engineBranch = 'feat/meta'
    buildParam.xsdkAppId = args.appId
    buildParam.executor = args.executor

    buildParam.bForceClearAllLocalCache = False
    buildParam.bUseSharedDDC = True
    
    buildParam.packageToolDir = args.packageToolDir
    buildParam.engineSearchDir = args.engineSearchDir
    buildParam.projectSourceDir = args.projectSourceDir
    buildParam.projectOutPutDir = args.projectOutputDir
    buildParam.projectBuildUrl = args.projectBuildUrl

    buildParam.bVRAndroidCi = True
    buildParam.bPrebuild = True
    buildParam.projectName = 'XVerseVR_Oculus'
    buildParam.targetPlatform = 'Android'
    buildParam.gameConfigurations = 'Development'
    buildParam.cookflavor = 'ASTC'
    buildParam.xsdkEnv = 'sit'

    buildParam.bCustomPak = True
    buildParam.bNeedLowmodelPak = True
    buildParam.bOnlyLowmodelPak = True
    # buildParam.backgroundUpload = False

    buildParam.clearDataWhenBuildFail = True
    buildParam.removeDataAfterBuild = True

    buildParam.xsdkLowModelPath = args.lowModelPath
    buildParam.xsdkLowModelVersion = args.lowModelVersion
    buildParam.lowmodelPakCosPrefix = args.outPathPrefix
    
    storeIdMap = getStoreIdMap()
    buildParam.dstStoreId = storeIdMap.get(args.storeName, "")  # 应该不可能为None
    print(f"{args.storeName}->{buildParam.dstStoreId}")

    buildParam.xsdkMarkerFile = f"{args.vrLandName}.json"
    if args.anchorConfig and len(args.anchorConfig) > 0:
        if re.search('^\d+_\w+$', args.anchorConfig):
            buildParam.xsdkMarkerFile = args.anchorConfig
        else:
            buildParam.anchorConfigJsonData = orjson.loads(args.anchorConfig)

        buildParam.otaVersion = args.otaVersion
        buildParam.remarks = args.releaseDesc
        buildParam.bOTA = True
        buildParam.bForceUploadOTA = False
        buildParam.bRelease = True

        buildParam.locateType = '锚点定位'

        buildResult = buildProject.buildProjectMain(buildParam)
        if not buildResult:
            sys.exit(1)
        sys.exit(0)
    else:
        print("锚点json传入为空")
        sys.exit(1)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='lowModelWorker', description='')
    parser.add_argument('--appId', type=str, required=True, help='应用Id')
    parser.add_argument('--lowModelPath', type=str, required=True, help='低模资产库路径')
    parser.add_argument('--lowModelVersion', type=str, required=True, help='低模版本号')
    parser.add_argument('--otaVersion', type=str, help='otaVersion')
    parser.add_argument('--releaseDesc', type=str, help='发布描述')
    parser.add_argument('--vrLandName', type=str, help='路线名')
    parser.add_argument('--anchorConfig', type=str, help='路线锚点配置')
    parser.add_argument('--outPathPrefix', type=str, help='低模cos路径')
    parser.add_argument('--storeName', type=str, default="", help='目标门店名')
    parser.add_argument('--executor', type=str, help='企微邮箱')
    
    parser.add_argument('--projectBranch', type=str, default='feat/LowModelWorker', help='项目分支')
    parser.add_argument('--engineSearchDir', type=str, required=True, help='预编译引擎目录')
    parser.add_argument('--projectBuildUrl', type=str, help='jenkins链接')
    parser.add_argument('--packageToolDir', type=str, help='ci代码')
    parser.add_argument('--projectSourceDir', type=str, help='xstudio目录')
    parser.add_argument('--projectOutputDir', type=str, help='输出目录')

    args = parser.parse_args()
    main()