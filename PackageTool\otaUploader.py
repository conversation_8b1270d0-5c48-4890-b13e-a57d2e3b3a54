import os
import sys
import pymysql
import orjson
import pymysql.cursors
import time
from joblib import Parallel, delayed
from qcloud_cos import CosConfig, CosS3Client
from qcloud_cos.cos_exception import CosClientError, CosServiceError
from loguru import logger
from collections import defaultdict
import asyncio
from pathlib import Path
from fastpycrc64 import get_file_crc64
from assetMgr import getCrc64
# from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
# from deepdiff import DeepDiff



otaConfig = CosConfig(Region='ap-shanghai', SecretId='AKIDIagW9wbotiOuDGjREo96Al9FAkodlRSH', SecretKey='kNlKfGdp67vaMvyPsPhmbppd4npeUjiS', Token=None, Scheme='https', Timeout=1800)
# otaDatabaseConfig = {"host":"sh-cdb-loi0lbda.sql.tencentcdb.com", "port": 63679, "user":'writer', "password":'writer@YX2021', "db":'x_ibr_pipeline'}
otaDatabaseConfig = {"host":"sh-cdb-061v8kug.sql.tencentcdb.com", "port": 63808, "user":'writer', "password":'writer@YX2021', "db":'x_ibr_pipeline'}
otaBucket = 'lbvr-apps-1258211750'

class Storage(object):
    def __init__(self, config):
        self.config = config
        self.client = CosS3Client(config)
    
    def download(self):
        raise NotImplementedError
    
    def upload(self):
        raise NotImplementedError


class OTAStorage(Storage):
    def __init__(self, retry_num = 10, config = otaConfig) -> None:
        super().__init__(config)
        
        # self.video_name = video_name
        # self.store_name = store_name
        # self.apk_version = apk_version
        # self.pak_version = pak_version
        # self.app_id = app_id
        # self.release_id = release_id
        self.retry_num = retry_num

        self.database = pymysql.connect(
            host = otaDatabaseConfig['host'],
            port = otaDatabaseConfig['port'],
            user=otaDatabaseConfig['user'],
            password=otaDatabaseConfig['password'],
            db=otaDatabaseConfig['db'],
            connect_timeout=3000,
            read_timeout=3000,
            write_timeout=3000
        )
        self.loop = asyncio.get_event_loop()
        self.max_workers = 10
        self.allUploadItems = []
    
    def uploadWithCrc64(self, local_file_path, remote_file_path, force):
        msg = ''
        try:
            response = self.client.head_object(Bucket=otaBucket, Key=remote_file_path)
            if response:
                local_crc64 = get_file_crc64(local_file_path)
                if local_crc64 != int(response['x-cos-hash-crc64ecma']):
                    if force:
                        msg = f"{remote_file_path}将被强制更新，尽管其crc64已经变化，因为bForceUploadOTA=True"
                    else:
                        msg = f"{remote_file_path}已经存在但不会更新，尽管其crc64已经变化，因为bForceUploadOTA=False"
                        remote_file_path = None
                    
                else:
                    return remote_file_path, f"{remote_file_path} no need update"
        except CosServiceError as e:
            if e.get_status_code() == 404:
                msg = f"{remote_file_path} doesn't exists, so upload"
            else:
                msg = f"failed to get {remote_file_path} info: {e}"
                remote_file_path = None

        if remote_file_path:
            self.allUploadItems.append([local_file_path, remote_file_path])
            # if not self.uploadWithRetry(local_file_path, remote_file_path):
            #     msg = f"upload {local_file_path} to {remote_file_path} failed"
            #     remote_file_path = None
            # else:
            #     msg += ", and upload successfully"
               
        return remote_file_path, msg

    def deployVerify(self, remote_path_list):
        for path in remote_path_list:
            if path:
                ext = os.path.splitext(os.path.basename(path))[1] 
                try:
                    if ext == '.pak' or ext == '.apk':
                        response = self.client.head_object(Bucket=otaBucket, Key=path)
                    elif ext == '':
                        response = self.client.list_objects(Bucket=otaBucket, Prefix=path, Delimiter='/', MaxKeys=1)
                        if 'CommonPrefixes' not in response and 'Contents' not in response:
                            return False, f"{path}在{otaBucket}中不存在"
                    else:
                        return False, f"{path}不符合规范"
                except CosServiceError as e:
                    if e.get_status_code() == 404:
                        return False, f"{path}在{otaBucket}中不存在"
                except Exception as e:
                    return False, f"校验{path}未知错误: {e}"
            else:
                return False, f"校验失败，有相同版本apk/pak已经存在"
        return True, ''
    
    async def uploadWithRetry(self, local_file_path, remote_file_path):
        # print(f"start upload {local_file_path}")
        for i in range(0, self.retry_num):
            try:
                await asyncio.to_thread(
                    self.client.upload_file,
                    Bucket=otaBucket,
                    Key=remote_file_path,
                    LocalFilePath=local_file_path,
                    PartSize=20,
                    MAXThread=15
                )
                # print(f"upload {local_file_path} successfully")
                return True, f"{os.path.basename(local_file_path)} uploaded to {remote_file_path} successfully"

            except (CosClientError, CosServiceError) as e:
                logger.error("failed to upload {} from {}: {}, {}", remote_file_path, local_file_path, e, i)
        return False, f"{os.path.basename(local_file_path)} uploaded to {remote_file_path} failed after {self.retry_num} retries\n"
    
    # def uploadWithRetry(self, local_file_path, remote_file_path):
    #     print(f"start upload {local_file_path}")
    #     for i in range(0, self.retry_num):
    #         try:
    #             _ = self.client.upload_file(
    #                 Bucket=otaBucket,
    #                 Key=remote_file_path,
    #                 LocalFilePath=local_file_path,
    #                 PartSize=16,
    #                 MAXThread=20
    #             )
    #             return True, f"{os.path.basename(local_file_path)} uploaded to {remote_file_path} successfully"

    #         except CosClientError or CosServiceError as e:
    #             logger.error("failed to upload {} from {}: {}, {}", remote_file_path, local_file_path, e, i)
    #     return False, f"{os.path.basename(local_file_path)} uploaded to {remote_file_path} failed after {self.retry_num} retries\n"

    
    def geSertPanoramicOTAInfo(self, allPanoramicVideoInfo):
        """
        :return(string, bool)：全景视频的cos地址，是否需要上传
        """
        referenceInfo, bUpload = defaultdict(int), True
        panoramicOTAPath = f"{allPanoramicVideoInfo['cosPathPrefix']}videos/{allPanoramicVideoInfo['releaseID'].split('_')[0]}"
        panoramicNum = len(allPanoramicVideoInfo['data'])
        sql_getPanoramicReferenceInfo = "SELECT cosPath FROM ota_panoramic_video WHERE crc64=%s AND cosPath LIKE %s"
        sql_insertPanoramicReferenceInfo = "INSERT INTO ota_panoramic_video (url, crc64, cosPath, appID, releaseID) VALUES (%s, %s, %s, %s, %s)"
        sql_getPanoramicCount = "SELECT COUNT(*) AS num FROM ota_panoramic_video WHERE cosPath=%s"
        
        def buildOTAQueryInfo(pvi):
            crc64Str = str(getCrc64(pvi['local_path']))
            return (pvi['url'], crc64Str, panoramicOTAPath, allPanoramicVideoInfo['appID'], allPanoramicVideoInfo['releaseID']), (crc64Str, allPanoramicVideoInfo['cosPathPrefix']+"%")
        
        # SIMD加速，但不支持用joblib并行，但是已经很快了
        insertInfo, queryInfo = [], []
        for pvi in allPanoramicVideoInfo['data']:
            crc64Str = str(get_file_crc64(pvi['local_path']))
            insertInfo.append([pvi['url'], crc64Str, panoramicOTAPath, allPanoramicVideoInfo['appID'], allPanoramicVideoInfo['releaseID']])
            queryInfo.append([crc64Str, allPanoramicVideoInfo['cosPathPrefix']+"%"])
        # queryInfoRes = Parallel(n_jobs=15)(delayed(buildOTAQueryInfo)(pvi) for pvi in allPanoramicVideoInfo['data'])
        # for result in queryInfoRes:
        #     insertInfo.append(result[0])
        #     queryInfo.append(result[1])
        retryCnt = 0
        while True:
            try:
                with self.database.cursor(cursor=pymysql.cursors.DictCursor) as cursor:
                    for q in queryInfo:
                        cursor.execute(sql_getPanoramicReferenceInfo, q)
                        queryRes = cursor.fetchall()
                        if queryRes:
                            for record in queryRes:
                                # key = (record['appID'], record['cosPath'])
                                referenceInfo[record['cosPath']] += 1
                    for rk, rv in referenceInfo.items():
                        if rv == panoramicNum:
                            # 判断是否是子集
                            cursor.execute(sql_getPanoramicCount, rk)
                            res = cursor.fetchall()
                            if res:
                                if res[0]['num'] == panoramicNum:
                                    panoramicOTAPath = rk
                                    bUpload = False
                                    break
                    
                    if bUpload:    
                        cursor.executemany(sql_insertPanoramicReferenceInfo, insertInfo)
                        self.database.commit()
                return panoramicOTAPath, bUpload
            except Exception as e:
                self.database.rollback()
                if retryCnt < 5:
                    retryCnt += 1
                else:
                    logger.error("query/insert database error, {e}")
                    return '', bUpload              

    def uploadDir(self, remote_dir, local_dir):
        """
        上传本地文件夹至cos目录, 如果local_dir不以分隔符结尾，则保留目录结构
        """
        parent_dir = os.path.dirname(local_dir)
        for path, _, file_list in os.walk(local_dir):
            for file_name in file_list:
                srcKey = os.path.join(path, file_name)
                cosObjectKey = os.path.join(remote_dir, os.path.relpath(srcKey, parent_dir)).replace("\\", "/")
            
                # ret = self.uploadWithRetry(srcKey, cosObjectKey)
                # if not ret:
                #     return "", f"upload {srcKey} failed"
                self.allUploadItems.append([srcKey, cosObjectKey])
        return remote_dir, f"upload {local_dir} success"
    
    async def batchUploadAsync(self):
        # with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
        #    tasks = [self.loop.run_in_executor(executor, self.uploadWithRetry, item[0], item[1]) for item in self.allUploadItems]
        #    results = await asyncio.gather(*tasks)
        #    return results
        tasks = [self.uploadWithRetry(item[0], item[1]) for item in self.allUploadItems]
        results = await asyncio.gather(*tasks)
        return results
    

    def startUpload(self):
        uploadRes = self.loop.run_until_complete(self.batchUploadAsync())
        msg = ""
        for res in uploadRes:
            if not res[0]:
                msg += res[1]
        return msg


    def getAllremoteCrc64(self, remote_dir):
        allCrc64 = []
        listObjects = self.client.list_objects(Bucket=otaBucket, Prefix=remote_dir, Delimiter='/')
        if 'Contents' in listObjects:
            for content in listObjects['Contents']:
                response = self.client.head_object(Bucket=otaBucket, Key=content['Key'])
                if response['x-cos-hash-crc64ecma'] != '0':
                    allCrc64.append([os.path.basename(content['Key']), response['x-cos-hash-crc64ecma']])
        return allCrc64

    def upsertBuildParameterInfo(self, buildInfo):
        sql_insertBuildParameterInfo = """
        INSERT INTO build_info (
            projectBranch, projectBranchCommitId, engineBranch, engineCommitId,
            appId, releaseId, pakMode, env, xsdkVersion, locateType,
            xsdkMarkerFile, xsdkLowModelVersion, xsdkLowModelPath, xsdkSceneId,
            videoName, storeName, packageName, applicationDisplayName,
            storeVersion, versionDisplayName, outputZipSambaPath, projectBuildUrl,
            apkCosPath, pakCosPath, videoCosPath, lowModelPakCosPath,
            ibrAssetSize, pbrAssetSize, panoramicVideoSize, apkSize, pakSize, lowmodelPakSize,
            createTime, pullProjectStartTime, pullEngineStartTime, downloadAssetStartTime,
            buildStartTime, custompakStartTime, sambaUploadStartTime, otaUploadStartTime,
            executor, resultCode, resultMsg
        ) VALUES (
            %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s, %s,
            %s, %s, %s
        )
        """
        retryCnt = 0
        while True:
            try:
                with self.database.cursor(cursor=pymysql.cursors.DictCursor) as cursor:
                    cursor.execute(sql_insertBuildParameterInfo, buildInfo)
                    self.database.commit()
                    break

            except Exception as e:
                self.database.rollback()
                if retryCnt < 5:
                    retryCnt += 1
                else:
                    return False, str(e)
        return True, ''
    
    def check_object_exists(self, remote_path: str):
        """
        检查COS对象或目录是否存在
        返回元组：(是否存在, 错误信息)
        """
        try:
            # 先尝试作为文件检查
            response = self.client.head_object(Bucket=otaBucket, Key=remote_path)
            return True, ""
        except CosServiceError as e:
            if e.get_status_code() == 404:
                # 文件不存在，尝试检查目录
                try:
                    # 检查目录是否存在（目录以/结尾）
                    list_objects = self.client.list_objects(
                        Bucket=otaBucket,
                        Prefix=remote_path.rstrip('/') + '/',
                        Delimiter='/',
                        MaxKeys=1
                    )
                    if 'CommonPrefixes' in list_objects or 'Contents' in list_objects:
                        return True, ""
                    return False, f"{remote_path} does not exist"
                except CosServiceError as e:
                    return False, f"检查目录时出错: {e}"
                except Exception as e:
                    return False, f"未知错误: {e}"
            else:
                return False, f"检查文件时出错: {e}"
        except Exception as e:
            return False, f"检查存在性时发生未知错误: {e}"

def getAllPanoramicVideoInfo():
    overrideConfigPath = os.path.join("C:\\Users\\<USER>\\Desktop", "OverrideConfig.json")
    with open(overrideConfigPath, 'rb') as f:
        overrideConfig = orjson.loads(f.read())
    allPanoramicVideoInfo = {"appID": '11222', "releaseID": '2410141128_f2f921', "cosPathPrefix": f"Moonbase/ShiJiaZhuangShiMeiJi", "data":[]}
    for roomInfo in overrideConfig['config']['roomList']:
        for skinInfo in roomInfo['skinList']:
            skinInfoKeys = list(skinInfo.keys())
            if 'panoramicVideo' in skinInfoKeys and skinInfo['panoramicVideo']:
                allPanoramicVideoInfo['data'].append(
                    {
                        "url": skinInfo['panoramicVideo']['url'].replace("\\", "/"),
                        "local_path": os.path.join("C:\\work\\xverse\\PanoVideo", os.path.basename(skinInfo['panoramicVideo']['url']))
                    }
                )
    return allPanoramicVideoInfo

if __name__ == "__main__":
    otaStorage = OTAStorage()
    # data = [
    #     ["AliceWonderland/zhongzhouwan/video/2410231136/", ""],
    #     ["AliceWonderland/wankeyuncheng/video/2410231136/", ""],
    #     ["UnderTheSea/hefeihuanlesong/videos/2410251409/", ""],
    #     ["Moonbase/TianJinMeiShuGuan/videos/2410252103/", ""],
    #     ["Moonbase/LiuZhouGongYeBoWuGuan/videos/2410252103/", ],
    #     ["Moonbase/HeFeiHeChai/videos/2410252103/", ],
    #     ["EternalTang/HuiShanGuZhen/videos/2410291413/", ],
    #     ["Moonbase/YunNanmuseum/videos/2410282334/", ],
    #     ["UnderTheSea/shanbo/videos/2410291133/", ],
    #     ["UnderTheSea/huishanguzhen/videos/2410281707/",],
    #     ["UnderTheSea/yunbo/videos/2410292010/", ],
    #     ['Moonbase/ShanXimuseum/videos/2410291434/', ]
    # ]
    # data1 = [
    #     "AliceWonderland/zhongzhouwan/video/202410151030/",
    #     "AliceWonderland/zhongzhouwan/video/202410251026/",
    #     "AliceWonderland/wankeyuncheng/video/202410151030/"
    #     "AliceWonderland/wankeyuncheng/video/202410251026/",
    #     "AliceWonderland/1702/video/202410151030/",
    #     "AliceWonderland/1702/video/202410262030/",
    #     "AliceWonderland/1720/video/202410151030/",
    #     "AliceWonderland/longgangcocopark/videos/202410232009/",
    #     "AliceWonderland/qianhaiwanxiang/video/202410151030/",
    #     "AliceWonderland/qianhaiwanxiang/video/202410251026/",
    #     "AliceWonderland/tianjinmeishuguan/videos/202410241748/",
    #     "AliceWonderland/tiyanban/videos/202410151030/",
    #     "AliceWonderland/tiyanban/videos/202410231025/",
    #     "Moonbase/AnHuiShengBoWuGuan/videos/202409231430/",
    #     "Moonbase/ChongqingCJH/videos/202409262110/",
    #     "Moonbase/GanKengGuZhen/videos/202409231430/",
    #     "Moonbase/GanKengGuZhenTwoSide/videos/202409231430/",
    #     "Moonbase/HeFeiHeChai/videos/202409231430/",
    #     "Moonbase/HuiShanGuZhen/videos/202409231430/",
    #     "Moonbase/LiJiangGuChen/videos/202409231430/",
    #     "Moonbase/LingJiaTanYiZhiGongYuan/videos/202409231430/",
    #     "Moonbase/LiuZhouGongYeBoWuGuan/videos/202409231430/",
    #     "Moonbase/LongGangCOCOPARK/videos/202409231430/",
    #     "Moonbase/LongGangWanDa/videos/202409231430/",
    #     "Moonbase/LuoYiGuCheng/videos/202409231430/",
    #     "Moonbase/NanJingXuanWuHu/videos/202409231430/",
    #     "Moonbase/TianJinMeiShuGuan/videos/202409231430/",
    #     "Moonbase/QianHaiWanXiang/videos/202409231430/",
    #     "Moonbase/ShanTouWanXiangChen/videos/202409231430/",
    #     "Moonbase/ShanXimuseum/videos/202409231430/",
    #     "Moonbase/ShiJiaZhuangShiMeiJi/videos/202409231430/",
    #     "Moonbase/TangShanZhongJunShiJieCheng/videos/202409231430/",
    #     "Moonbase/TiYanBan/videos/",
    #     "Moonbase/WanKeYunCheng/videos/202409231430/",
    #     "Moonbase/WuYuanMoonbay/video/202409231430/",
    #     "Moonbase/ZhongZhouWan/videos/202409231430/",
    #     "Moonbase/YunNanmuseum/videos/202409231430/",
    #     "EternalTang/AnHuiBoWuGuang/videos/202410171808/",
    #     "EternalTang/GanKengGuZhen/video/20240926142157/",
    #     "EternalTang/GaoFeng2x2/videos/202410181450/",
    #     "EternalTang/HeChai/videos/202410171808/",
    #     "EternalTang/LiJiangGuChen/videos/202410231414/",
    #     "EternalTang/LuoYangLuoYi/video/20240926142157/",
    #     "EternalTang/NanJinXuanWuHu/videos/202410231414/",
    #     "EternalTang/TiYanBan/videos/202410231620/",
    #     "EternalTang/ZhongZhouWan/video/202409291624/",
    #     "EternalTang/anbo/videos/202409292327/",
    #     "EternalTang/cqcjh/videos/202409292327/",
    #     "EternalTang/shimeiji/videos/202409292327/",
    #     "EternalTang/HuiShanGuZhen/videos/202410171808/",
    #     "MoonbaseGaofeng/2p2/videos/202409271828/",
    #     "MoonbaseGaofeng/GankengGuzhen/videos/202409262150/",
    #     "MoonbaseGaofeng/LiuzhouGongyeBowuguan/videos/202409281950/",
    #     "MoonbaseGaofeng/LonggangWanda/videos/202409262146/",
    #     "UnderTheSea/2x2/videos/2410231656_f0e420/",
    #     "UnderTheSea/chongqingchangjiahui/videos/202409231140/",
    #     "UnderTheSea/gankengcenter/videos/202409231140/",
    #     "UnderTheSea/gankengsides/videos/202409231140/",
    #     "UnderTheSea/hefeihechai/videos/202409231140/",
    #     "UnderTheSea/hefeihuanlesong/videos/202409231140/",
    #     "UnderTheSea/lgcocopark/videos/202409231140/",
    #     "UnderTheSea/lijiangguchen/videos/202409231140/",
    #     "UnderTheSea/lingjiatanyizhigongyuan/videos/202409231140/",
    #     "UnderTheSea/liuzhougongyebowuguan/videos/202409231140/",
    #     "UnderTheSea/luoyiguchen/videos/202409231140/",
    #     "UnderTheSea/nanjinxuanwuhu/videos/202409231140/",
    #     "UnderTheSea/qianhai/videos/202410121013/",
    #     "UnderTheSea/shantouwanxiangchen/videos/202409231140/",
    #     "UnderTheSea/shijiazhuangshimeiji/videos/202409231140/",
    #     "UnderTheSea/tanshanzhongjunshijiechen/videos/202409231140/",
    #     "UnderTheSea/tianjinmeishuguan/videos/202409231140/",
    #     "UnderTheSea/tiyanban/videos/202409231140/",
    #     "UnderTheSea/wanda/videos/202409231140/",
    #     "UnderTheSea/wankeyunchen/videos/202409231140/",
    #     "UnderTheSea/yueliangwan/videos/202409231140/",
    #     "UnderTheSea/zhongzhouwan/videos/202409231140/",
    #     "UnderTheSea/shanbo/videos/202409231140/",
    #     "UnderTheSea/huishanguzhen/videos/202409231140/",
    #     "UnderTheSea/yunbo/videos/202409231200/"
    # ]
    data1 = ["AliceWonderland/CommonToB/videos/2501062350/"]
    insertInfo = []
    for d in data1:
        crc64Info = otaStorage.getAllremoteCrc64(d)
        for c in crc64Info:
            insertInfo.append((c[0], c[1], d[:-1]))
    sql_insertPanoramicReferenceInfo = "INSERT INTO ota_panoramic_video (url, crc64, cosPath) VALUES (%s, %s, %s)"
    with otaStorage.database.cursor(cursor=pymysql.cursors.DictCursor) as cursor:
        cursor.executemany(sql_insertPanoramicReferenceInfo, insertInfo)
        otaStorage.database.commit()
    # otaStorage.allUploadItems = [
    #     ["C:\\customPak\\XVerseVR_Oculus-20241201-142144\\asset\\xverse-Android_ASTC.pak", "ci_test/test/pak/xverse-Android_ASTC.pak"],
    #     ["C:\\customPak\\XVerseVR_Oculus-20241201-142144\\asset\\XVerseVR_Oculus-arm64.apk", "ci_test/test/apk/XVerseVR_Oculus-arm64.apk"],
    #     ["C:\\custompak\\B202.mp4","ci_test/test/video/B202.mp4"],
    #     ["C:\\custompak\\B303.mp4","ci_test/test/video/B303.mp4"],
    #     ["C:\\custompak\\B501.mp4","ci_test/test/video/B501.mp4"],
    #     ["C:\\custompak\\B503.mp4","ci_test/test/video/B503.mp4"],
    #     ["C:\\custompak\\B702.mp4","ci_test/test/video/B702.mp4"],
    #     ["C:\\custompak\\B703.mp4","ci_test/test/video/B703.mp4"],
    #     ["C:\\custompak\\B802.mp4","ci_test/test/video/B802.mp4"],
    #     ["C:\\custompak\\B803.mp4","ci_test/test/video/B902.mp4"]
    # ]
    # start = time.time()
    # otaStorage.startUpload()
    # print(time.time()-start)
    # a = Path('E:/2412171511')
    # res = Parallel(n_jobs=-1)(delayed(getCrc64)(p) for p in a.iterdir())
    # for r in res:
    #     print(r)
        
        # if bUpload:
        #     buildParam.videoCosPath, msg = otaStorage.batchUpload(videoCosPath, buildParam.panoVideoDstDir+"\\")
        #     printLog(f"upload to {videoCosPath}: {msg}")
        # else:
        #     buildParam.videoCosPath = videoCosPath
        #     printLog(f"{buildParam.xsdkAppId}-{buildParam.xsdkReleaseId} reuse {videoCosPath} panoramic assets")
        # if not buildParam.videoCosPath:
        #     buildParam.stateDescribe += f"OTA上传：全景视频上传cos失败\n"