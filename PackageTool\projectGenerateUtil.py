#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import os
from types import SimpleNamespace
from collections import namedtuple
from xverseUtil import UProjectHelper
from xverseUtil import XTextReplaceHelper
from xverseUtil import XVerseProjectManager
import xverseUtil
logTAG = "projectGenerateUtil"

def removeOldProject(projectDir, projectName = None, withConfig = True):
    printLog("removeOldProject {} ".format(projectDir))
    # remove old project files
    sourceCsFiles = os.path.join(projectDir, "Source", "*.Target.cs")
    targetCsFiles = os.path.join(projectDir, "Source", "XVerse", "*.cs")
    sourceFiles = os.path.join(projectDir, "Source", "XVerse", "*.cpp")
    xverseUtil.removeFiles(sourceCsFiles)
    xverseUtil.removeFiles(targetCsFiles)
    xverseUtil.removeFiles(sourceFiles)

    # remove config
    if withConfig:
        WinConfig = os.path.join(projectDir, "Config", "Windows")
        LinuxConfig = os.path.join(projectDir, "Config", "Linux")
        AndroidConfig = os.path.join(projectDir, "Config", "Android")
        IosConfig = os.path.join(projectDir, "Config", "IOS")
        xverseUtil.removeDir(WinConfig)
        xverseUtil.removeDir(LinuxConfig)
        xverseUtil.removeDir(AndroidConfig)
        xverseUtil.removeDir(IosConfig)

    # remove .uproject and .sln
    uprojectList = os.listdir(projectDir)
    for fname in uprojectList:
        f = os.path.join(projectDir, fname)
        newProjectName = f"{projectName}.uproject"
        newSlnName = f"{projectName}.sln"
        if not os.path.isfile(f):
            continue
        if fname.startswith("XVerseStudio"):
            continue
        if fname.endswith(".sln") and projectName is not None and not fname == newSlnName:
            os.remove(f)
        elif fname.endswith(".uproject") and projectName is not None and not fname == newProjectName:
            os.remove(f)


def getProjectPlugins(projectDir, projectName, withEnable = True):
    printLog("getProjectPlugins projectDir={}, projectName={}, withEnable={}".format(projectDir, projectName, withEnable))
    targetPath = os.path.join(projectDir, "{}.uproject".format(projectName))
    uprojHelper = UProjectHelper()
    uprojHelper.read(targetPath)
    return uprojHelper.getPlugins(withEnable)

def generateProjectUProject(projectDir, projectName, inEnablePlugins = None, InDisablePlugins = None, InEnableAutoGenDeps = False, inXProjectMgr : XVerseProjectManager = None):
    printLog("generateProjectUProject projectDir={}, projectName={}, genDeps={}".format(projectDir, projectName, InEnableAutoGenDeps))

    printLog("generateProjectUProject inXProjectMgr={}".format(inXProjectMgr))

    helper = UProjectHelper()

    xPrjlang = XTextReplaceHelper()

    targetPath = os.path.join(projectDir, "{}.uproject".format(projectName))
    #template uproject
    templateName = "XProjectTemplate"
    sourceUProjectPath = os.path.join(projectDir, "Script", "Projects", templateName, "{}.uproject".format(templateName))

    projectUProjectPath = os.path.join(projectDir, "Script", "Projects", projectName, "{}.uproject".format(projectName))
    genChanncel = "template"
    if os.path.exists(projectUProjectPath):
        genChanncel = "project"
        printLog("generateProjectUProject use project UProject File %s"%(projectUProjectPath))
        sourceUProjectPath = projectUProjectPath

    #replace projectName
    xPrjlang.read(sourceUProjectPath)
    xPrjlang.replaceProjectName(projectName)
    xPrjlang.write(targetPath)

    #read project file
    helper.read(targetPath)

    #project plugins
    enablePlugins = []
    disablePlugins = []
    forceEnablePlugins = []
    enableAutoGenDeps = InEnableAutoGenDeps
    projectPluginsPath = os.path.join(projectDir, "Script", "Projects", projectName, "{}.uplugin.conf".format(projectName))
    if os.path.exists(projectPluginsPath):
        genChanncel += "-uplugin"
        printLog("generateProjectUProject use project uplugin.conf %s"%(projectPluginsPath))
        from pyhocon import ConfigFactory
        conf = ConfigFactory.parse_file(projectPluginsPath)

        if "EnablePlugins" in conf:
            plugins = conf["EnablePlugins"]
            enablePlugins.extend(plugins)
        
        if "DisablePlugins" in conf:
            plugins = conf["DisablePlugins"]
            disablePlugins.extend(plugins)

        if "ForceEnablePlugins" in conf:
            plugins = conf["ForceEnablePlugins"]
            forceEnablePlugins.extend(plugins)
        
        if "EnableAutoGenDeps" in conf:
            enableAutoGenDeps = conf["EnableAutoGenDeps"]

    #InDisablePlugins
    if InDisablePlugins is not None:
        disablePlugins.extend(InDisablePlugins)

    #inEnablePlugins
    if inEnablePlugins is not None:
        enablePlugins.extend(inEnablePlugins)
    
    #merge plugins
    for name in disablePlugins:
        if name in enablePlugins:
            enablePlugins.remove(name)

    for name in forceEnablePlugins:
        if name in disablePlugins:
            disablePlugins.remove(name)

    enablePlugins.extend(forceEnablePlugins)

    # search and gen deps plugins
    printLog("generateProjectUProject search and gen deps %s"%(enableAutoGenDeps))
    uprojEnablePlgs = []
    xProjectMgr = None
    if enableAutoGenDeps == True:
        if inXProjectMgr is None:
            printLog("generateProjectUProject xProjectManage is Null, will create")
            xProjectMgr = XVerseProjectManager()
            xProjectMgr.init(projectDir)
        else:
            xProjectMgr = inXProjectMgr
        for name in enablePlugins:
            plgItem = xProjectMgr.getPluginDepsForDict(name)
            if plgItem is not None:
                for key in plgItem:
                    uprojEnablePlgs.append(key)
    else:
        uprojEnablePlgs.extend(enablePlugins)

    # merge all plugin
    for name in disablePlugins:
        if name in uprojEnablePlgs:
            uprojEnablePlgs.remove(name)
            
    # disable plugin list
    helper.joinPlugins(disablePlugins, False)

    # enable plugin list
    finalEnablePluginList = []
    for enableName in uprojEnablePlgs:
        enableItem = {}
        enableItem["Name"] = enableName
        enableItem["Enabled"] = True
        projectPlugin = xProjectMgr.getProjectPluginInfo(enableName)
        if projectPlugin is not None and projectPlugin.pluginBodyInfo is not None:
            if "MarketplaceURL" in projectPlugin.pluginBodyInfo and len(projectPlugin.pluginBodyInfo["MarketplaceURL"]) > 0:
                enableItem["MarketplaceURL"] = projectPlugin.pluginBodyInfo["MarketplaceURL"]
            
            if "SupportedTargetPlatforms" in projectPlugin.pluginBodyInfo:
                enableItem["SupportedTargetPlatforms"] = projectPlugin.pluginBodyInfo["SupportedTargetPlatforms"]
        finalEnablePluginList.append(enableItem)
    helper.joinPluginItemList(finalEnablePluginList)

    #extends uproject info
    helper.setGenChannel(genChanncel)

    #write
    helper.write(targetPath)

def generateProjectSource(projectDir, projectName, withEntry = False):
    printLog("generateProjectSource projectDir={}, projectName={}".format(projectDir, projectName))
    #EditorTarget.cs
    templateProjectName = "XProjectTemplate"
    templateEditorPath = "{0}/Script/Projects/{1}/Source/{2}Editor.Target.cs".format(projectDir, templateProjectName, templateProjectName)

    projectEditorPath = "{0}/Script/Projects/{1}/Source/{2}Editor.Target.cs".format(projectDir, projectName, projectName)
    if os.path.exists(projectEditorPath):
        printLog("generateProjectSource use project EditorTarget.cs File %s"%(projectEditorPath))
        templateEditorPath = projectEditorPath
    destEditorPath = "{0}/Source/{1}Editor.Target.cs".format(projectDir, projectName)
    if os.path.exists(templateEditorPath):
        xprcLang = XTextReplaceHelper()
        xprcLang.read(templateEditorPath)
        xprcLang.replaceProjectName(projectName)
        xprcLang.keepCondition(withEntry)
        xprcLang.write(destEditorPath)
    
    #GameTarget.cs
    templateGamePath = "{0}/Script/Projects/{1}/Source/{2}.Target.cs".format(projectDir, templateProjectName, templateProjectName)
    projectGamePath = "{0}/Script/Projects/{1}/Source/{2}.Target.cs".format(projectDir, projectName, projectName)
    if os.path.exists(projectGamePath):
        printLog("generateProjectSource use project GameTarget.cs File %s"%(projectGamePath))
        templateGamePath = projectGamePath

    destGamePath = "{0}/Source/{1}.Target.cs".format(projectDir, projectName)
    if os.path.exists(templateGamePath):
        xprcLang = XTextReplaceHelper()
        xprcLang.read(templateGamePath)
        xprcLang.replaceProjectName(projectName)
        xprcLang.keepCondition(withEntry)
        xprcLang.write(destGamePath)

    #Build.cs
    templateBuildPath = "{0}/Script/Projects/{1}/Source/XVerse/{2}.Build.cs".format(projectDir, templateProjectName, templateProjectName)
    projectBuildPath = "{0}/Script/Projects/{1}/Source/XVerse/{2}.Build.cs".format(projectDir, projectName, projectName)
    if os.path.exists(projectBuildPath):
        printLog("generateProjectSource use project Build.cs File %s"%(projectBuildPath))
        templateBuildPath = projectBuildPath
    destBuildPath = "{0}/Source/XVerse/{1}.Build.cs".format(projectDir, projectName)
    if os.path.exists(templateBuildPath):
        xprcLang = XTextReplaceHelper()
        xprcLang.read(templateBuildPath)
        xprcLang.replaceProjectName(projectName)
        xprcLang.keepCondition(withEntry)
        xprcLang.write(destBuildPath)

def copyProjectConfig(projectDir, projectName):
    printLog("copyProjectConfig projectDir={}, projectName={}".format(projectDir, projectName))
    srcDir = "{0}/Script/Projects/{1}/Config/".format(projectDir, projectName)
    if os.path.exists(srcDir):
        destDir = os.path.join(projectDir, "Config")
        xverseUtil.copyDirByShutil(srcDir, destDir)

def copyProjectBuild(projectDir, projectName):
    printLog("copyProjectBuild projectDir={}, projectName={}".format(projectDir, projectName))
    srcDir = "{0}/Script/Projects/{1}/Build/".format(projectDir, projectName)
    if os.path.exists(srcDir):
        destDir = os.path.join(projectDir, "Build")
        xverseUtil.copyDirByShutil(srcDir, destDir)

def copyProjectSource(projectDir, projectName):
    printLog("copyProjectSource projectDir={}, projectName={}".format(projectDir, projectName))
    srcDir = "{0}/Script/Projects/{1}/Source/".format(projectDir, projectName)
    if os.path.exists(srcDir):
        destDir = os.path.join(projectDir, "Source")
        xverseUtil.copyDirByShutil(srcDir, destDir)

def copyProjectUProject(projectDir, projectName):
    printLog("copyProjectUProject projectDir={}, projectName={}".format(projectDir, projectName))
    srcFile = "{0}/Script/Projects/{1}/{2}.uproject".format(projectDir, projectName, 'XVerseVR_Oculus')
    if os.path.exists(srcFile):
        destFile = os.path.join(projectDir, "{}.uproject".format('XVerseVR_Oculus'))
        xverseUtil.copyFileByShutil(srcFile, destFile)

def copyUProjectFromScript(projectDir, projectName):
    copyProjectSource(projectDir, projectName)
    copyProjectBuild(projectDir, projectName)
    copyProjectConfig(projectDir, projectName)
    copyProjectUProject(projectDir, projectName)

def printLog(msg):
    xverseUtil.printLogTag(logTAG, msg)

if __name__ == "__main__":
    currPath = os.getcwd()
    printLog("currPath " + currPath)
