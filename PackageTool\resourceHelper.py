#!/usr/bin/python
# -*- coding: UTF-8 -*-

import ciTools
import datetime
import requests
import os
import sqlite3
import threading
from enum import Enum
import time
import math

class ResouceDownlaodParam():
    def __init__(self):
        self.projectSourceDir = None
        self.projectAssetDbPath = None
        self.xsdkAppId = None
        self.xsdkVersion = None
        self.xsdkEnv = None
        self.xsdkWorldId = None
        self.xsdkReleaseId = None
        self.cacheDir = None
        self.targetPlatform = "Android"
        self.assetListDownloadConfigPath = None
class AssetConfig():
    def __init__(self):
        self.assetDbUrl = None
        self.resourceVersion = None

class DownloadAssetStatus(Enum):
    free = 0
    reqDownloadUrling = 1
    reqDownloadUrlSucc = 2
    reqDownloadUrlFail = 3
    downloadAsseting = 11
    downloadAssetSucc = 12
    downloadAssetFail = 13

class DownloadAssetMgr():
    def __init__(self):
        self.maxTaskCount = 1
        self.maxTaskFreeTimeMs = 10 * 1000
        self.assetQueueMgr : DownloadAssetQueueMgr = None
        self.tastQueue = []
        self.buildParam : ResouceDownlaodParam = None
        self.releaseConfig : AssetConfig = None
    def init(self):
        self.assetQueueMgr = DownloadAssetQueueMgr()
        if not os.path.exists(self.buildParam.cacheDir):
            os.makedirs(self.buildParam.cacheDir)

    def getGlobalVersion(self):
        return self.releaseConfig.resourceVersion

    def startDownloadAsset(self):
        self.queryReleaseAssetList()
        self.queryAssetInfo()
        self.startDownloadTask()

    def queryReleaseAssetList(self):
        ret = self.downloadAssetDb()
        if ret == True:
            return self.analysisAssetDb()
        return False


    def getUnDownloadPathIds(self):
        if self.assetQueueMgr is not None:
           return self.assetQueueMgr.unDownloadAssetList
        return None

    def generateAssetConsoleConfig(self):
        printLog("generateAssetConsoleConfig start")
        pathIdList = self.getUnDownloadPathIds()
        if pathIdList is not None and len(pathIdList):
            pathContent = ""
            for pathId in pathIdList:
                if len(pathContent) > 0:
                    pathContent += ","
                pathContent += "\"%s\""%pathId
            content = "[%s]"%(pathContent)
            file = open(self.buildParam.assetListDownloadConfigPath, "w")
            file.write(content)
            file.close()
            return True
        return False

    def getBranchConfig(self):
        printLog("getBranchConfig start")
        appId = self.buildParam.xsdkAppId
        releaseId = self.buildParam.xsdkReleaseId
        platform = self.buildParam.targetPlatform.upper()
        url = "https://static.xverse.cn/console/config/%s/%s/%s/config.json"%(appId, releaseId, platform)
        data = getJson(url)
        if data is None:
            return None
        print(data)
        config = AssetConfig()
        config.resourceVersion = data["resourceVersion"]
        if "resourceSnapshotDB" in data:
            dbDict = data["resourceSnapshotDB"]
            if "url" in dbDict:
                config.assetDbUrl = dbDict["url"]
                return config
        return None

    def downloadAssetDb(self):
        printLog("downloadAssetDb start")
        self.buildParam.projectAssetDbPath = None
        if self.buildParam is None:
            return False
        if self.buildParam.xsdkReleaseId is None or self.buildParam.xsdkAppId is None:
            printLog("downloadAssetDb error releaseId or appId is empty")
            return False
        config = self.getBranchConfig()
        if config is None:
            printLog("downloadAssetDb get asset db url error")
            return False
        self.releaseConfig = config
        outFilePathDir = os.path.join(self.buildParam.cacheDir, "Assets")
        if not os.path.exists(outFilePathDir):
            os.makedirs(outFilePathDir)
        outFilePath = os.path.join(outFilePathDir, "%s_Assets.db3"%(self.buildParam.xsdkReleaseId))
        self.buildParam.projectAssetDbPath = outFilePath
        if os.path.exists(outFilePath):
            os.remove(outFilePath)
        assetUrl = config.assetDbUrl
        printLog("globalVersion {}".format(config.resourceVersion))
        ret = downloadFile(assetUrl, outFilePath = outFilePath)
        return ret
    def queryAssetInfo(self):
        globalVersion = self.releaseConfig.resourceVersion
        printLog("queryAssetInfo begin %s"%globalVersion)
        queryAssetInfoUrl = "https://console-api.xverse.cn/release/console/asset_mgt/v1/assets/query"
        #JsonBuiler.AddKeyValue(TEXT("pathIdList"), Request->RequestingPathIds);
		#JsonBuiler.AddKeyValue(TEXT("fileType"), Request->Platform);
		#JsonBuiler.AddKeyValue(TEXT("globalVersion"), Request->GlobalVer.ToString());
        maxLen = 50
        size = len(self.assetQueueMgr.unDownloadAssetList)
        currentIndex = 0
        for i in range(size):
            currentIndex += 1
            if currentIndex % maxLen == 0 or currentIndex == size:
                reqJson = dict()
                pathIdList = []
                pathId = self.assetQueueMgr.getUnDownloadAsset(i)
                pathIdList.append(pathId)      
                reqJson["pathIdList"] = pathIdList
                reqJson["globalVersion"] = globalVersion
                reqJson["fileType"] = "SOURCE"
                respJson = postJson(queryAssetInfoUrl, body = reqJson)
                code = respJson["code"]
                if code == 0:
                    data = respJson["data"]
                    assetList = data["assetList"]
                    for assetItem in assetList:
                        itemPathId = assetItem["pathId"]
                        downloadUrl = assetItem["url"]
                        cacheItem : DownloadAssetItem= self.assetQueueMgr.getAssetInfo(itemPathId)
                        if cacheItem is not None:
                            cacheItem.downloadUrl = downloadUrl
                printLog("queryAssetInfo result {}".format(respJson))
                break
        self.status = DownloadAssetStatus.reqDownloadUrlSucc
        printLog("queryAssetInfo end ")
    def analysisAssetDb(self):
        if self.buildParam.projectAssetDbPath is None or not os.path.exists(self.buildParam.projectAssetDbPath):
            return False
        conn = sqlite3.connect(self.buildParam.projectAssetDbPath)
        c = conn.cursor()
        #CREATE TABLE `AssetCacheTbl` (`RowId` integer,`PathId` text,`FileType` text,`Version` integer,`MaxVersion` integer,`TimeStamp` integer,`FileSize` integer,`MetaInfo` JSON,PRIMARY KEY (`RowId`))
        cursor = c.execute("SELECT RowId, PathId, Version, MetaInfo from AssetCacheTbl")
        for row in cursor:
            pathId = row[1]
            version = row[2]
            metaInfo = row[3]
            item = DownloadAssetItem()
            item.pathId = pathId
            item.version = version
            item.metaInfo = metaInfo
            item.localStoragePath = os.path.join("%s")
            self.assetQueueMgr.insertAssetInfo(pathId, item)
            self.assetQueueMgr.addUnDownloadAsset(pathId)
            #printLog("ID = %s, PathId = %s"%(row[0], row[1]))
            #printLog("row = {}".format(row))
            #break
        conn.close()
        return True
    def startDownloadTask(self):
        if self.assetQueueMgr is None or self.assetQueueMgr.isEmpty():
            pass
        else:
            for i in range(self.maxTaskCount):
                task = DownloadAssetTask()
                task.assetQueueMgr = self.assetQueueMgr
                task.taskName = "DownloadAssetTask-%d"%i
                self.tastQueue.append(task)
                task.startTask()

class DownloadAssetItem():
    def __init__(self):
        self.pathId = None
        self.version = None
        self.metaInfo = None
        self.downloadUrl = None
        self.localStoragePath = None

class DownloadAssetQueueMgr():
    def __init__(self):
        self.assetInfoDict = dict()
        self.unDownloadAssetList = []
        self.downloadedSuccessList = []
        self.downloadedFailList = []

    def isEmpty(self):
        return len(self.unDownloadAssetList) == 0

    def insertAssetInfo(self, pathId, item : DownloadAssetItem):
        self.assetInfoDict[pathId] = item

    def getAssetInfo(self, pathId):
        return self.assetInfoDict.get(pathId)

    def getUnDownloadAsset(self, index : 0):
        return self.unDownloadAssetList[index]

    def addUnDownloadAsset(self, item : DownloadAssetItem):
        self.unDownloadAssetList.append(item)
    
    def popUnDownloadAsset(self):
        if len(self.unDownloadAssetList) > 0:
            return self.unDownloadAssetList.pop()
        else:
            return None

    def addSuccAsset(self, item : DownloadAssetItem):
        self.downloadedSuccessList.append(item)
    
    def addFailAsset(self, item : DownloadAssetItem):
        self.downloadedFailList.append(item)

class DownloadAssetTask():
    def __init__(self):
        self.taskThread = None
        self.taskName = None
        self.assetQueueMgr : DownloadAssetQueueMgr = None
        self.condition = threading.Condition()
        self.currentAssetItem : DownloadAssetItem= None
        self.bStop = False
        self.status = DownloadAssetStatus.free

    def run(self):
        while self.bStop == False:
            if self.status == DownloadAssetStatus.free or self.status == DownloadAssetStatus.downloadAssetSucc:
                item = self.assetQueueMgr.popUnDownloadAsset()
                if item is not None:
                    self.status = DownloadAssetStatus.reqDownloadUrling
                    self.currentAssetItem = item
            elif self.status == DownloadAssetStatus.reqDownloadUrlSucc:
                self.downloadAsset()
            self.workerWait(3)
    def startTask(self):
        taskThread = threading.Thread(target=self.run, name=self.taskName)
        taskThread.start()

    def workerWait(self, delay):
        with self.condition:
            self.condition.wait(delay)

    def stopWorker(self):
        self.bStop = True
        with self.condition:
            self.condition.notify()
        printLog("DownloadAssetTask stopWorker")
    def downloadAsset(self):
        printLog("downloadAsset begin %s"%self.currentAssetItem.pathId)
        self.status = DownloadAssetStatus.downloadAsseting
        self.workerWait(5)
        self.assetQueueMgr.addSuccAsset(self.currentAssetItem)
        self.status = DownloadAssetStatus.downloadAssetSucc
        printLog("downloadAsset end %s"%self.currentAssetItem.pathId)
def printLog(msg):
    cdate = datetime.datetime.now()
    date_time = cdate.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    log = "{} {} -- {}".format(date_time, "resourceHelper", msg)
    print(log, flush=True)

def downloadAssetPathList(buildParam : ResouceDownlaodParam):
    downloadMgr = DownloadAssetMgr()
    downloadMgr.buildParam = buildParam
    downloadMgr.init()
    downloadMgr.queryReleaseAssetList()
    itemList = downloadMgr.getUnDownloadPathIds()
    pathList = set()
    for item in itemList:
        pathList.add(item)
    printLog("downloadAssetPathList end")
def downloadFile(url : str, params = None, outFilePath = None):
    if outFilePath is None:
        return False
    r = requests.get(url = url, params = params)
    code = r.status_code
    if code == 200:
        open(outFilePath, "wb").write(r.content)
        printLog("download File oK %s"%(outFilePath))
        return True
    return False

def getJson(url : str, params = None):
    printLog("getJson %s"%(url))
    r = requests.get(url = url, params = params)
    code = r.status_code
    if code == 200:
        return r.json()
    return None


def postJson(url : str, params = None, body = None):
    printLog("postJson %s"%(url))
    r = requests.post(url = url, data = params, json = body)
    code = r.status_code
    if code == 200:
        return r.json()
    return None

if __name__ == "__main__":
    buildParam = ResouceDownlaodParam()
    buildParam.xsdkAppId = "11088"
    buildParam.xsdkReleaseId = "2309142121_4e7db9"
    buildParam.cacheDir = os.getcwd()
    buildParam.projectSourceDir = "C:\\work\\xverse\\XVerseStudio"
    downloadAssetPathList(buildParam)