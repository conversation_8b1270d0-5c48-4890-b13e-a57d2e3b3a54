import os
import sys
import socket
import argparse
import rtoml
from pathlib import Path
from corpwechatbot.chatbot import CorpWechatBot
from ciTools import runScript


def main():
    projectNames = args.projectNames.split(';')
    engineBranch = args.engineBranch.strip()
    engineDir = Path(args.engineDir)
    projectDstPath = args.projectDstPath.replace('\\', '/')

    if not engineDir.exists():
        print(f'引擎目录不存在：{engineDir}')
        sys.exit(1)
    os.makedirs(projectDstPath, exist_ok=True)

    lbvrProjectConfigPath = Path("./config/LBVRProject.toml").absolute()
    if not lbvrProjectConfigPath.exists():
        print(f'LBVRProject.toml配置文件不存在：{lbvrProjectConfigPath}')
        sys.exit(1)

    # 读取LBVRProject.toml配置文件
    allLBVRProjectInfo = rtoml.load(lbvrProjectConfigPath)
    # 获取引擎commitId

    commitId, matched = None, False
    for p in engineDir.iterdir():
        if 'XVerseEngine' in p.name:
            if p.is_dir():
                versionPath = p / "Engine\\Version\\EngineVersion.ini"
                with open(versionPath, 'r', encoding='utf-8') as f:
                    for line in f.readlines():
                        if line.startswith("Branch"):
                            branch = line.split(':')[-1].strip()
                            if branch != engineBranch:
                                break
                            else:
                                matched = True
                        if line.startswith("CommitId") and matched:
                            commitId = line.split(':')[-1].strip()
                            break

                if commitId and matched:
                    break
    if not commitId or not matched:
        print(f'未找到匹配的引擎, {engineDir}')
        sys.exit(1)

    os.chdir(projectDstPath)
    for projectName in projectNames:
        projectName = projectName.strip().lower()
        if not projectName:
            continue
        print(f'正在处理：{projectName}')
        sharedDDCPathPrefix = f"//DDCache/DDC/CI/{projectName}/"
        if projectName in allLBVRProjectInfo['projectName']:
            projectInfo = allLBVRProjectInfo.get(projectName)
            if projectInfo:
                if os.path.exists(projectInfo['sourceDir']):
                    print(f"{projectName} 源码目录{projectInfo['sourceDir']}已存在，跳过")
                    continue
        
        # 执行git clone
        sharedDDCPathPrefix = f"//DDCache/DDC/CI/{projectName}/"
        sourceDir = os.path.join(projectDstPath, projectName).replace("\\", "/")
        gitCloneCmd = f"git clone https://e.coding.net/xverse-git/xversesdk/XVerseStudio.git {projectName}"
        ret, msg = runScript(gitCloneCmd)
        if not ret:
            print(f"执行失败：{msg}")
        if projectName not in allLBVRProjectInfo['projectName']:
            allLBVRProjectInfo['projectName'].append(projectName)
            allLBVRProjectInfo[projectName] = {}
        allLBVRProjectInfo[projectName]['sourceDir'] = sourceDir
        allLBVRProjectInfo[projectName]['lastBuildEngineCommitId'] = commitId
        allLBVRProjectInfo[projectName]['sharedDDCPathPrefix'] = sharedDDCPathPrefix
        allLBVRProjectInfo[projectName]['lastBuildEngineBranch'] = engineBranch
    
    rtoml.dump(allLBVRProjectInfo, lbvrProjectConfigPath, pretty=True)
    bot = CorpWechatBot(key='c094f2f9-ba98-43aa-8f0d-96564e72da15')
    bot.send_text(content=f"{socket.gethostname()}已部署{projectNames}")
    sys.exit(0)



if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='newLBVRProject', description='')
    parser.add_argument('--projectNames', type=str, required=True, help='片源名,按；分割')
    parser.add_argument('--engineBranch', type=str, default='feat/meta', help='引擎分支')
    parser.add_argument('--engineDir', type=str, required=True, help='预编译引擎目录')
    parser.add_argument('--projectDstPath', type=str, default='D:/AllLBVRProject', help='项目clone目录')

    args = parser.parse_args()
    main()