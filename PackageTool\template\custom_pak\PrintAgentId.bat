@echo off
setlocal enabledelayedexpansion
set ADB=.\tool\adb.exe
set DEVICE=
if not "%1"=="" set DEVICE=-s %1
for /f "delims=" %%A in ('%ADB% %DEVICE% shell "echo $EXTERNAL_STORAGE"') do @set STORAGE=%%A

@echo.
if exist agentid.txt (
    del agentid.txt
)

%ADB% %DEVICE% shell cat  %STORAGE%/Android/data/com.xverse.xvragent/files/xv_agent_pref/agent_id.txt > agentid.txt
@if "%ERRORLEVEL%" NEQ "0" goto Error
type agentid.txt
rem del agentid.txt
@echo.
@pause

goto:eof

:Error
del agentid.txt
@echo.
@echo Get DeviceId Error
@echo.
@pause