@echo off
setlocal enabledelayedexpansion
set ADB=.\tool\adb.exe
set PACKAGENAME=com.xverse.template
set DEVICE=
if not "%1"=="" set DEVICE=-s %1
for /f "delims=" %%A in ('%ADB% %DEVICE% shell "echo $EXTERNAL_STORAGE"') do @set STORAGE=%%A

@echo.
if exist deviceid.txt (
    del deviceid.txt
)

if exist id.txt (
    del id.txt
)
%ADB% %DEVICE% shell grep "deviceid=" %STORAGE%/Android/data/%PACKAGENAME%/files/UnrealGame/XVerseVR_Oculus/XVerseVR_Oculus/Saved/Logs/XVerseVR_Oculus.log > deviceid.txt
@if "%ERRORLEVEL%" NEQ "0" goto Error
set deviceid=
for /f tokens^=2^ delims^=^" %%a in (deviceid.txt) do (
    rem echo %%a
    set deviceid=%%a
)
echo %deviceid%>>id.txt
del deviceid.txt
echo %deviceid%
@pause

goto:eof

:Error
del deviceid.txt
@echo.
@echo Get DeviceId Error
@echo.
@pause
