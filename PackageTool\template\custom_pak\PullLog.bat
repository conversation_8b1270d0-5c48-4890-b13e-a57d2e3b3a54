setlocal
set ADB=.\tool\adb.exe
set PACKAGENAME=com.xverse.template
set DEVICE=
if not "%1"=="" set DEVICE=-s %1
for /f "delims=" %%A in ('%ADB% %DEVICE% shell "echo $EXTERNAL_STORAGE"') do @set STORAGE=%%A

@echo.
@echo pull log Starting
%ADB% %DEVICE% pull %STORAGE%/Android/data/%PACKAGENAME%/files/UnrealGame/XVerseVR_Oculus/XVerseVR_Oculus/Saved/Logs %PACKAGENAME%_Logs
%ADB% %DEVICE% pull %STORAGE%/Android/data/%PACKAGENAME%/files/XVerseLog XVerseLogs

timeout /t 3

goto:eof
:Error
@echo.
@echo There was an error installing the game or the obb file. Look above for more info.
@echo.
@echo Things to try:
@echo Check that the device (and only the device) is listed with "%ADB$ devices" from a command prompt.
@echo Make sure all Developer options look normal on the device
@echo Check that the device has an SD card.
@pause
