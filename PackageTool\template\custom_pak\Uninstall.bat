setlocal
set ADB=.\tool\adb.exe
set PACKAGENAME=com.xverse.template
set DEVICE=
if not "%1"=="" set DEVICE=-s %1
for /f "delims=" %%A in ('%ADB% %DEVICE% shell "echo $EXTERNAL_STORAGE"') do @set STORAGE=%%A
@echo.
@echo Uninstalling existing application. Failures here can almost always be ignored.
%ADB% %DEVICE% uninstall %PACKAGENAME%
@echo.
echo Removing old data. Failures here are usually fine - indicating the files were not on the device.
%ADB% %DEVICE% shell rm -rf %STORAGE%/UnrealGame/XVerseVR_Oculus
%ADB% %DEVICE% shell rm -rf %STORAGE%/UnrealGame/UECommandLine.txt
%ADB% %DEVICE% shell rm -rf %STORAGE%/obb/%PACKAGENAME%
%ADB% %DEVICE% shell rm -rf %STORAGE%/Android/obb/%PACKAGENAME%
%ADB% %DEVICE% shell rm -rf %STORAGE%/Download/%PACKAGENAME%
@echo.
@echo Uninstall completed
