setlocal
set BatVersion=1.3
set ADB=.\tool\adb.exe
set DEVICE=
if not "%1"=="" set DEVICE=-s %1
for /f "delims=" %%A in ('%ADB% %DEVICE% shell "echo $EXTERNAL_STORAGE"') do @set STORAGE=%%A

SET PackageName=com.xverse.template
SET ProjectName=XVerseVR_Oculus
SET RecFilesPath=%STORAGE%/Android/data/%PackageName%/files/UnrealGame/%ProjectName%/%ProjectName%/Saved/CollectedPSOs/

SET PSOFolderPath=%cd%

%ADB% %DEVICE% pull %RecFilesPath% %PSOFolderPath%
MOVE %PSOFolderPath%\CollectedPSOs\*.rec.upipelinecache %PSOFolderPath%
rmdir /s /q %PSOFolderPath%\CollectedPSOs
@PAUSE
