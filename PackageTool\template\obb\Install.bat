setlocal
set BatVersion=1.3
set ADB=.\tool\adb.exe
set PACKAGENAME=com.xverse.template
set DEVICE=
if not "%1"=="" set DEVICE=-s %1
for /f "delims=" %%A in ('%ADB% %DEVICE% shell "echo $EXTERNAL_STORAGE"') do @set STORAGE=%%A
@echo BatVersion=%BatVersion%
@echo.
@echo Uninstalling existing application. Failures here can almost always be ignored.
@echo.
@echo Installing existing application. Failures here indicate a problem with the device (connection or storage permissions) and are fatal.
%ADB% %DEVICE% install .\asset\XVerseVR_Oculus-arm64.apk
@if "%ERRORLEVEL%" NEQ "0" goto Error
%ADB% %DEVICE% shell pm list packages %PACKAGENAME%
%ADB% %DEVICE% shell rm -r %STORAGE%/obb/%PACKAGENAME%
%ADB% %DEVICE% shell rm -r %STORAGE%/Android/obb/%PACKAGENAME%
%ADB% %DEVICE% shell rm -r %STORAGE%/Download/obb/%PACKAGENAME%
%ADB% %DEVICE% shell mkdir %STORAGE%/Android/obb/
%ADB% %DEVICE% shell mkdir %STORAGE%/Android/obb/%PACKAGENAME%
@echo.
@echo Installing new data. Failures here indicate storage problems (missing SD card or bad permissions) and are fatal.
@echo Starting Push obb file
for %%i in (.\asset\*.obb) do ( echo %%i 
%ADB% %DEVICE% push %%i %STORAGE%/Android/obb/%PACKAGENAME%
)

@echo.
@echo Starting Push pak file
for %%i in (.\asset\*.pak) do ( echo %%i 
%ADB% %DEVICE% push %%i %STORAGE%/Android/obb/%PACKAGENAME%
)

@echo.
@echo Grant READ_EXTERNAL_STORAGE and WRITE_EXTERNAL_STORAGE to the apk for reading OBB file or game file in external storage.
%ADB% %DEVICE% shell pm grant %PACKAGENAME% android.permission.READ_EXTERNAL_STORAGE
%ADB% %DEVICE% shell pm grant %PACKAGENAME% android.permission.WRITE_EXTERNAL_STORAGE

%ADB% %DEVICE% shell settings put global captive_portal_mode 0

@echo.
@echo Installation successful

rem dit not delete old media by default
rem @echo.
rem @echo Remove Old Video
rem %ADB% %DEVICE% shell rm -rf /storage/emulated/0/Download/*.mp4

%ADB% %DEVICE% shell mkdir %STORAGE%/Download/%PACKAGENAME%/
@echo.
@echo String copy video
for %%i in (.\video\*.mp4) do ( echo %%i 
%ADB% %DEVICE% push %%i %STORAGE%/Download/%PACKAGENAME%/
)
if "%ERRORLEVEL%" NEQ "0" goto Error
@echo.
@echo copy video completed, exiting.......
timeout /t 3

goto:eof
:Error
@echo.
@echo There was an error installing the game or the obb file. Look above for more info.
@echo.
@echo Things to try:
@echo Check that the device (and only the device) is listed with "%ADB$ devices" from a command prompt.
@echo Make sure all Developer options look normal on the device
@echo Check that the device has an SD card.
@pause
