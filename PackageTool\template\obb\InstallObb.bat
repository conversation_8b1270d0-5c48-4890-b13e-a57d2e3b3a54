setlocal
set BatVersion=1.3
set ADB=.\tool\adb.exe
set PACKAGENAME=com.xverse.template
set DEVICE=
if not "%1"=="" set DEVICE=-s %1
for /f "delims=" %%A in ('%ADB% %DEVICE% shell "echo $EXTERNAL_STORAGE"') do @set STORAGE=%%A
@echo BatVersion=%BatVersion%
@echo.
@echo Uninstalling existing application. Failures here can almost always be ignored.
@echo.
@echo Installing existing application. Failures here indicate a problem with the device (connection or storage permissions) and are fatal.
%

%ADB% %DEVICE% shell mkdir %STORAGE%/Android/obb/%PACKAGENAME%

@echo Remove Old Obb
%ADB% %DEVICE% shell rm -rf %STORAGE%/Android/obb/%PACKAGENAME%/*.obb

@echo Starting Push obb file

set OBBEXIST=
for %%i in (.\asset\*.obb) do ( echo %%i 
%ADB% %DEVICE% push %%i %STORAGE%/Android/obb/%PACKAGENAME%/
set OBBEXIST=OK
)

@echo OBBEXIST=%OBBEXIST%

if "%OBBEXIST%"=="" (
    @echo Push Obb File Error :: Not Found Obb Files In Asset Dir
    goto Error
)

@echo Copy Obb successful
goto:eof

:Error
@echo.
@echo There was an error copy the obb file. Look above for more info.
@echo.
@echo Things to try:
@echo Check that the device (and only the device) is listed with "%ADB$ devices" from a command prompt.
@echo Make sure all Developer options look normal on the device
@echo Check that the device has an SD card.
@pause
