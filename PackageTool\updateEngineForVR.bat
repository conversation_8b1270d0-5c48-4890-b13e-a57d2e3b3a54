@chcp 65001
@cls
setlocal

set engineSearchDir=C:\work\xverse\Engine5Output
set newEngineZipPath=%newEngineZipSambaPath%

for %%f in ("%newEngineZipPath%") do set newEngineZipFullBaseName=%%~nf%%~xf
for %%f in ("%newEngineZipPath%") do set newEngineZipBaseName=%%~nf

if exist "%engineSearchDir%\%newEngineZipBaseName%" (
    echo The engine folder already exists: %engineSearchDir%\%newEngineZipBaseName%
    exit /b 0
)

echo start copy targetEngine: %newEngineZipPath% to %engineSearchDir%
copy %newEngineZipPath% %engineSearchDir%
echo done copy targetEngine: %newEngineZipPath% to %engineSearchDir%

echo start unzip targetEngine %newEngineZipFullBaseName%
mkdir %engineSearchDir%\%newEngineZipBaseName%
bz x -o:%engineSearchDir%\%newEngineZipBaseName% %engineSearchDir%\%newEngineZipFullBaseName%
echo done unzip targetEngine %newEngineZipFullBaseName%

echo start delete zip %newEngineZipFullBaseName%
del %engineSearchDir%\%newEngineZipFullBaseName%
echo done delete zip %newEngineZipFullBaseName%


echo start clear dir %engineSearchDir%
for /f "delims=" %%f in ('dir /a-d /b "%engineSearchDir%"') do (
    del /f /q "%engineSearchDir%\%%f"
)
for /f "delims=" %%d in ('dir /ad /b "%engineSearchDir%"') do (
    if /i not "%%d"=="%newEngineZipBaseName%" (
        rd /s /q "%engineSearchDir%\%%d"
    )
)
echo done clear dir %engineSearchDir%

set "webhook_url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e26e016b-bc57-4c04-9c09-cce9939d091a"
set inform_content=*********** success update VR-Android-CI Engine version to %newEngineZipBaseName%
set "message_payload={\"msgtype\": \"text\", \"text\": {\"content\": \"%inform_content%\"}}"
curl -X POST %webhook_url% -H "Content-Type: application/json" -d "%message_payload%"

endlocal
exit /b 0
