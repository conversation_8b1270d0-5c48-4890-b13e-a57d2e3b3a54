import os
import sys
import zipfile
import argparse
import requests
import httpx
import shutil
from pathlib import Path
from corpwechatbot.chatbot import CorpWechatBot
from otaUploader import Storage
from qcloud_cos.cos_exception import CosClientError, CosServiceError
from qcloud_cos import CosConfig
from tqdm import tqdm
from httpx import HTTPError



class typedStorage(Storage):
    def __init__(self, config, type):
        super().__init__(config)
        self.type = type
        self.retry_num = 10
        self.downloaded_size = 0
        self.total_size = 0

    def exists(self, file_key):
        remote_key = ''
        try:
            if self.type == 'samba':
                remote_key = f"http://**********:8080/XCreatorEngine/{file_key}"
                resp = requests.get(remote_key, stream=True)
                resp.raise_for_status()
                self.total_size = int(resp.headers.get("Content-Length", "0"))
                return remote_key
            elif self.type == 'cos':
                remote_key = f"XVerseEngine/{file_key.split('/')[-1]}"
                resp = self.client.head_object(Bucket='xverse-creator-sh-1258211750', Key=remote_key)
                self.total_size = int(resp['Content-Length'])
                return remote_key
            else:
                raise NotImplementedError
        except Exception as e:
            print(f"get {file_key} meta info failed: {e}")
            return ''

    def download(self, remote_file_path, local_file_path):
        for i in range(self.retry_num):
            try:
                if os.path.exists(local_file_path):
                    self.downloaded_size = os.path.getsize(local_file_path)
                with tqdm(total=self.total_size, initial=self.downloaded_size, unit='B', unit_scale=True, desc='Downloading', unit_divisor=1024) as progress_bar:
                    if self.type == 'samba':
                        chunk_size = 1024 * 1024
                        headers = {"Range": f"bytes={self.downloaded_size}-"}
                        with httpx.stream('get', remote_file_path, headers=headers) as response:
                            if response.status_code not in (200, 206):
                                raise HTTPError(f"Failed to download file: {response.status_code}")
                            with open(local_file_path, 'ab') as f:
                                 for chunk in response.iter_bytes(chunk_size=chunk_size):
                                    f.write(chunk)
                                    progress_bar.update(len(chunk))
                                    # 计算百分比并设置进度条的附加信息
                                    # percentage = (progress_bar.n / self.total_size) * 100
                                    # progress_bar.set_postfix(percentage=f"{percentage:.2f}%")  # 显示百分比
                        return True
                    elif self.type == 'cos':
                        def progress_callback(consumed_bytes, total_bytes):
                            # 更新进度条，转换为MB单位
                            progress_bar.update((consumed_bytes - progress_bar.n))  # 单位为MB
                            # 计算百分比
                            # percentage = (consumed_bytes / total_bytes) * 100
                            # progress_bar.set_postfix(percentage=f"{percentage:.2f}%")  # 显示百分比
                        response = self.client.download_file(Bucket='xverse-creator-sh-1258211750', Key=remote_file_path, DestFilePath=local_file_path, PartSize=16, MAXThread=20, EnableCRC=True, progress_callback=progress_callback)
                        return True
                    else:
                        raise NotImplementedError
            except (CosClientError, CosServiceError) as e:
                print(f"download {remote_file_path} failed: {e}")
            except HTTPError as e:
                print(f"download {remote_file_path} failed: {e}")
            except Exception as e:
                print(f"download {remote_file_path} failed, unexpected error: {e}")
                return False
        
        return False


def extract(compress_path, dst_path):
    try:
        commitId, commitInfo = '', ''
        with zipfile.ZipFile(compress_path, mode='r') as archive:
            file_list = archive.namelist()
            with tqdm(total=len(file_list), unit="file", desc="Extracting") as progress_bar:
                for file in file_list:
                    archive.extract(file, dst_path)
                    progress_bar.update(1)
        with open(os.path.join(dst_path, 'Engine\\Version\\EngineVersion.ini'), 'r', encoding='utf-8') as f:
            for line in f.readlines():
                if line.startswith("CommitId"):
                    commitId = line.split(":")[-1].strip()
                if line.startswith("CommitInfo"):
                    commitInfo = line[11:].strip()
        return commitId, commitInfo
    except Exception as e:
        print(f"decompress {compress_path} failed, {e}")
        return "", ""

def clearOldEngine():
    try:
        engineDirPath = Path(args.engineDir)
        oldItems:list[Path] = []
        for p in engineDirPath.iterdir():
            if 'XVerseEngine' in p.name:
                if p.is_dir():
                    workStatePath = p / "Engine\\Version\\EngineNoWork.ini"
                    if workStatePath.exists():
                        oldItems.append(p)                    
                        continue
                    versionPath = p / "Engine\\Version\\EngineVersion.ini"
                    with open(versionPath, 'r', encoding='utf-8') as f:
                        for line in f.readlines():
                            if line.startswith("Branch"):
                                branch = line.split(':')[-1].strip()
                                if branch == args.engineBranch:
                                    oldItems.append(p)
                else:
                    oldItems.append(p)
        
        for item in oldItems:
            print(f"{item} removed")
            if item.is_dir():
                shutil.rmtree(item)
            else:
                item.unlink()
        return True
    except Exception as e:
        print(f"clear old engine failed: {e}")
        return False

def main():
    bot = CorpWechatBot(key='c094f2f9-ba98-43aa-8f0d-96564e72da15')
    config = CosConfig(Region='ap-shanghai', SecretId='AKIDIagW9wbotiOuDGjREo96Al9FAkodlRSH', SecretKey='kNlKfGdp67vaMvyPsPhmbppd4npeUjiS', Token=None, Scheme='https', Timeout=1800)
    storage = typedStorage(config, args.storageType)
    # remove old and noWork engine
    ret = clearOldEngine()
    if not ret:
        bot.send_text(content=f"{args.node} {args.engineDir} 清理旧引擎失败")
        sys.exit(1)
    remote_path = storage.exists(f"{args.engineBranch}/{args.engineZipName}")
    if len(remote_path) == 0:
        bot.send_text(content=f"{args.storageType}不存在{args.engineBranch}/{args.engineZipName}")
        sys.exit(1)
    local_zip_path = os.path.join(args.engineDir, args.engineZipName)
    ret = storage.download(remote_path, local_zip_path)
    if not ret:
        bot.send_text(content=f"{args.storageType}下载{args.engineBranch}/{args.engineZipName}失败")
        sys.exit(1)
    commitId, commitInfo = extract(local_zip_path, os.path.join(args.engineDir, Path(local_zip_path).stem))
    if len(commitId) == 0 or len(commitInfo) == 0:
        bot.send_text(content=f"{args.node} extract {args.engineBranch}/{args.engineZipName}失败")
        sys.exit(1)
    os.remove(local_zip_path)
    bot.send_text(content=f"{args.node} 更新 {args.engineBranch}预编译引擎成功\ncommitId:{commitId}\ncommitInfo:{commitInfo}")
    sys.exit(0)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(prog='EngineUpdate', description='')
    parser.add_argument('--engineBranch', type=str, required=True, help='引擎分支')
    parser.add_argument('--storageType', type=str, default='samba', help='预编译引擎zip存储方式')
    parser.add_argument('--engineZipName', type=str, required=True, help='预编译引擎zip文件名')
    parser.add_argument('--engineDir', type=str, required=True, help='预编译引擎目录')
    parser.add_argument('--node', type=str, help='要更新预编译引擎的主机')
    args = parser.parse_args()
    main()