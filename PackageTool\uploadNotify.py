import sys
import os
import pickle
import ciTools
import cosUtil
import httpx
import json
from datetime import datetime
from ciTools import BuildProjectParam
from otaUploader import OTAStorage
from assetMgr import getFileSize
from buildProject import getAllPanoramicVideoInfo, printLog, stopCustomPak



def retryHttp(url, data, mode, silent_response=True):
    retryCnt = 0
    with httpx.Client(timeout=httpx.Timeout(timeout=30.0)) as client:
        while True:
            try:
                if mode == "post":
                    resp = client.post(url, json=data)
                elif mode == "get":
                    resp = client.get(url, params=data)
                resp.raise_for_status()
                respInfo = resp.json()
                if respInfo['code'] == 0:
                    if silent_response:
                        return True, ''
                    else:
                        return respInfo, ''
                else:
                    raise Exception(f"request failed:{respInfo['msg']}")

            except Exception as e:
                if retryCnt < 5:
                    retryCnt += 1
                else:
                    return None, str(e)

def getStoreIdMap():
    storeIDMap = {}
    # allStoreInfo, msg = retryHttp(url='http://*************:6754/shop/get_store_simple_list', data="", mode="get", silent_response=False)
    allStoreInfo, msg = retryHttp(url='https://lbvr-api.xverse.cn/shop/get_store_simple_list', data="", mode="get", silent_response=False)
    if not allStoreInfo:
        print(f"获取所有门店信息失败: {msg}")
    else:
        for storeInfo in allStoreInfo['data']['storeList']:
            if not storeIDMap.get(storeInfo['storeName']):
                storeIDMap[storeInfo['storeName']] = storeInfo['storeId']
    return storeIDMap
                    

if __name__ == "__main__":

    try:
        uploadTemppath = sys.argv[1]
        # 加载参数
        with open(uploadTemppath, 'rb') as f:
            buildParam = pickle.load(f)
        overrideConfigPath = os.path.join(buildParam.panoVideoDstDir, "OverrideConfig.json")
        cosUtil.sendDevMessage(f'{buildParam.projectBuildUrl}: uploadTempPath{uploadTemppath} uploadNotify start')
        otaStorage = OTAStorage()
        if buildParam.bOTA:
            buildParam.otaUploadStartTime = datetime.now()
            cosUtil.sendDevMessage(f'{buildParam.projectBuildUrl}: start ota upload')
            apkLocalPath = os.path.join(buildParam.fullProjectOutPutDir, "asset","XVerseVR_Oculus-arm64.apk")
            pakLocalPath = os.path.join(buildParam.fullProjectOutPutDir, "asset","xverse-Android_ASTC.pak")
            lowModelPakLocalPath = os.path.join(buildParam.fullProjectOutPutDir, "asset", "xverse-lowmodel-Android_ASTC.pak")
            
            if buildParam.apkCosPrefix:
                apkCosPrefix = buildParam.apkCosPrefix
            else:
                apkCosPrefix = f"{buildParam.videoName}/{buildParam.storeName}"
            apkCosPath = f"{apkCosPrefix}/apk/{buildParam.versionDisplayName}/XVerseVR_Oculus-arm64.apk"

            if buildParam.pakCosPrefix:
                pakCosPrefix = buildParam.pakCosPrefix
            else:
                pakCosPrefix = f"{buildParam.videoName}/{buildParam.storeName}"

            pakCosPath = f"{pakCosPrefix}/pak/{buildParam.xsdkVersion}/xverse-Android_ASTC.pak"
            
            if buildParam.lowmodelPakCosPrefix:
                lowmodelPakCosPrefix = buildParam.lowmodelPakCosPrefix
            else:
                lowmodelPakCosPrefix = f"{buildParam.videoName}/{buildParam.storeName}"
            lowModelPakCosPath = f"{lowmodelPakCosPrefix}/lowmodel/{buildParam.xsdkLowModelVersion}/xverse-lowmodel-Android_ASTC.pak"
            

            buildParam.stateDescribe = ''
            
            if os.path.exists(apkLocalPath):
                buildParam.apkCosPath, msg = otaStorage.uploadWithCrc64(apkLocalPath, apkCosPath, buildParam.bForceUploadOTA)
                buildParam.apkSize = getFileSize(apkLocalPath)
                printLog(f"upload to {apkCosPath}: {msg}")
                if not buildParam.apkCosPath:
                    buildParam.stateDescribe += f"OTA-apk版本冲突：{msg}\n"
            if os.path.exists(pakLocalPath):
                buildParam.pakCosPath, msg = otaStorage.uploadWithCrc64(pakLocalPath, pakCosPath, buildParam.bForceUploadOTA)
                buildParam.pakSize = getFileSize(pakLocalPath)
                printLog(f"upload to {pakCosPath}: {msg}")
                if not buildParam.pakCosPath:
                    buildParam.stateDescribe += f"OTA-主pak版本冲突：{msg}\n"
            if os.path.exists(lowModelPakLocalPath):
                buildParam.lowModelPakCosPath, msg = otaStorage.uploadWithCrc64(lowModelPakLocalPath, lowModelPakCosPath, buildParam.bForceUploadOTA)
                buildParam.lowmodelPakSize = getFileSize(lowModelPakLocalPath)
                printLog(f"upload to {lowModelPakCosPath}: {msg}")
                if not buildParam.lowModelPakCosPath:
                    buildParam.stateDescribe += f"OTA-低模pak版本冲突：{msg}\n"
            if buildParam.panoVideoDstDir:
                allPanoramicVideoInfo = getAllPanoramicVideoInfo(buildParam, overrideConfigPath)
                ciTools.removeFile(overrideConfigPath)
                buildParam.panoramicVideoSize = allPanoramicVideoInfo['size']
                if len(allPanoramicVideoInfo['data']) > 0:
                    videoCosPath, bUpload =  otaStorage.geSertPanoramicOTAInfo(allPanoramicVideoInfo)
                    if bUpload:
                        buildParam.videoCosPath, msg = otaStorage.uploadDir(videoCosPath, buildParam.panoVideoDstDir+"\\")
                        printLog(f"upload to {videoCosPath}: {msg}")
                    else:
                        buildParam.videoCosPath = videoCosPath
                        printLog(f"{buildParam.xsdkAppId}-{buildParam.xsdkReleaseId} reuse {videoCosPath} panoramic assets")
                    if not buildParam.videoCosPath:
                        buildParam.stateDescribe += f"全景视频查询数据库失败，请联系开发人员处理\n"
            cosUtil.sendDevMessage(f'{buildParam.projectBuildUrl}: ota verify end')
            msg = otaStorage.startUpload()
            if msg:
                buildParam.stateDescribe += f'OTA上传: {msg}'
            cosUtil.sendDevMessage(f'{buildParam.projectBuildUrl}: ota upload end')
            
        buildAllInfo = (buildParam.branch, buildParam.commitId, buildParam.engineBranch, buildParam.engineCommitId,
            buildParam.xsdkAppId, buildParam.xsdkReleaseId, buildParam.pakMode, buildParam.xsdkEnv, buildParam.xsdkVersion, buildParam.locateType,
            buildParam.xsdkMarkerFile, buildParam.xsdkLowModelVersion, buildParam.xsdkLowModelPath, buildParam.xsdkSceneId,
            buildParam.videoName, buildParam.storeName, buildParam.packageName, buildParam.applicationDisplayName,
            buildParam.storeVersion, buildParam.versionDisplayName, buildParam.projectSambaDirForNotify, buildParam.projectBuildUrl,
            buildParam.apkCosPath, buildParam.pakCosPath, buildParam.videoCosPath, buildParam.lowModelPakCosPath,
            buildParam.ibrAssetSize, buildParam.pbrAssetSize, buildParam.panoramicVideoSize, buildParam.apkSize, buildParam.pakSize, buildParam.lowmodelPakSize,
            buildParam.creatTime, buildParam.pullProjectStartTime, buildParam.pullEngineStartTime, buildParam.downloadAssetStartTime,
            buildParam.buildStartTime, buildParam.custompakStartTime, buildParam.sambaUploadStartTime, buildParam.otaUploadStartTime,    
            buildParam.executor, buildParam.buildResultCode, buildParam.buildResultMsg)
        ret, msg = otaStorage.upsertBuildParameterInfo(buildAllInfo)
        
        if not ret:
            cosUtil.sendDevMessage(f'{buildParam.projectBuildUrl}: 写入build 参数和统计信息失败,{msg}\n {buildAllInfo}')
        if buildParam.removeDataAfterBuild == True:
            printLog("buildProjectMain remove Build Data {}".format(buildParam.fullProjectOutPutDir))
            ciTools.removeDir(buildParam.fullProjectOutPutDir)
            ciTools.removeFile(buildParam.fullProjectOutPutZipPath)
            ciTools.removeDir(buildParam.panoVideoDstDir)
            ciTools.removeFile(uploadTemppath)

        if buildParam.bRelease:
            buildParam.cdStateDescribe = ''
            if buildParam.bOnlyLowmodelPak:
                # 上传路线
                if buildParam.anchorConfigJsonData:
                    landData = {
                        "appId": buildParam.xsdkAppId,
                        "vrLandName": os.path.splitext(buildParam.xsdkMarkerFile)[0],
                        "anchorConfig": json.dumps(buildParam.anchorConfigJsonData),
                        "targetStoreId": buildParam.dstStoreId
                    }
                    # landRet, msg = retryHttp(url="http://*************:6754/app/auto_create_xverse_prod_vr_land", data=landData, mode='post')
                    landRet, msg = retryHttp(url="https://lbvr-api.xverse.cn/app/auto_create_xverse_prod_vr_land", data=landData, mode='post')
                    if not landRet:
                        buildParam.cdStateDescribe += f"上传路线失败：{msg}\n"
                    
                # 获取app主包路径
                primaryPackageInfo, msg = retryHttp(url=f"https://console-api.xverse.cn/release/console/app_mgt/v1/apps/{buildParam.xsdkAppId}/lbvr_package_path", data= {"appId": buildParam.xsdkAppId}, mode='get', silent_response=False)
                if not primaryPackageInfo:
                    buildParam.cdStateDescribe += f"获取{buildParam.xsdkAppId}主包路径失败：{msg}\n"
                else:
                    for pinfo in primaryPackageInfo['data']['packageInfo']:
                        # 校验
                        # if buildParam.otaVersion and buildParam.remarks:
                        verifyRet, msg = otaStorage.deployVerify([pinfo['apkPath'], pinfo['pakPath'], pinfo['videoPath'], buildParam.lowModelPakCosPath])
                        if verifyRet:
                            # 发布到lbvr Console
                            releaseData = {
                                "appId": buildParam.xsdkAppId,
                                "version": buildParam.otaVersion + '--' + pinfo['deviceType'],
                                "desc": buildParam.remarks + '--' + pinfo['deviceType'],
                                "targetStoreId": buildParam.dstStoreId,
                                "apkPath": pinfo['apkPath'],
                                "pakPath": pinfo['pakPath'],
                                "videoPath": pinfo['videoPath'],
                                "extraFileList": [
                                    {
                                        "path": buildParam.lowModelPakCosPath,
                                        "targetPath": "mountPak"
                                    }
                                ]
                            }
                            # releaseRet, msg = retryHttp(url="http://*************:6754/ota/auto_create_xverse_prd_version", data=releaseData, mode='post')
                            releaseRet, msg = retryHttp(url="https://lbvr-api.xverse.cn/ota/auto_create_xverse_prd_version", data=releaseData, mode='post')
                            if not releaseRet:
                                buildParam.cdStateDescribe += f"{releaseData['version']} OTA发布失败：{msg}\n"
                            else:
                                buildParam.cdStateDescribe += f"{releaseData['version']} OTA发布成功\n"
                        else:
                            buildParam.cdStateDescribe += f"OTA发布失败：{msg}\n"

        ciTools.sendBuildResultMessage(buildParam)
        sys.exit(0)
    except Exception as e:
        ciTools.sendBuildResultMessage(buildParam)
        cosUtil.sendDevMessage(f"{buildParam.projectBuildUrl}: uploadNotify failed: {e}")
        sys.exit(1)