#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import os
import threading
import time
import ciTools
import multiprocessing
from enum import Enum
import cosUtil
import logging
import signal
import BuildHelper
import buildProject
from ciTools import BuildProjectParam
import xverseUtil

logTag = "xverseCIBackProcess"


class XCIRunParam:
    def __init__(self):
        self.embedUploadCosService = True
        self.embedCaptureCheckService = False
        self.embedProjectCodeBuild = False
        self.notifyForTest = False

        self.embed_new_cos_upload_service = True


class XCIWorkerState():
    Free = 0
    Processing = 1


class UploadCosWorker(threading.Thread):
    def __init__(self, runParam: XCIRunParam):
        threading.Thread.__init__(self)
        self.bStop = False
        self.tickCount = 0
        self.workState = XCIWorkerState.Free
        self.ciRunParam = runParam
        self.condition = threading.Condition()

    def workerWait(self, delay):
        with self.condition:
            self.condition.wait(delay)

    def stopWorker(self):
        self.bStop = True
        with self.condition:
            self.condition.notify()
        printLog("uploadCosWorker stopWorker")

    def run(self):
        while self.bStop == False:
            # printLog("uploadCos tickCount {}".format(self.tickCount))
            self.tickCount += 1
            if self.workState == XCIWorkerState.Processing:
                printLog("worker is processing wait {}s".format(10))
                self.workerWait(10)
            else:
                nextTask = ciTools.getNextCaptureTask()
                if nextTask is not None:
                    taskFilePath = nextTask.taskFilePath
                    uploadFilePath = nextTask.uploadFilePath
                    cosPath = nextTask.cosPath
                    printLog("start process new task {}".format(taskFilePath))
                    self.workState = XCIWorkerState.Processing
                    if os.path.exists(uploadFilePath):
                        nextTask.cosState = ciTools.CaptureCosInfo.StateUploading
                        self.sendNotify(nextTask)
                        retCode = ciTools.uploadToCosByDefault(cosPath, uploadFilePath)
                        if retCode == True:
                            printLog("upload Capture Success {}".format(taskFilePath))
                            nextTask.cosState = ciTools.CaptureCosInfo.StateUploadSucc
                            self.workState = XCIWorkerState.Free
                            captureRecordFilePath = nextTask.captureRecordFilePath
                            captureInfo = ciTools.readCaptureCosInfoByPath(captureRecordFilePath)
                            if captureInfo is not None:
                                captureInfo.cosState = ciTools.CaptureCosInfo.StateUploadSucc
                                ciTools.writeCaptureCosInfo(captureRecordFilePath, captureInfo)
                            ciTools.updateCaptureTask(nextTask)
                            self.sendNotify(nextTask)
                            self.workerWait(5)
                            # remove task after upload success
                            # os.remove(taskFilePath)
                        else:
                            nextTask.cosState = ciTools.CaptureCosInfo.StateUploadFail

                            nextTask.uploadCosFailCount = nextTask.uploadCosFailCount + 1
                            self.workState = XCIWorkerState.Free
                            captureRecordFilePath = nextTask.captureRecordFilePath
                            captureInfo = ciTools.readCaptureCosInfoByPath(captureRecordFilePath)
                            if captureInfo is not None:
                                captureInfo.cosState = ciTools.CaptureCosInfo.StateUploadFail
                                ciTools.writeCaptureCosInfo(captureRecordFilePath, captureInfo)
                            ciTools.updateCaptureTask(nextTask)
                            self.sendNotify(nextTask)
                            self.workerWait(5)

                    else:
                        printLog("upload cos task error local file not exist {}".format(uploadFilePath))
                        self.workState = XCIWorkerState.Free
                        os.remove(taskFilePath)
                    self.workerWait(10)
                else:
                    # printLog("UploadCosThread is free wait {}s".format(20))
                    self.workState = XCIWorkerState.Free
                    self.workerWait(20)

    def sendNotify(self, task: ciTools.CaptureUploadTaskInfo):
        cosPath = task.cosPath
        cosState = task.cosState
        branch = task.projectBranch
        mainVersion = task.mainVersion
        commitVersion = task.commitVersion
        projectName = task.projectName
        targetPlatform = task.targetPlatform
        taskUid = task.taskUid
        currentTime = ciTools.getCurrentDateTimeStr1()

        msg = cosUtil.BuildNotifyMessage()

        if cosState == ciTools.CaptureCosInfo.StateUploadFail:
            retDesc = "失败"
            msg.title = "{}({})上传Cos{}".format(projectName, targetPlatform, retDesc)
        elif cosState == ciTools.CaptureCosInfo.StateUploading:
            retDesc = "开始上传Cos..."
            msg.title = "{}({}){}".format(projectName, targetPlatform, retDesc)
        elif cosState == ciTools.CaptureCosInfo.StateUploadSucc:
            retDesc = "成功"
            msg.title = "{}({})上传Cos{}".format(projectName, targetPlatform, retDesc)
        msg.buildTime = currentTime
        if cosState == ciTools.CaptureCosInfo.StateUploadSucc:
            msg.outPutPath = ciTools.getFullCosPath(cosPath)
        msg.branchName = branch
        msg.version = "{}.{}".format(mainVersion, commitVersion)
        msg.taskUid = taskUid
        msg.forTest = self.ciRunParam.notifyForTest
        msg.outPutPath = ciTools.getFullCosPath(task.cosPath)
        cosUtil.sendWeixinTipsMessage(msg)


class UploadDefaultCosWorker(threading.Thread):
    def __init__(self, run_param: XCIRunParam):
        threading.Thread.__init__(self)
        self.b_stop = False
        self.tick_count = 0
        self.work_state = XCIWorkerState.Free
        self.ci_run_param = run_param
        self.condition = threading.Condition()

    def workerWait(self, delay):
        with self.condition:
            self.condition.wait(delay)

    def stopWorker(self):
        self.b_stop = True
        with self.condition:
            self.condition.notify()
        printLog("uploadCosWorker stopWorker")

    def run(self):
        while not self.b_stop:
            self.tick_count += 1
            if self.work_state == XCIWorkerState.Processing:
                printLog("default worker is processing wait {}s".format(10))
                self.workerWait(10)
            else:
                # 获取最新任务，当前只有引擎任务
                next_task = ciTools.getNextEngineTask()
                if next_task is not None:
                    task_file_path = next_task.task_file_path
                    upload_file_path = next_task.upload_file_path
                    cos_path = next_task.cos_path
                    printLog("start process new task {}".format(task_file_path))
                    # 修改工作器最新状态为执行中
                    self.work_state = XCIWorkerState.Processing
                    if os.path.exists(upload_file_path):
                        #  路径下文件存在则 1:发送通知、2: 发起上传
                        next_task.cos_state = ciTools.CosUploadStatus.StateUploading
                        self.sendNotify(next_task)
                        ret_code = ciTools.uploadToCosByDefault(cos_path, upload_file_path)
                        if ret_code:
                            printLog("upload Engine Success {}".format(task_file_path))
                            next_task.cos_state = ciTools.CosUploadStatus.StateUploadSuccess
                            # 释放工作器
                            self.work_state = XCIWorkerState.Free
                            # 更新任务状态，发送通知
                            ciTools.updateEngineCosTask(next_task)
                            self.sendNotify(next_task)
                        else:
                            next_task.cos_state = ciTools.CosUploadStatus.StateUploadFail
                            next_task.fail_count = next_task.fail_count + 1
                            # 释放工作器
                            self.work_state = XCIWorkerState.Free
                            ciTools.updateCaptureTask(next_task)
                            self.sendNotify(next_task)
                    else:
                        # 不存在文件则打印信息跳过，暂时不接入通知
                        printLog("upload cos task error local file not exist {}".format(upload_file_path))
                        self.work_state = XCIWorkerState.Free
                        os.remove(task_file_path)
                else:
                    self.work_state = XCIWorkerState.Free

                self.workerWait(20)

    def sendNotify(self, task: ciTools.EngineCosTaskInfo):
        cos_state = task.cos_state
        branch = task.project_branch
        commit_version = task.commit_version
        cos_path = task.cos_path
        msg = cosUtil.BuildNotifyMessage()

        current_time = ciTools.getCurrentDateTimeStr1()

        if cos_state == ciTools.CosUploadStatus.StateUploadFail:
            retDesc = "失败"
            msg.title = "{}上传Cos{}".format(branch, retDesc)
        elif cos_state == ciTools.CosUploadStatus.StateUploading:
            retDesc = "开始上传Cos..."
            msg.title = "{}{}".format(branch, retDesc)
        elif cos_state == ciTools.CosUploadStatus.StateUploadSuccess:
            retDesc = "成功"
            msg.title = "{}上传Cos{}".format(branch, retDesc)
        msg.buildTime = current_time
        if cos_state == ciTools.CosUploadStatus.StateUploadSuccess:
            msg.outPutPath = ciTools.getFullCosPath(cos_path)
        msg.branchName = branch
        msg.version = "{}.{}".format(branch, commit_version)
        msg.outPutPath = ciTools.getFullCosPath(task.cos_path)
        cosUtil.sendWeixinTipsMessage(msg)


class CaptureCheckWorker(threading.Thread):
    def __init__(self, runParam: XCIRunParam):
        threading.Thread.__init__(self)
        self.bStop = False
        self.tickCount = 0
        self.workState = XCIWorkerState.Free
        self.ciRunParam = runParam
        self.condition = threading.Condition()

    def stopWorker(self):
        self.bStop = True
        with self.condition:
            self.condition.notify()
        printLog("CaptureCheckWorker stopWorker")

    def workerWait(self, delay):
        with self.condition:
            self.condition.wait(delay)

    def workerNotify(self):
        with self.condition:
            self.condition.notifyAll()

    def run(self):
        while self.bStop == False:
            pass

    def getNextCaptureCheckTask(self):
        pass

    def downloadSkin(self):
        pass

    def downloadCapturePackage(self):
        pass

    def configCapturePackage(self):
        pass

    def verifyCapturePackage(self):
        pass


class ProjectCodeBuildWorker(threading.Thread):
    def __init__(self, runParam: XCIRunParam):
        threading.Thread.__init__(self)
        self.bStop = False
        self.tickCount = 0
        self.workState = XCIWorkerState.Free
        self.ciRunParam = runParam
        self.condition = threading.Condition()
        self.buildBranchInfo = {}

    def stopWorker(self):
        self.bStop = True
        with self.condition:
            self.condition.notify()
        printLog("ProjectCodeBuildWorker stopWorker")

    # seconds
    def workerWait(self, delay):
        with self.condition:
            self.condition.wait(delay)

    def run(self):
        while self.bStop == False:
            if self.tickCount < 1:
                ubtRunning = ciTools.checkUBTProcess()
                if ubtRunning == False:
                    buildParam = BuildProjectParam()
                    self.prepareBuild(buildParam)
                    branch = buildParam.branch
                    commitVersion = self.getBranchCommitVersion(buildParam.projectSourceDir, branch)
                    lastBuildVersion = 0
                    if branch in self.buildBranchInfo:
                        lastBuildVersion = self.buildBranchInfo.get(branch)
                    if commitVersion > 0 and commitVersion > lastBuildVersion:
                        self.buildBranchInfo[branch] = commitVersion
                        buildRet = buildProject.build(buildParam)
                        printLog("ProjectCodeBuild Result {}".format(buildRet))
                        self.tickCount = self.tickCount + 1
                        self.workerWait(20)
                else:
                    printLog("wait for ubt runing")
                    self.workerWait(60)

    def prepareBuild(self, buildParam: BuildProjectParam):
        buildParam.projectName = "XStudio"
        buildParam.targetPlatform = "Win64"
        buildParam.gameConfigurations = "Development"
        buildParam.branch = "dev"
        buildParam.projectBranch = "dev"
        # git
        buildParam.embedGitClean = False
        buildParam.embedPullLatestCode = False
        buildParam.forceResetLocalBranch = False
        buildParam.embedSwitchBranch = False

        # build param
        buildParam.embedProjectCook = False
        buildParam.embedProjectArchive = False
        buildParam.embedProjectDDC = False
        buildParam.embedProjectInstalled = False
        buildParam.embedProjectPak = False
        buildParam.includeCrashReporter = False
        buildParam.includeDebugFiles = False
        buildParam.includePrerequisites = False
        buildParam.embedProjectStage = False

        # upload
        buildParam.embedUploadSamba = False
        buildParam.includeXCapture = False
        buildParam.embedUploadCos = False

    def getBranchCommitVersion(self, dir, branch):
        gitVersionCmd = "git rev-list --count --first-parent {0}".format(branch)
        os.chdir(dir)
        result = os.popen(gitVersionCmd)
        context = result.buffer.read().decode('utf-8')
        commitVersion = 0
        for line in context.splitlines():
            printLog("gitVersionCmd=" + line)
            commitVersion = line
        result.close()
        return int(commitVersion)


def printLog(msg):
    logger.debug("{}".format(msg))
    print("{} -- {}".format(logTag, msg))


if __name__ == "__main__":
    os.makedirs(ciTools.CIConfig.xCiBackProcessLogDir, exist_ok=True)
    currentDateTimeStr = ciTools.currentYMDHMS()
    logFile = os.path.join(ciTools.CIConfig.xCiBackProcessLogDir, "backProcess_{}.log".format(currentDateTimeStr))
    logger = logging.getLogger("xverseCIBackProcessApp")
    logger.setLevel(logging.DEBUG)
    fileHandler = logging.FileHandler(logFile, mode='w')
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    fileHandler.setFormatter(formatter)
    logger.addHandler(fileHandler)
    # logging.basicConfig(filename=logFile, filemode='w', format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.DEBUG)
    printLog("Start xverseCIBackProcessApp user Input Param {}".format(sys.argv))
    pid = os.getpid()
    currProcess = multiprocessing.current_process()
    printLog("pid:{}, name={}".format(pid, currProcess.name))
    pidFile = open(ciTools.CIConfig.xBackProcessPidPath, "w")
    pidFile.write("{}".format(pid))
    pidFile.close()

    ciRunParam = XCIRunParam()
    xverseUtil.readParams(sys.argv, ciRunParam)
    printLog("reandParams {}".format(ciTools.object2json(ciRunParam)))
    upLoadWorker = None
    if ciRunParam.embedUploadCosService == True:
        upLoadWorker = UploadCosWorker(ciRunParam)
        upLoadWorker.start()

    default_cos_worker = None
    if ciRunParam.embed_new_cos_upload_service:
        default_cos_worker = UploadDefaultCosWorker(ciRunParam)
        default_cos_worker.start()

    checkerWorker = None
    if ciRunParam.embedCaptureCheckService == True:
        checkerWorker = CaptureCheckWorker(ciRunParam)
        checkerWorker.start()
    projectCodeWorker = None
    if ciRunParam.embedProjectCodeBuild == True:
        projectCodeWorker = ProjectCodeBuildWorker(ciRunParam)
        projectCodeWorker.start()
    exitLoop = False
    while exitLoop == False:
        cmd = str(input())
        printLog("xverseCIBackInputCmd={}".format(cmd))
        if cmd == "exit":
            exitLoop = True

    if upLoadWorker is not None:
        upLoadWorker.stopWorker()
    if checkerWorker is not None:
        checkerWorker.stopWorker()
    if projectCodeWorker is not None:
        projectCodeWorker.stopWorker()
    if default_cos_worker is not None:
        default_cos_worker.stopWorker()
    printLog("grace stop")
    # time.sleep(10)
    # processList = psutil.process_iter()
    # for process in processList:
    #     if process.pid == currProcess.pid:
    #         printLog("process:{}, pid={}".format(process.name(), process.pid))
    #         os.kill(process.pid, signal.SIGTERM)
