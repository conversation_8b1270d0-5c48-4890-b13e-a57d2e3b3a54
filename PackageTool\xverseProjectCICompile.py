#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import os
import threading
import time
import ciTools
import multiprocessing
from enum import Enum
import cosUtil
import logging
import signal
import BuildHelper
import buildProject
from ciTools import BuildProjectParam
from ciTools import BranchInfo
import buildProject

logTag = "xverseProjectCICompile"
class XCIProjectCompileParam(BuildProjectParam):
    def __init__(self):
        super(XCIProjectCompileParam, self).__init__()
        self.embedCICompileService = True
        self.branchList = []
        self.embedExitWhenBuildFail = False
        self.projectName = "XStudio"
        self.targetPlatform = "Win64"
        self.gameConfigurations = "Development"
        self.branch = "dev"
        self.channel = "自动编译"

        # notify
        self.disableNotifyMessageAtSucc = True


class XCompileRecordBranchInfo():
    def __init__(self):
        self.branchName : str = None
        self.targetPlatform : str = None
        self.lastBuildTime = 0
        self.lastBuildErrorCode : int = 0
        self.lastBuildErrorMsg : str = None
        self.commitVersion : int = 0


class XCompileRecord():
    def __init__(self):
        self.lastBuildTime = 0
        self.lastBuildBranch : str = None
        self.branchInfos = {}
        #self.actionList = []

class ProjectCodeBuildWorker():
    def __init__(self, runParam : XCIProjectCompileParam):
        self.runParam = runParam
        self.compileRecord : XCompileRecord = None
        self.buildRecordPath : str= None

    def init(self):
        self.prepareBuild(self.runParam)
        cacheDir = os.path.join(self.runParam.packageToolDir, "Cache")
        if not os.path.exists(cacheDir):
            os.makedirs(cacheDir)
        self.buildRecordPath = os.path.join(cacheDir, "ci_projet_compile_record.json")
        self.loadCompileRecord()

    def startWorker(self):
        buildResult = 2
        buildBranchName = None
        targetPlatform = self.runParam.targetPlatform
        for branchName in self.runParam.branchList:
            ret = self.checkBranchBuild(branchName, targetPlatform)
            if ret == True:
                buildBranchName = branchName
                break
        if buildBranchName is not None:
            branchKey = f"{buildBranchName}_{targetPlatform}"
            printLog("buildProjectWithBranch start with branch ({})".format(branchKey))
            branchBuildInfo : XCompileRecordBranchInfo = self.compileRecord.branchInfos[branchKey]
            branchBuildInfo.lastBuildTime = ciTools.getCurrentDateTimeStr1()
            #record
            self.compileRecord.lastBuildBranch = branchName
            self.compileRecord.lastBuildTime = ciTools.getCurrentDateTimeStr1()
            ret = self.buildProjectWithBranch(self.runParam)
            branchBuildInfo.lastBuildErrorCode = self.runParam.buildResultCode
            branchBuildInfo.lastBuildErrorMsg = self.runParam.buildResultMsg
            printLog("buildProjectWithBranch end : code {}, msg {}".format(self.runParam.buildResultCode, self.runParam.buildResultMsg))
            self.saveCompileRecord()
            if ret == True:
                buildResult = 0
            else:
                buildResult = 1
        else:
            buildResult = 2
            printLog("buildProjectWithBranch error this branch ({}) no need build".format(self.runParam.branch))
        return buildResult

    def checkBranchBuild(self, buildBranch, targetPlatform):
        printLog("checkBranchBuild branch %s , %s"%(buildBranch, targetPlatform))
        branchInfo = self.getBranchCommitVersion(self.runParam.projectSourceDir, buildBranch)
        branchKey = f"{buildBranch}_{targetPlatform}"
        if branchInfo is not None:
            commitVersion = branchInfo.commitVersion
            lastBuildCommitVersion = 0
            if branchKey in self.compileRecord.branchInfos:
                lastBuildBranchInfo : XCompileRecordBranchInfo = self.compileRecord.branchInfos[branchKey]
                lastTargetPlatform = lastBuildBranchInfo.targetPlatform
                lastBuildCommitVersion = lastBuildBranchInfo.commitVersion
                printLog("checkBranchBuild branch %s lastTarget %s, currentTarget %s"%(buildBranch, lastTargetPlatform, targetPlatform))                    
            else:
                printLog("checkBranchBuild newBranch %s, currentTarget %s"%(buildBranch, targetPlatform))  
                lastBuildBranchInfo = XCompileRecordBranchInfo()

            if int(commitVersion) > int(lastBuildCommitVersion):
                lastBuildBranchInfo.branchName = buildBranch
                lastBuildBranchInfo.commitVersion = commitVersion
                lastBuildBranchInfo.targetPlatform = targetPlatform
                self.compileRecord.branchInfos[branchKey] = lastBuildBranchInfo
                printLog("checkBranchBuild result : branch {}, lastCommitVersion={},commitVersion={}".format(buildBranch, lastBuildCommitVersion, commitVersion))
                return True
            else:
                printLog("checkBranchBuild error : this branch (%s) commit Version(%s) had already build"%(buildBranch, commitVersion))
                return False
        else:
            printLog("checkBranchBuild error : no this branch %s"%buildBranch)
            return False
        
    def loadCompileRecord(self):
        self.compileRecord = XCompileRecord()
        jsonData = ciTools.readJsonFromFile(self.buildRecordPath)
        if jsonData is not None:
            ciTools.convertDict2Object(jsonData, self.compileRecord)
            branchs =  dict()
            for key in self.compileRecord.branchInfos:
                branchInfo = XCompileRecordBranchInfo()
                data = self.compileRecord.branchInfos[key]
                ciTools.convertDict2Object(data, branchInfo)
                branchs[key] = branchInfo
            self.compileRecord.branchInfos = branchs

    def saveCompileRecord(self):
        jsonData = ciTools.getJsonFromObject(self.compileRecord)
        ciTools.writeJsonToFile(self.buildRecordPath, jsonData)

    def buildProjectWithBranch(self, runParam : XCIProjectCompileParam):
        return buildProject.buildProjectMain(runParam)

    def getBranchCommitVersion(self, projectDir, branch):
        ciTools.resetProjectBranch(projectDir)
        ciTools.switchBranch(projectDir, branch)
        ciTools.pullBranch(projectDir)
        branchInfo = ciTools.getBranchInfo(projectDir, branch)
        return branchInfo

    def prepareBuild(self, buildParam: BuildProjectParam):
        #git
        buildParam.embedGitClean = True
        buildParam.embedPullLatestCode = True
        buildParam.forceResetLocalBranch = True
        buildParam.embedSwitchBranch = True

        #build param
        buildParam.embedProjectCook = False
        buildParam.embedProjectArchive = False
        buildParam.embedProjectDDC = False
        buildParam.embedProjectInstalled = False
        buildParam.embedProjectPak = False
        buildParam.includeCrashReporter = False
        buildParam.includeDebugFiles = False
        buildParam.includePrerequisites = False
        buildParam.embedProjectStage = False
        buildParam.removeDataAfterBuild = True

        #upload
        buildParam.embedUploadSamba = False
        buildParam.includeXCapture = False
        buildParam.embedUploadCos = False
        buildParam.embedSymStore = False

        #baseInfo
        buildParam.executor = "自动脚本执行"


def printLog(msg):
    print("{} -- {}".format(logTag, msg))

def projectCiCompileMain(runParam : XCIProjectCompileParam):
    ubt = ciTools.checkUBTProcess()
    if ubt == True:
        printLog("ubt is running,try later")
    else:
        projectWorker = ProjectCodeBuildWorker(runParam)
        projectWorker.init()
        workResult = projectWorker.startWorker()
        if workResult == 1 and runParam.embedExitWhenBuildFail == False:
            printLog("auto compile project error")
            ciTools.stopProcess("auto compile project error")
        elif workResult == 2:
            printLog("auto compile project:%s no need"%(runParam.projectName))
        elif workResult == 0:
            printLog("auto compile project:%s succ"%(runParam.projectName))
if __name__ == "__main__":
    runParam = XCIProjectCompileParam()
    ciTools.readBuildParams(sys.argv, runParam)
    ciTools.updataParamsAfterUserSetting(runParam)
    if runParam.branch is not None and len(runParam.branch) > 0:
        branchs = runParam.branch.split(";")
        for n in branchs:
            runParam.branchList.append(n)
    ciTools.printObject(runParam, "XCIProjectCompileParam read end")
    if runParam.embedCICompileService == True:
        projectCiCompileMain(runParam)
    else:
        printLog("not need compile project :(embedCICompileService is disable)")