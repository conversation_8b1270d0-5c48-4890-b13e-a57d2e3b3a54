#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import os
import threading
import time
import ciTools
import multiprocessing
from enum import Enum
import cosUtil
import logging
import signal
import shutil
from ciTools import BuildProjectParam
from cosUtil import BuildNotifyMessage
import xverseUtil

logTag = "xverseServiceStat"
def printLog(msg):
    ciTools.printLogTag(logTag, msg)

class ServiceStatConfig:
    def __init__(self):
        self.all = True
        self.embedNotify = False

class ServiceStat:
    def __init__(self):
        self.ubt = False
        self.unreal = False
        self.freeSpace = 0
        self.ciBackService = False

def collectServiceStat(config : ServiceStatConfig, statInfos, stat : ServiceStat):
    #printLog("collectServiceStat {}".format(config.all))
    #ubtStat
    ubtStat = ciTools.checkUBTProcess()
    statInfos["UE-UBTRunning"] = ubtStat
    stat.ubt = ubtStat

    #UE
    unreal = ciTools.checkProcessRunning("UE4", ".exe")
    unreal |= ciTools.checkProcessRunning("Unreal", ".exe")
    statInfos["UE-ProcessRunning"] = unreal
    stat.unreal = unreal

    #free space
    total, used, free = shutil.disk_usage("/")

    #print("Total: %d GiB" % (total // (2**30)))
    #print("Used: %d GiB" % (used // (2**30)))
    #print("Free: %d GiB" % (free // (2**30)))
    freeSpace = "%dGB" % (free // (2**30))
    statInfos["PC-FreeSpace"] = freeSpace
    stat.freeSpace = freeSpace

    #xverseCIProcess
    xciProcess = ciTools.checkProcessRunning("python", ".exe", "xverseCIBackProcess")
    statInfos["X-CiBackProcess"] = xciProcess
    stat.ciBackService = xciProcess

def printServiceStat(config : ServiceStatConfig, statInfos):
    printLog("printServiceStat start all={}".format(config.all))
    for k, v in statInfos.items():
        printLog("{}={}".format(k, v))

if __name__ == "__main__":

    config = ServiceStatConfig()
    # test
    # config.embedNotify = True
    xverseUtil.readParams(sys.argv, config)
    statInfos = {}
    stat = ServiceStat()
    collectServiceStat(config, statInfos, stat)
    printServiceStat(config, statInfos)
    if config.embedNotify == True:
        msg = BuildNotifyMessage()
        msg.buildTime = ciTools.getCurrentDateTimeStr1()

        # test
        # msg.forTest = True

        hasWarn = False
        title = ""
        if stat.ciBackService == False:
            title += "Cos上传服务未运行"
            hasWarn = True
        if hasWarn:
            msg.title = title
            cosUtil.sendWeixinTipsMessage(msg)
