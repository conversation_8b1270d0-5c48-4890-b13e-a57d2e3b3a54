#!/usr/bin/python
# -*- coding: UTF-8 -*-
import sys
import json
import os
import datetime
import shutil
import glob
from types import SimpleNamespace
from collections import namedtuple
import codecs
logTAG = "xverseUtil"

class PluginInfo():
    def __init__(self):
        self.name = None
        self.path = None
        # type -1:inavailabe, 0:default, 1:project,2:engine, 3:engine(user define),9:not found
        self.type = -1
        # item for pluginInfo
        self.pluginDepsForProject = []
        # item for pluginInfo
        self.pluginDepsForEngine = []
        # uplgin config original value
        self.plugins = []

        self.pluginBodyInfo : dict = None

    def __str__(self) -> str:
        return "{},{}".format(self.name, self.path)
    def __repr__(self) -> str:
        return "{},{}".format(self.name, self.path)

class XVerseProjectManager():
    def __init__(self):
        self.enginePath : str = None
        self.projectPath : str = None
        self.searchEnginePlugin = True
        self.projectPluginsPath : str = None
        self.projectPluginDict : PluginInfo = {}
        self.enginePluginDict : PluginInfo = {}
        self.pluginDepsDict : PluginInfo = {}
        self.tempForPluginSearchKey = set()
    def init(self,projectPath, enginePath = None, projectPluginsPath = None, searchEnginePlugin = True):
        self.projectPath = projectPath
        self.enginePath = enginePath
        self.searchEnginePlugin = searchEnginePlugin
        if projectPluginsPath is None:
            self.projectPluginsPath = os.path.join(projectPath, "Plugins")
        else:
            self.projectPluginsPath = projectPluginsPath
        self.loadProjectPlugin()
        self.loadEnginePlugin()
        printLog("XVerseProjectManager init end {}")

    def loadProjectPlugin(self):
        pluginDir = os.path.join(self.projectPath, "Plugins")
        self.loadProjectPluginInner(pluginDir)
        printLog("XVerseProjectManager loadProjectPlugin end {}")

    def loadProjectPluginInner(self, searchDir):
        dirs = os.listdir(searchDir)
        if len(dirs) > 0:
            for fileName in dirs:
                path = os.path.join(searchDir, fileName)
                if os.path.isdir(path):
                    self.loadProjectPluginInner(path)
                elif os.path.isfile(path) and fileName.endswith(".uplugin"):
                    printLog("XVerseProjectManager loadProjectPlugin uplugin {}".format(path))
                    fIndex = fileName.find(".uplugin")
                    pluginName = fileName[:fIndex]
                    plgInfo = PluginInfo()
                    plgInfo.name = pluginName
                    plgInfo.path = searchDir
                    plgInfo.type = 1
                    pjson = json.load(codecs.open(path, 'r', 'utf-8-sig'))
                    plugins = pjson.get("Plugins")
                    plgInfo.plugins = plugins
                    plgInfo.pluginBodyInfo = pjson
                    self.projectPluginDict[pluginName] = plgInfo
    def loadEnginePlugin(self):
        if self.searchEnginePlugin == True and self.enginePath is not None and os.path.exists(self.enginePath):
            pluginDir = os.path.join(self.enginePath, "Engine", "Plugins")
            self.loadEnginePluginInner(pluginDir)
            printLog("XVerseProjectManager loadEnginePlugin end {}")
        else:
            printLog("XVerseProjectManager loadEnginePlugin end no enginePath")

    def loadEnginePluginInner(self, searchDir):
        dirs = os.listdir(searchDir)
        if len(dirs) > 0:
            for fileName in dirs:
                path = os.path.join(searchDir, fileName)
                if os.path.isdir(path):
                    self.loadEnginePluginInner(path)
                elif os.path.isfile(path) and fileName.endswith(".uplugin"):
                    fIndex = fileName.find(".uplugin")
                    pluginName = fileName[:fIndex]
                    pjson = json.load(codecs.open(path, 'r', 'utf-8-sig'))
                    plgInfo = PluginInfo()
                    plgInfo.name = pluginName
                    plgInfo.path = searchDir
                    plgInfo.type = 2
                    plgInfo.pluginBodyInfo = pjson
                    self.enginePluginDict[pluginName] = plgInfo

    def getProjectPluginInfo(self, name):
        return self.projectPluginDict.get(name)

    def getEnginePluginInfo(self, name):
        return self.enginePluginDict.get(name)

    def getPluginDeps(self, pluginName):
        if pluginName in self.pluginDepsDict:
            return self.pluginDepsDict[pluginName]
        item = self.getPluginDepsInner(pluginName)
        self.pluginDepsDict[pluginName] = item
        return item

    def getPluginDepsInner(self, pluginName):
        currentPlg : PluginInfo = self.getProjectPluginInfo(pluginName)
        if currentPlg is None:
            currentPlg : PluginInfo = self.getEnginePluginInfo(pluginName)
            if currentPlg is None:
                currentPlg = PluginInfo()
                currentPlg.type = 3
                currentPlg.name = pluginName
                return currentPlg
            else:
                return currentPlg
        Plugins = currentPlg.plugins
        if Plugins is not None:
            for plg in Plugins:
                depPluginName = plg["Name"]
                childPluginInfo = self.getPluginDepsInner(depPluginName)
                if childPluginInfo.type ==  1:
                    currentPlg.pluginDepsForProject.append(childPluginInfo)
                else:
                    currentPlg.pluginDepsForEngine.append(childPluginInfo)
        return currentPlg


    def getPluginDepsForDict(self, pluginName):
        printLog("XVerseProjectManager getPluginDepsForDict %s"%(pluginName))
        self.tempForPluginSearchKey.clear()
        pluginDeps = self.getPluginDepsForDictInner(pluginName)
        keys = list(pluginDeps.keys())
        printLog("XVerseProjectManager getPluginDepsForDict End %s = %s"%(pluginName, ','.join(keys)))
        return pluginDeps

    def getPluginDepsForDictInner(self, pluginName):
        pluginList = dict()
        pluginIt = self.getPluginDeps(pluginName)
        pluginRet = pluginIt
        pluginList[pluginName] = pluginRet
        projectPlugins = pluginIt.pluginDepsForProject
        if projectPlugins is not None:
            for plgItem in projectPlugins:
                pluginName = plgItem.name
                if not pluginName in self.tempForPluginSearchKey:
                    self.tempForPluginSearchKey.add(pluginName)
                    projPluginList = self.getPluginDepsForDictInner(pluginName)
                    pluginList.update(projPluginList)
        enginePlugins = pluginIt.pluginDepsForEngine
        if enginePlugins is not None:
            for plgItem in enginePlugins:
                pluginName = plgItem.name
                if not pluginName in self.tempForPluginSearchKey:
                    pluginList[pluginName] = plgItem
                    self.tempForPluginSearchKey.add(pluginName)
                # projPluginList = self.getPluginDepsForDict(pluginName)
                # pluginList.update(projPluginList)
        return pluginList

class IniConfigHelper:
    def __init__(self):
        self.content = []
    def read(self, path, encoding="utf-8"):
        if os.path.exists(path):
            f = open(path, "r", encoding=encoding)
            lines = f.readlines() 
            f.close()
            self.content.extend(lines)

    def get(self, section, key):
        foundSection = False
        result = None
        self.loopContent = self.content.copy()
        for line in self.loopContent:
            line = line.replace("\n", "")
            if len(line) < 1:
                continue
            if line.startswith(";"):
                continue
            if line.startswith("#"):
                continue
            if foundSection == True:
                if not line.startswith("["):
                    if line.startswith(key) and line.find("=") == len(key):
                        foundSection = False
                        result = line[len(key) + 1:]
                        break
                    else:
                        continue
                else:
                    foundSection = False
            if foundSection == False:
                sectionIndex = line.find(section)
                if line.startswith("[") and line.endswith("]") and sectionIndex > 0:
                    foundSection = True
                    continue
                else:
                    continue
        return result
    
    def set(self, section, key, value):
        self.loopContent = self.content.copy()
        self.content = []
        existSection = False
        foundSection = False
        endCommit = False
        for line in self.loopContent:
            line : str = line.replace("\n", "")
            if len(line) < 1:
                continue
            if line.startswith(";"):
                self.content.append(line)
                continue
            if line.startswith("#"):
                self.content.append(line)
                continue
            if endCommit:
                self.content.append(line)
                continue
            if foundSection == True:
                if not line.startswith("["):
                    if line.startswith(key) and line.find("=") == len(key):
                        newLine = "{}={}".format(key,value)
                        self.content.append(newLine)
                        endCommit = True
                        continue
                    else:
                        self.content.append(line)
                        continue
                else:
                    # did not found any key
                    newLine = "{}={}".format(key,value)
                    self.content.append(newLine)
                    self.content.append(line)
                    endCommit = True
                    continue
            else:
                sectionIndex = line.find(section)
                if line.startswith("[") and line.endswith("]") and sectionIndex > 0:
                    foundSection = True
                    self.content.append(line)
                    if existSection == False:
                        existSection = True
                    continue
                else:
                    self.content.append(line)
                    continue
        if existSection == False:
            newSection = "[{}]".format(section)
            self.content.append(newSection)
            newLine = "{}={}".format(key,value)
            self.content.append(newLine)
        if existSection == True and foundSection == True and endCommit == False:
            newLine = "{}={}".format(key,value)
            self.content.append(newLine)
    # append for array. mode add or remove, withSign: include + or - prefix key
    def appendList(self, section, key, value, modeWithPlus = True, withSign = True):
        self.loopContent = self.content.copy()
        self.content = []
        foundSection = False
        endSection = False
        endCommit = False
        foundKey = False
        for line in self.loopContent:
            line = line.replace("\n", "")
            if len(line) < 1:
                continue
            if line.startswith(";"):
                self.content.append(line)
                continue
            if line.startswith("#"):
                self.content.append(line)
                continue
            if endCommit == True:
                self.content.append(line)
                continue
            if foundKey == True or (foundSection == True and foundKey == False and endSection):
                    prefixFlag = ""
                    if withSign == True and modeWithPlus == True:
                        prefixFlag = "+"
                    elif withSign == True and modeWithPlus == False:
                        prefixFlag = "-"
                    newLine = "{}{}={}".format(prefixFlag, key, value)
                    self.content.append(newLine)
                    self.content.append(line)
                    endCommit = True
                    continue
            if foundSection == True:
                if not line.startswith("["):
                    realKey = "%s="%key
                    if line.find(realKey) >= 0:
                        self.content.append(line)
                        foundKey = True
                        continue
                    else:
                        self.content.append(line)
                        continue
                else:
                    self.content.append(line)
                    endSection = True
            else:
                sectionIndex = line.find(section)
                if line.startswith("[") and line.endswith("]") and sectionIndex > 0:
                    foundSection = True
                    self.content.append(line)
                    continue
                else:
                    self.content.append(line)
                    continue
        if foundSection == False:
            newSection = f"[{section}]"
            self.content.append(newSection)
            prefixFlag = ""
            if withSign == True and modeWithPlus == True:
                prefixFlag = "+"
            elif withSign == True and modeWithPlus == False:
                prefixFlag = "-"
            newLine = "{}{}={}".format(prefixFlag, key, value)
            self.content.append(newLine)
        if foundSection == True and foundKey == False and endSection == False:
            prefixFlag = ""
            if withSign == True and modeWithPlus == True:
                prefixFlag = "+"
            elif withSign == True and modeWithPlus == False:
                prefixFlag = "-"
            newLine = "{}{}={}".format(prefixFlag, key, value)
            self.content.append(newLine)

    def remove(self, section, key):
        foundSection = False
        self.loopContent = self.content.copy()
        self.content = []
        sectionEnd = False
        for line in self.loopContent:
            line = line.replace("\n", "")
            if len(line) < 1:
                continue
            if line.startswith(";"):
                self.content.append(line)
                continue
            if line.startswith("#"):
                self.content.append(line)
                continue
            if sectionEnd == True:
                self.content.append(line)
                continue
            if foundSection == True:
                if not line.startswith("["):
                    if line.startswith(key) and line.find("=") == len(key):
                        foundSection = False
                        sectionEnd = True
                        continue
                    else:
                        self.content.append(line)
                        continue
                else:
                    self.content.append(line)
                    foundSection = False
                    sectionEnd = True
            if foundSection == False:
                sectionIndex = line.find(section)
                if line.startswith("[") and line.endswith("]") and sectionIndex > 0:
                    foundSection = True
                    self.content.append(line)
                    continue
                else:
                    self.content.append(line)
                    continue

    def append(self, secondIniFile : str):
        f = open(secondIniFile, "r", encoding="utf-8")
        secondContent = f.readlines()
        for line in secondContent:
            line = line.replace("\n", "")
            if len(line) < 1:
                continue
            self.content.append(line)

    def write(self, path, encoding="utf-8"):
        contents = ""
        for line in self.content:
            contents += line
            contents += "\n"
        f = open(path, "w", encoding=encoding)
        f.write(contents)
        f.close()

class XTextReplaceHelper:
    def __init__(self):
        self.content = []
    def read(self, path, encoding="utf-8"):
        if os.path.exists(path):
            f = open(path, "r", encoding=encoding)
            self.content = f.readlines() 
            f.close()
    def replaceProjectName(self, projectName):
        nameReplaceMent = "${XProjectTemplateName}"
        self.replace(nameReplaceMent, projectName)
    def replace(self, replaceText, newContent, onlyLine = False):
        hasReplaced = False
        self.loopContent = self.content.copy()
        self.content = []
        for line in self.loopContent:
            line = str(line.replace("\n", ""))
            if len(line) < 1:
                continue
            if line.startswith(";") or line.startswith("#") or line.startswith("//"):
                self.content.append(line)
                continue
            elif line.startswith("/*") or line.startswith("*/") or line.startswith("*"):
                self.content.append(line)
                continue
            if line.find(replaceText) >= 0:
                if onlyLine == True and hasReplaced:
                    self.content.append(line)
                else:
                    hasReplaced = True
                    newLine = line.replace(replaceText, newContent)
                    self.content.append(newLine)
            else:
                self.content.append(line)

        return hasReplaced

    def keepCondition(self, keep = False):
        keepConditionKey = "${XProjectTemplateKeepCondition}$"
        self.loopContent = self.content.copy()
        self.content = []
        hasFound = False
        foundCount = 0
        for line in self.loopContent:
            line = str(line.replace("\n", ""))
            if len(line) < 1:
                continue
            if line.startswith(";") or line.startswith("#") or line.startswith("//"):
                self.content.append(line)
                continue
            elif line.startswith("/*") or line.startswith("*/") or line.startswith("*"):
                self.content.append(line)
                continue
            if hasFound == True:
                if line.find(keepConditionKey) >= 0:
                    hasFound = False
                    foundCount += 1
                else:
                    if keep == True:
                        self.content.append(line)
                    else:
                        continue
            else:
                if line.find(keepConditionKey) >= 0:
                    hasFound = True
                else:
                    self.content.append(line)
    def write(self, path, encoding="utf-8"):
        contents = ""
        for line in self.content:
            contents += line
            contents += "\n"
        f = open(path, "w", encoding=encoding)
        f.write(contents)
        f.close()

class UProjectHelper():
    def __init__(self):
        self.content = {}
    def read(self, path, encoding="utf-8"):
        if os.path.exists(path):
            self.content = json.load(codecs.open(path, 'r', encoding))
        else:
            print("read error file not exist {}".format(path))
            self.content["Plugins"] = []

    def getEnablePlugins(self):
        return self.getPlugins(True)

    def getDisablePlugins(self):
        return self.getPlugins(False)

    def getPlugins(self, withEnable = True):
        if "Plugins" in self.content:
            plugins = self.content.get("Plugins")
            if plugins is not None and len(plugins) > 0:
                retPlugins = []
                for plgItem in plugins:
                    enable = plgItem["Enabled"]
                    pluginName = plgItem.get("Name")
                    if enable == withEnable:
                        retPlugins.append(pluginName)
                return retPlugins
        return None

    def addPlugin(self, pluginName, withEnable = False):
        item = {}
        item["Name"] = pluginName
        item["Enabled"] = withEnable 
        self.addPluginItem(item)

    def addPluginItem(self, item : dict):
        if not "Plugins" in self.content:
            plugins = []
            self.content["Plugins"] = plugins
        plugins = self.content.get("Plugins")
        existPlgun = False
        pluginName = item.get("Name")
        pluginIndex = -1
        for plg in plugins:
            pluginIndex = pluginIndex + 1
            pName = plg.get("Name")
            if pName == pluginName:
                existPlgun = True
                plg["Enabled"] = item["Enabled"]

                if "MarketplaceURL" in item:
                    plg["MarketplaceURL"] = item["MarketplaceURL"]

                if "SupportedTargetPlatforms" in item:
                    plg["SupportedTargetPlatforms"] = item["SupportedTargetPlatforms"]
                break
        if existPlgun == False:
            plugins.append(item)
    
    def joinPlugins(self, plugins = None, withEnable = True):
        if plugins is not None:
            for name in plugins:
                self.addPlugin(name, withEnable)

    def joinPluginItemList(self, plugins = None):
    
        if plugins is not None:
            for plgItem in plugins:
                self.addPluginItem(plgItem)

    def set(self, key, value):
        self.content[key] = value


    def setGenAuthor(self, value):
        self.content["XGenAuthor"] = value

    def setGenChannel(self, value):
        self.content["XGenChanel"] = value

    def write(self, path, encoding="utf-8"):
        json_object = json.dumps(self.content, indent=4)
        f = open(path, "w", encoding=encoding)
        f.write(json_object)
        f.close()

def copyFileByShutil(srcFile, destFile):
    if os.path.exists(srcFile):
        shutil.copyfile(srcFile, destFile)
    else:
        printLog("copyFileByShutil error {} not exist".format(srcFile))

def copyDirByShutil(dir1, dir2):
    printLog("copyDirByShutil src={}, dest={}".format(dir1, dir2))
    if os.path.exists(dir1):
        shutil.copytree(dir1, dir2, dirs_exist_ok = True)


def printLog(msg):
    printLogTag(logTAG, msg)

def printLogTag(tag, msg):
    cdate = datetime.datetime.now()
    date_time = cdate.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    log = "{} {} -- {}".format(date_time, tag, msg)
    print(log, flush=True)

def readParams(argv, buildParam : object):
    attr = [a for a in dir(buildParam) if not a.startswith('__')]
    for a in attr:
        key = "-{0}".format(a)
        if not existParam(argv, key):
            continue
        pt = getattr(buildParam, a)
        tp = type(pt)
        val = None
        if tp == int:
            val = getIntParam(argv, key)
        elif tp == bool:
            val = getBoolParam(argv, key)
        else:
            val = getParam(argv, key)
        if val is not None:
            setattr(buildParam, a, val)

def getParam(argv, key):
    value = None
    for item in argv:
        if item.startswith(key):
            value = item[len(key) + 1:]
            break 
    return value

def getStringParam(argv, key):
    return getParam(argv, key)

def getIntParam(argv, key):
    value = getStringParam(argv, key)
    if value is not None and len(value) > 0:
        return int(value)
    return 0

def getBoolParam(argv, key):
    value = None
    for item in argv:
        if item == key:
            value = "true"
        elif item.startswith(key):
            value = item[len(key) + 1:]
            break
    if value is not None and len(value) > 0:
        return value.lower() in ("yes", "true", "t", "1")
    return False

def existParam(argv, key):
    exist = False
    for item in argv:
        if item.startswith(key):
            exist = True
            break 
    return exist

def getFileName(path):
    return os.path.basename(path)

def removeFiles(path):
    files = glob.glob(path)
    for file in files:
        os.remove(file)

def removeDir(dir):
    if os.path.exists(dir):
        shutil.rmtree(dir,ignore_errors=False, onerror=None)


# plugin deps search begin

#plugin deps search end

def testSymStore():
    symexe = "SymStore.exe"
    f = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2318.tmp"
    #f = "D:\\symbol.txt"
    f = "D:\\tmp2318.tmp"
    s = "C:\\work\\xverse\\UnrealEngine5.2\\UnrealEngine\\Saved\\SymStore5"
    t = "XEngine5"
    param = "add /f @{} /s {} /t {}".format(f, s, t)
    os.chdir("C:\\Program Files (x86)\\Windows Kits\\10\\Debuggers\\x64")
    cmd = "{} {}".format(symexe, param)
    printLog(cmd)
    ret = os.system(cmd)
    print(ret)
if __name__ == "__main__":
    currPath = os.getcwd()
    printLog("currPath " + currPath)
    projectPath = "C:\\work\\xverse\\XVerseStudio"
    enginePath = "C:\\work\\xverse\\EngineOutput\\XVerseEngine-20230911-195520"
    testSymStore()
    #projectMgr = XVerseProjectManager()
    #projectMgr.init(projectPath, enginePath, searchEnginePlugin=False)
    #currentPlg = projectMgr.getPluginDepsForDict("XUE")
    #printLog("currentPluginInfo {}".format(len(currentPlg)))
