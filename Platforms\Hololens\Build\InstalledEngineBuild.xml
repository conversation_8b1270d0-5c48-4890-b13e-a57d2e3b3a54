<?xml version='1.0' ?>
<BuildGraph xmlns="http://www.epicgames.com/BuildGraph" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.epicgames.com/BuildGraph ../../../../Engine/Build/Graph/Schema.xsd" >

	<Property Name="DefaultWithHoloLens" Value="$(DefaultWithPlatform)"/>
	<Property Name="DefaultWithHoloLens" Value="false" If="'$(HostPlatform)' != 'Win64' And !$(AllPlatforms)"/>

	<Option Name="WithHoloLens" Restrict="true|false" DefaultValue="$(DefaultWithHoloLens)" Description="Include the HoloLens target platform"/>

	<!-- Hololens Architectures -->
	<Property Name="HoloLensArchitectures" Value="x64+arm64"/>
	<Property Name="DefaultArchitectureCommandsWin64" Value="$(DefaultArchitectureCommandsWin64)  -HoloLensArchitectures=$(HoloLensArchitectures)"/>

	<!-- platform extensions -->
	<Property Name="ExtensionPlatforms" Value="$(ExtensionPlatforms);" If="'$(ExtensionPlatforms)' != ''"/>
	<Property Name="ExtensionPlatforms" Value="$(ExtensionPlatforms)HoloLens"/>

	<Do If="$(WithHoloLens)">
		<Expand Name="AddExtensionPlatform" PlatformDir="HoloLens"/>
	</Do>

	<Extend Name="Platform_FilterRestrictedFolders">
		<Do If="$(WithHoloLens)">
		</Do>
	</Extend>

	<Extend Name="Platform_FilterOverrides">
		<Do If="$(WithHoloLens)">
			<!-- Update CopyEditorEngineOrPlatformFilter, AutomationToolPaths, etc -->
		</Do>
	</Extend>

	<Extend Name="Platform_Agent_Target_Win64_Licensee">
		<Agent Name="Target HoloLens" Type="Win64_Licensee">
			<Node Name="Compile UnrealGame HoloLens" Requires="Update Version Files" Produces="#UnrealGame HoloLens;#UnrealGame HoloLens Unstripped;#UnrealGame HoloLens Stripped;#UnrealGame HoloLens Unsigned;#UnrealGame HoloLens Signed">
				<ForEach Name="Configuration" Values="$(GameConfigurations)">
					<Compile Target="UnrealGame" Platform="HoloLens" Configuration="$(Configuration)" Tag="#UnrealGame HoloLens" Arguments="-precompile -allmodules -nolink -architecture=arm64 $(TargetDebugInfoArg) $(VS2019Arg)"/>
					<Compile Target="UnrealGame" Platform="HoloLens" Configuration="$(Configuration)" Tag="#UnrealGame HoloLens" Arguments="-precompile -architecture=arm64 $(TargetDebugInfoArg) $(VS2019Arg)" Clean="false"/>
					<Compile Target="UnrealGame" Platform="HoloLens" Configuration="$(Configuration)" Tag="#UnrealGame HoloLens" Arguments="-precompile -allmodules -nolink -architecture=x64 $(TargetDebugInfoArg) $(VS2019Arg)"/>
					<Compile Target="UnrealGame" Platform="HoloLens" Configuration="$(Configuration)" Tag="#UnrealGame HoloLens" Arguments="-precompile -architecture=x64 $(TargetDebugInfoArg) $(VS2019Arg)" Clean="false"/>
				</ForEach>

				<Switch>
					<Case If="'$(WithFullDebugInfo)' == true">
						<!-- Copy files for local debug builds -->
						<Copy Files="#UnrealGame HoloLens Unstripped" From="$(RootDir)" To="$(SavedOutput)"/>
					</Case>
					<Default>
						<!-- Otherwise, strip the files for distribution -->
						<Strip BaseDir="$(RootDir)" OutputDir="$(SavedOutput)" Platform="Win64" Files="#UnrealGame HoloLens Unstripped" Tag="#UnrealGame HoloLens Stripped"/>
					</Default>
				</Switch>

				<Do If="'$(SignExecutables)' == true">
					<Tag Files="#UnrealGame HoloLens" Filter="$(WindowsSignFilter)" With="#UnrealGame HoloLens Unsigned"/>
					<Copy Files="#UnrealGame HoloLens Unsigned" From="$(RootDir)" To="$(SavedOutput)" Tag="#Game_ToSign_HoloLens"/>
					<Sign Files="#Game_ToSign_HoloLens" Tag="#UnrealGame HoloLens Signed"/>
				</Do>
			</Node>
		</Agent>
		<Aggregate Name="TargetPlatforms_HoloLens" Label="Platforms/HoloLens" Requires="Compile UnrealGame HoloLens"/>
	</Extend>

	<Extend Name="Platform_AddInstalledRequirements">
		<Do If="$(WithHoloLens)">
			<Property Name="InstalledRequirements" Value="$(InstalledRequirements);#UnrealGame HoloLens;#UnrealGame HoloLens Unstripped;#UnrealGame HoloLens Stripped;#UnrealGame HoloLens Unsigned;#UnrealGame HoloLens Signed"/>
		</Do>
	</Extend>

	<Extend Name="Platform_TagCsToolsRedistUATSource">
		<Do If="$(WithHoloLens)">
		</Do>
	</Extend>

	<Extend Name="Platform_Build_Tools_CS_Binaries">
		<Do If="$(WithHoloLens)">
			<!-- Compile platform tools -->
		</Do>
	</Extend>

	<Extend Name="Platform_Append_DDCPlatformsWin64">
		<Do If="$(WithHoloLens)">
			<Property Name="DDCPlatformsWin64" Value="$(DDCPlatformsWin64)+HoloLens"/>
		</Do>
	</Extend>

	<Extend Name="Platform_TagDDCFiles">
		<Do If="$(WithHoloLens)">
			<!-- Copy utilities/tools -->
			<Tag Files="Engine\Platforms\HoloLens\Binaries\DotNET\AutomationScripts\..." With="#ToCopy"/>
		</Do>
	</Extend>

	<Extend Name="Platform_ExtraSignFiles">
		<Do If="$(WithHoloLens)">
			<Property Name="ExtraSignFilesHoloLens">
				Engine\Platforms\Hololens\Binaries\DotNET\AutomationTool\AutomationScripts\...
			</Property>
			<Property Name="ExtraSignFiles" Value="$(ExtraSignFiles);$(ExtraSignFilesHoloLens)"/>
		</Do>
	</Extend>

	<Extend Name="Platform_Tag_UnrealGame">
		<Do If="$(WithHoloLens)">
			<Property Name="CopyHoloLensFilter">
				<!-- Build Files -->
				Engine\Platforms\HoloLens\Binaries\...
				Engine\Platforms\HoloLens\Build\...
				Engine\Platforms\HoloLens\Content\Editor\...
				Engine\Build\HoloLens\...

				<!-- Additional Binaries -->
				Engine\Platforms\HoloLens\Binaries\DotNET\AutomationScripts\...
			</Property>

			<Property Name="CopyHoloLensExceptions">
			</Property>
			<Tag Files="#UnrealGame HoloLens" Except="#UnrealGame HoloLens Unstripped;#UnrealGame HoloLens Unsigned" With="#Installed Win64"/>
			<Tag Files="#UnrealGame HoloLens Stripped;#UnrealGame HoloLens Signed" With="#Saved Output"/>
			<Property Name="Platforms" Value="$(Platforms)HoloLens;"/>
			<Property Name="CopyInstalledFilter" Value="$(CopyInstalledFilter);$(CopyHoloLensFilter)"/>
			<Property Name="CopyInstalledExceptions" Value="$(CopyInstalledExceptions);$(CopyHoloLensExceptions)"/>
		</Do>
		<Do If="!$(WithHoloLens)">
			<Property Name="CopyHoloLensCsToolsExceptions">
				Engine\Saved\CsTools\Engine\Intermediate\ScriptModules\HoloLens.Automation.json
			</Property>
			<Property Name="CopyWin64CsToolsExceptions" Value="$(CopyWin64CsToolsExceptions);$(CopyHoloLensCsToolsExceptions)"/>
		</Do>
	</Extend>

</BuildGraph>
