import datetime
from dateutil.relativedelta import relativedelta

def get_build_version():
    # 获取当前日期和时间
    now = datetime.datetime.now()

    # 提取年、月、日
    year = now.year
    month = now.month
    day = now.day
    hour = now.hour
    minute = now.minute
    second = now.second

    # 获取离公司成立已经过去的时间
    start_time = datetime.datetime(2021, 3, 11, 10, 0, 0)
    delta = now - start_time
    print(int(delta.total_seconds() / 60+delta.seconds))
    return f"{year}.{month}{day}{hour}.{minute}{second}"

get_build_version()